# =====================
# ЭТАП 1: Сборка
# =====================
FROM node:20 AS builder

# Устанавливаем рабочую директорию внутри контейнера
WORKDIR /usr/src/app

# 1. Копируем package.json и package-lock.json
COPY package*.json ./

# 2. Устанавливаем все зависимости (dev и prod)
RUN npm install

# 3. Копируем весь исходный код (включая src, prisma, tsconfig.json и т.д.)
COPY . .

# 4. Генерация Prisma Client (если используется)
RUN npx prisma generate --schema=prisma/schema.prisma

# 5. Выполняем сборку TypeScript (создаётся папка dist)
RUN npm run build

# =====================
# ЭТАП 2: Production
# =====================
FROM node:20 AS production

# Устанавливаем рабочую директорию
WORKDIR /usr/src/app

# 1. Копируем только dist и node_modules из builder-этапа
COPY --from=builder /usr/src/app/dist ./dist
COPY --from=builder /usr/src/app/node_modules ./node_modules
COPY --from=builder /usr/src/app/package*.json ./

# 2. Создаем папку uploads и копируем её структуру
RUN mkdir -p uploads/calendars uploads/menus uploads/schedules uploads/reports
COPY --from=builder /usr/src/app/uploads ./uploads

# 3. Указываем порт
EXPOSE 3002

# 4. Запускаем приложение
CMD ["npm", "run", "start:prod"]
