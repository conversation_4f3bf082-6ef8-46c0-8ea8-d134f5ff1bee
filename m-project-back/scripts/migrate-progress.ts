import { PrismaClient } from '@prisma/client';

// Инициализация Prisma клиента
const prisma = new PrismaClient();

// Карта соответствия старых и новых ID навыков
const idMappings = {
  // Английский язык (категория изменилась с ID '6' на ID '12')
  '6_1': '12_1', // Устная речь
  '6_1_1': '12_1_1', // Контакт с педагогом
  '6_1_2': '12_1_2', // Рутинные беседы
  '6_1_3': '12_1_3', // Ответы на вопросы
  '6_1_4': '12_1_4', // Развернуто отвечает на вопросы
  '6_1_5': '12_1_5', // Говорит предложениями 3-5 слов
  '6_1_6': '12_1_6', // Короткие беседы
  '6_1_7': '12_1_7', // Фразы для уборки класса
  '6_1_8': '12_1_8', // Начинающие: кубики с алфавитом
  '6_1_9': '12_1_9', // Начинающие: плакат с алфавитом
  '6_1_10': '12_1_10', // Классификационные карточки
  '6_1_10_1': '12_1_10_1', // Фрукты
  '6_1_10_2': '12_1_10_2', // Овощи
  '6_1_10_3': '12_1_10_3', // Домашние животные
  '6_1_10_4': '12_1_10_4', // Птицы
  '6_1_10_5': '12_1_10_5', // Грибы
  '6_1_10_6': '12_1_10_6', // Яблоки
  '6_1_10_7': '12_1_10_7', // Живое и неживое
  '6_1_10_8': '12_1_10_8', // Электроприборы
  '6_1_10_9': '12_1_10_9', // Части тела
  '6_1_10_10': '12_1_10_10', // Дерево
  '6_1_10_11': '12_1_10_11', // Лист
  '6_1_10_12': '12_1_10_12', // Цветок
  '6_1_11': '12_1_11', // Карточки пазлы алфавит и первые слова
  '6_1_12': '12_1_12', // Цвета (карточки)
  '6_1_13': '12_1_13', // Счёт (карточки)
  '6_1_14': '12_1_14', // Формы (карточки)
  '6_1_15': '12_1_15', // Розовая серия языковых материалов
  '6_1_15_1': '12_1_15_1', // Коробка с предметами 1
  '6_1_15_2': '12_1_15_2', // Коробка с предметами 2
  '6_1_15_3': '12_1_15_3', // Упражнения с фонетическими словами CVC
  '6_1_15_4': '12_1_15_4', // Список слов для чтения
  '6_1_15_5': '12_1_15_5', // Фразы для чтения
  '6_1_15_6': '12_1_15_6', // Предложения для чтения
  '6_1_15_7': '12_1_15_7', // Частоупотребимые слова-головоломки
  '6_1_16': '12_1_16', // Голубая серия языковых материалов
  '6_1_16_1': '12_1_16_1', // Коробка с предметами 1
  '6_1_16_2': '12_1_16_2', // Список слов для чтения
  '6_1_16_3': '12_1_16_3', // Фразы для чтения
  '6_1_16_4': '12_1_16_4', // Предложения для чтения
  '6_1_17': '12_1_17', // Зеленая серия языковых материалов
  '6_1_17_1': '12_1_17_1', // Коробка с предметами 1
  '6_1_17_2': '12_1_17_2', // Семейства фонограмм
  '6_1_17_3': '12_1_17_3', // Список слов для чтения
  '6_1_17_4': '12_1_17_4', // Фразы для чтения
  '6_1_17_5': '12_1_17_5', // Предложения для чтения
  '6_1_18': '12_1_18', // Нравится и не нравится
  '6_1_19': '12_1_19', // Могу и не могу
  '6_1_20': '12_1_20', // Карточки пазлы – слова из букв
  '6_1_21': '12_1_21', // Карточки пазлы – фразы
  '6_1_22': '12_1_22', // Карточки пазлы – действия, предложения
  '6_1_23': '12_1_23', // Называет предметы вокруг
  '6_1_24': '12_1_24', // Описание предметов вокруг
  '6_1_25': '12_1_25', // Называет и описывает предметы c изображений
  '6_1_26': '12_1_26', // Стихи, пальчиковые игры, песни
  '6_2': '12_2', // Письменная речь
  '6_2_1': '12_2_1', // Звуковые игры
  '6_2_2': '12_2_2', // Шершавые буквы
  '6_2_3': '12_2_3', // Подвижный алфавит
  '6_2_4': '12_2_4', // Изучение прописных букв
  '6_2_5': '12_2_5', // Прописи
  '6_2_6': '12_2_6', // Письмо на бумаге
  '6_2_7': '12_2_7', // Переписывание текстов
  '6_2_8': '12_2_8', // Письмо на песке
  '6_2_9': '12_2_9', // Письмо на доске
  '6_3': '12_3', // Чтение
  '6_3_1': '12_3_1', // Чтение ребенком фонетических слов
  '6_3_2': '12_3_2', // Чтение книг с обсуждением картинок
  '6_3_3': '12_3_3', // Шершавые дифтонги
  '6_3_4': '12_3_4', // Чтение книг педагогом
  '6_3_5': '12_3_5', // Способен следить за сюжетом длинного рассказа
  
  // Добавьте сюда другие изменения ID, если они были
};

// Функция для миграции данных
async function migrateProgress() {
  console.log('Начинаем миграцию данных прогресса...');
  
  try {
    // Получаем все записи прогресса
    const allProgress = await prisma.progress.findMany();
    console.log(`Найдено ${allProgress.length} записей прогресса`);
    
    // Счетчики для статистики
    let updatedCount = 0;
    let skippedCount = 0;
    
    // Обрабатываем каждую запись
    for (const progress of allProgress) {
      // Проверяем, есть ли skillId в карте соответствия
      if (idMappings[progress.skillId]) {
        // Обновляем запись с новым skillId
        await prisma.progress.update({
          where: { id: progress.id },
          data: { skillId: idMappings[progress.skillId] }
        });
        updatedCount++;
        console.log(`Обновлена запись: ${progress.id}, skillId: ${progress.skillId} -> ${idMappings[progress.skillId]}`);
      } else {
        skippedCount++;
      }
    }
    
    console.log(`Миграция завершена. Обновлено: ${updatedCount}, пропущено: ${skippedCount}`);
  } catch (error) {
    console.error('Ошибка при миграции данных:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Запускаем миграцию
migrateProgress()
  .catch(e => {
    console.error(e);
    process.exit(1);
  });
