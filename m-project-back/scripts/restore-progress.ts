import { PrismaClient } from '@prisma/client';
import * as fs from 'fs';
import * as path from 'path';
import * as readline from 'readline';

// Инициализация Prisma клиента
const prisma = new PrismaClient();

// Функция для чтения ввода пользователя
async function askQuestion(query: string): Promise<string> {
  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout,
  });

  return new Promise((resolve) => {
    rl.question(query, (answer) => {
      rl.close();
      resolve(answer);
    });
  });
}

// Функция для восстановления данных прогресса из резервной копии
async function restoreProgress() {
  console.log('Начинаем восстановление данных прогресса...');
  
  try {
    // Проверяем наличие директории с резервными копиями
    const backupDir = path.join(__dirname, '../backups');
    if (!fs.existsSync(backupDir)) {
      console.error('Директория с резервными копиями не найдена');
      return;
    }
    
    // Получаем список файлов резервных копий
    const backupFiles = fs.readdirSync(backupDir)
      .filter(file => file.startsWith('progress-backup-') && file.endsWith('.json'))
      .sort()
      .reverse(); // Сортируем в обратном порядке, чтобы самые новые были первыми
    
    if (backupFiles.length === 0) {
      console.error('Резервные копии не найдены');
      return;
    }
    
    // Выводим список доступных резервных копий
    console.log('Доступные резервные копии:');
    backupFiles.forEach((file, index) => {
      console.log(`${index + 1}. ${file}`);
    });
    
    // Запрашиваем у пользователя номер файла для восстановления
    const fileIndex = parseInt(await askQuestion('Введите номер файла для восстановления: ')) - 1;
    
    if (isNaN(fileIndex) || fileIndex < 0 || fileIndex >= backupFiles.length) {
      console.error('Некорректный номер файла');
      return;
    }
    
    const selectedFile = backupFiles[fileIndex];
    const backupFilePath = path.join(backupDir, selectedFile);
    
    // Читаем данные из файла
    const backupData = JSON.parse(fs.readFileSync(backupFilePath, 'utf8'));
    
    // Запрашиваем подтверждение
    const confirmation = await askQuestion(`Вы уверены, что хотите восстановить ${backupData.length} записей из файла ${selectedFile}? (y/n): `);
    
    if (confirmation.toLowerCase() !== 'y') {
      console.log('Восстановление отменено');
      return;
    }
    
    // Удаляем все текущие записи прогресса
    const deleteResult = await prisma.progress.deleteMany({});
    console.log(`Удалено ${deleteResult.count} текущих записей прогресса`);
    
    // Восстанавливаем записи из резервной копии
    let restoredCount = 0;
    
    for (const progress of backupData) {
      await prisma.progress.create({
        data: {
          id: progress.id,
          childId: progress.childId,
          skillId: progress.skillId,
          status: progress.status,
          updatedAt: new Date(progress.updatedAt)
        }
      });
      restoredCount++;
    }
    
    console.log(`Восстановлено ${restoredCount} записей прогресса`);
  } catch (error) {
    console.error('Ошибка при восстановлении данных:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Запускаем восстановление
restoreProgress()
  .catch(e => {
    console.error(e);
    process.exit(1);
  });
