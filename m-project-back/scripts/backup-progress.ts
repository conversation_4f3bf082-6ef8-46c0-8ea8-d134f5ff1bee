import { PrismaClient } from '@prisma/client';
import * as fs from 'fs';
import * as path from 'path';

// Инициализация Prisma клиента
const prisma = new PrismaClient();

// Функция для создания резервной копии данных прогресса
async function backupProgress() {
  console.log('Начинаем создание резервной копии данных прогресса...');
  
  try {
    // Получаем все записи прогресса
    const allProgress = await prisma.progress.findMany();
    console.log(`Найдено ${allProgress.length} записей прогресса`);
    
    // Создаем директорию для резервных копий, если она не существует
    const backupDir = path.join(__dirname, '../backups');
    if (!fs.existsSync(backupDir)) {
      fs.mkdirSync(backupDir, { recursive: true });
    }
    
    // Создаем имя файла с текущей датой и временем
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const backupFile = path.join(backupDir, `progress-backup-${timestamp}.json`);
    
    // Записываем данные в файл
    fs.writeFileSync(backupFile, JSON.stringify(allProgress, null, 2));
    
    console.log(`Резервная копия создана: ${backupFile}`);
  } catch (error) {
    console.error('Ошибка при создании резервной копии:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Запускаем создание резервной копии
backupProgress()
  .catch(e => {
    console.error(e);
    process.exit(1);
  });
