import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function main() {
  await prisma.child.deleteMany({});

  const children = [
    {
      name: 'Егоров Никита',
      dateOfBirth: new Date(),
      groupId: '16460324-ef8f-4d5d-8a23-5b2e54743290',
      schoolId: '69c6b490-0aa7-455b-953d-b3787308fa7d',
      parentIds: [],
      photoUrl: '',
    },
    {
      name: 'Сысо<PERSON>я<PERSON>ин Макар',
      dateOfBirth: new Date(),
      groupId: '16460324-ef8f-4d5d-8a23-5b2e54743290',
      schoolId: '69c6b490-0aa7-455b-953d-b3787308fa7d',
      parentIds: [],
      photoUrl: '',
    },
    {
      name: '<PERSON><PERSON><PERSON><PERSON> Мирон',
      dateOfBirth: new Date(),
      groupId: '16460324-ef8f-4d5d-8a23-5b2e54743290',
      schoolId: '69c6b490-0aa7-455b-953d-b3787308fa7d',
      parentIds: [],
      photoUrl: '',
    },
    {
      name: 'Тимошук Майя',
      dateOfBirth: new Date(),
      groupId: '16460324-ef8f-4d5d-8a23-5b2e54743290',
      schoolId: '69c6b490-0aa7-455b-953d-b3787308fa7d',
      parentIds: [],
      photoUrl: '',
    },
    {
      name: 'Калашникова Стелла',
      dateOfBirth: new Date(),
      groupId: '16460324-ef8f-4d5d-8a23-5b2e54743290',
      schoolId: '69c6b490-0aa7-455b-953d-b3787308fa7d',
      parentIds: [],
      photoUrl: '',
    },
    {
      name: 'Агапова Полина',
      dateOfBirth: new Date(),
      groupId: '16460324-ef8f-4d5d-8a23-5b2e54743290',
      schoolId: '69c6b490-0aa7-455b-953d-b3787308fa7d',
      parentIds: [],
      photoUrl: '',
    },
    {
      name: 'Вахтина Паулина',
      dateOfBirth: new Date(),
      groupId: '16460324-ef8f-4d5d-8a23-5b2e54743290',
      schoolId: '69c6b490-0aa7-455b-953d-b3787308fa7d',
      parentIds: [],
      photoUrl: '',
    },
    {
      name: 'Холковская Мира',
      dateOfBirth: new Date(),
      groupId: '16460324-ef8f-4d5d-8a23-5b2e54743290',
      schoolId: '69c6b490-0aa7-455b-953d-b3787308fa7d',
      parentIds: [],
      photoUrl: '',
    },
    {
      name: 'Бриленков Арсений',
      dateOfBirth: new Date(),
      groupId: '16460324-ef8f-4d5d-8a23-5b2e54743290',
      schoolId: '69c6b490-0aa7-455b-953d-b3787308fa7d',
      parentIds: [],
      photoUrl: '',
    },
    {
      name: 'Ковалев Богдан',
      dateOfBirth: new Date(),
      groupId: '16460324-ef8f-4d5d-8a23-5b2e54743290',
      schoolId: '69c6b490-0aa7-455b-953d-b3787308fa7d',
      parentIds: [],
      photoUrl: '',
    },
    {
      name: 'Милкова Мия',
      dateOfBirth: new Date(),
      groupId: '16460324-ef8f-4d5d-8a23-5b2e54743290',
      schoolId: '69c6b490-0aa7-455b-953d-b3787308fa7d',
      parentIds: [],
      photoUrl: '',
    },
    {
      name: 'Бороздунов Влад',
      dateOfBirth: new Date(),
      groupId: '16460324-ef8f-4d5d-8a23-5b2e54743290',
      schoolId: '69c6b490-0aa7-455b-953d-b3787308fa7d',
      parentIds: [],
      photoUrl: '',
    },
    {
      name: 'Копытов Матвей',
      dateOfBirth: new Date(),
      groupId: '16460324-ef8f-4d5d-8a23-5b2e54743290',
      schoolId: '69c6b490-0aa7-455b-953d-b3787308fa7d',
      parentIds: [],
      photoUrl: '',
    },
    {
      name: 'Ченгина Вероника',
      dateOfBirth: new Date(),
      groupId: '16460324-ef8f-4d5d-8a23-5b2e54743290',
      schoolId: '69c6b490-0aa7-455b-953d-b3787308fa7d',
      parentIds: [],
      photoUrl: '',
    },
    {
      name: 'Ляшко Лева',
      dateOfBirth: new Date(),
      groupId: '16460324-ef8f-4d5d-8a23-5b2e54743290',
      schoolId: '69c6b490-0aa7-455b-953d-b3787308fa7d',
      parentIds: [],
      photoUrl: '',
    },
    {
      name: 'Новиков Матвей',
      dateOfBirth: new Date(),
      groupId: '16460324-ef8f-4d5d-8a23-5b2e54743290',
      schoolId: '69c6b490-0aa7-455b-953d-b3787308fa7d',
      parentIds: [],
      photoUrl: '',
    },
    {
      name: 'Шевелева Диана',
      dateOfBirth: new Date(),
      groupId: '16460324-ef8f-4d5d-8a23-5b2e54743290',
      schoolId: '69c6b490-0aa7-455b-953d-b3787308fa7d',
      parentIds: [],
      photoUrl: '',
    },
    {
      name: 'Ляпин Мирослав',
      dateOfBirth: new Date(),
      groupId: '16460324-ef8f-4d5d-8a23-5b2e54743290',
      schoolId: '69c6b490-0aa7-455b-953d-b3787308fa7d',
      parentIds: [],
      photoUrl: '',
    },
    {
      name: 'Кудрявский Арсений',
      dateOfBirth: new Date(),
      groupId: '16460324-ef8f-4d5d-8a23-5b2e54743290',
      schoolId: '69c6b490-0aa7-455b-953d-b3787308fa7d',
      parentIds: [],
      photoUrl: '',
    },
    {
      name: 'Варлашкина Есения',
      dateOfBirth: new Date(),
      groupId: '16460324-ef8f-4d5d-8a23-5b2e54743290',
      schoolId: '69c6b490-0aa7-455b-953d-b3787308fa7d',
      parentIds: [],
      photoUrl: '',
    },
    {
      name: 'Остроумова Алиса',
      dateOfBirth: new Date(),
      groupId: '16460324-ef8f-4d5d-8a23-5b2e54743290',
      schoolId: '69c6b490-0aa7-455b-953d-b3787308fa7d',
      parentIds: [],
      photoUrl: '',
    },
    {
      name: 'Евсеев Одиссей',
      dateOfBirth: new Date(),
      groupId: '16460324-ef8f-4d5d-8a23-5b2e54743290',
      schoolId: '69c6b490-0aa7-455b-953d-b3787308fa7d',
      parentIds: [],
      photoUrl: '',
    },
    {
      name: 'Гурьев Дема',
      dateOfBirth: new Date(),
      groupId: '16460324-ef8f-4d5d-8a23-5b2e54743290',
      schoolId: '69c6b490-0aa7-455b-953d-b3787308fa7d',
      parentIds: [],
      photoUrl: '',
    },
    {
      name: 'Евдокимов Матвей',
      dateOfBirth: new Date(),
      groupId: '16460324-ef8f-4d5d-8a23-5b2e54743290',
      schoolId: '69c6b490-0aa7-455b-953d-b3787308fa7d',
      parentIds: [],
      photoUrl: '',
    },
    {
      name: 'Гептин Глеб',
      dateOfBirth: new Date(),
      groupId: '16460324-ef8f-4d5d-8a23-5b2e54743290',
      schoolId: '69c6b490-0aa7-455b-953d-b3787308fa7d',
      parentIds: [],
      photoUrl: '',
    },
    {
      name: 'Лазареченко Роман',
      dateOfBirth: new Date(),
      groupId: '16460324-ef8f-4d5d-8a23-5b2e54743290',
      schoolId: '69c6b490-0aa7-455b-953d-b3787308fa7d',
      parentIds: [],
      photoUrl: '',
    },
    {
      name: 'Месяткина Вера',
      dateOfBirth: new Date(),
      groupId: '16460324-ef8f-4d5d-8a23-5b2e54743290',
      schoolId: '69c6b490-0aa7-455b-953d-b3787308fa7d',
      parentIds: [],
      photoUrl: '',
    },
    {
      name: 'Москалев Леша',
      dateOfBirth: new Date(),
      groupId: '16460324-ef8f-4d5d-8a23-5b2e54743290',
      schoolId: '69c6b490-0aa7-455b-953d-b3787308fa7d',
      parentIds: [],
      photoUrl: '',
    },
    {
      name: 'Тимохина Дарина',
      dateOfBirth: new Date(),
      groupId: '16460324-ef8f-4d5d-8a23-5b2e54743290',
      schoolId: '69c6b490-0aa7-455b-953d-b3787308fa7d',
      parentIds: [],
      photoUrl: '',
    },
    {
      name: 'Пашкевич Леонардо',
      dateOfBirth: new Date(),
      groupId: '16460324-ef8f-4d5d-8a23-5b2e54743290',
      schoolId: '69c6b490-0aa7-455b-953d-b3787308fa7d',
      parentIds: [],
      photoUrl: '',
    },
  ];

  for (const child of children) {
    await prisma.child.create({
      data: child,
    });
  }
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
