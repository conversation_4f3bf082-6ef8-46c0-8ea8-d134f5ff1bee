generator client {
  provider      = "prisma-client-js"
  binaryTargets = ["native", "linux-arm64-openssl-3.0.x"]
  seed          = "ts-node prisma/seed.ts"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

enum Role {
  superadmin
  admin
  teacher
  parent
}

model School {
  id          String   @id @default(uuid())
  name        String
  ownerId     String?
  address     String?
  description String?
  contact     String?
  logoUrl     String?
  location    String?
  isDeleted   Boolean  @default(false)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  groups      Group[]  // Обратное отношение к группам
  feeds       Feed[]   // Обратная связь с Feed
  menus       Menu[]   // Обратная связь с Menu
  photos      Photo[]  // Обратная связь с Photo
  schedules   Schedule[] // Новая связь с расписанием
  children    Child[]  // Добавляем обратную связь с Child
  attendance  Attendance[] // Добавляем обратную связь с Attendance
  reports     Report[]    // Добавляем обратную связь с Report
  calendars   Calendar[]  // Обратная связь с Calendar
}

model User {
  id             String   @id @default(uuid())
  email          String   @unique
  password       String?
  role           Role
  schoolId       String?
  firstName      String?
  lastName       String?
  phone          String?
  profilePicture String?
  telegramId     String?  @unique // ID пользователя в Telegram
  telegramUsername String? // Username в Telegram
  isDeleted      Boolean  @default(false)
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt
  notes          Note[]   // Обратная связь с Note
  feeds          Feed[]   // Обратная связь с Feed
  reports        Report[] // Обратная связь с Report
  photos         Photo[]  // Обратная связь с Photo (загруженные пользователем)
  refreshTokens  RefreshToken[]
}

model Group {
  id          String   @id @default(uuid())
  name        String
  schoolId    String
  ageGroupId  String   @default("children_3_6") // Новое поле для связи с возрастной группой, по умолчанию "дети 3-6 лет"
  school      School   @relation(fields: [schoolId], references: [id]) // Связь с School
  teacherIds  String[] // Массив ID учителей
  description String?
  children    Child[]  // Обратное отношение к детям
  isDeleted   Boolean  @default(false)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
}

model Note {
  id        String   @id @default(uuid())
  childId   String
  authorId  String
  content   String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  child  Child @relation(fields: [childId], references: [id])
  author User  @relation(fields: [authorId], references: [id])
}

model Child {
  id                 String   @id @default(uuid())
  name               String
  dateOfBirth        DateTime
  groupId            String
  schoolId           String
  parentIds          String[]
  allowedTelegramIds String[] // Массив разрешенных Telegram ID
  photoUrl           String?
  accessCode         String?  @unique
  isDeleted          Boolean  @default(false)
  createdAt          DateTime @default(now())
  updatedAt          DateTime @updatedAt

  group              Group    @relation(fields: [groupId], references: [id])
  school             School   @relation(fields: [schoolId], references: [id])
  notes       Note[]       // Обратная связь с Note
  progress    Progress[]   // Обратная связь с Progress
  attendance  Attendance[] // Обратная связь с Attendance
  feeds       Feed[]       // Обратная связь с Feed
  reports     Report[]     // Обратная связь с Report
  photos      Photo[]      @relation("ChildPhotos") // Обратная связь с Photo (многие-ко-многим)
}

model Skill {
  id            String  @id @default(uuid())
  name          String
  description   String?
  category      String
  parentSkillId String?  // Связь с родительским навыком
  parentSkill   Skill?   @relation("SkillHierarchy", fields: [parentSkillId], references: [id])
  subSkills     Skill[]  @relation("SkillHierarchy")

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model Progress {
  id        String   @id @default(uuid())
  childId   String
  skillId   String
  status    String // Например: "in_progress", "completed"
  updatedAt DateTime @updatedAt

  child Child @relation(fields: [childId], references: [id])
}

model Attendance {
  id          String   @id @default(uuid())
  childId     String
  schoolId    String   // Добавляем связь со школой
  date        DateTime
  status      String   // Например: "present", "absent", "late"
  checkInTime DateTime?
  checkOutTime DateTime?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  child  Child  @relation(fields: [childId], references: [id])
  school School @relation(fields: [schoolId], references: [id]) // Добавляем связь со школой
}

model Feed {
  id        String   @id @default(uuid())
  childId   String?
  schoolId  String
  authorId  String
  content   String
  type      String   // Например: "news", "notification", "update"
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  child  Child? @relation(fields: [childId], references: [id])
  school School @relation(fields: [schoolId], references: [id])
  author User   @relation(fields: [authorId], references: [id])
}

model Report {
  id        String   @id @default(uuid())
  childId   String
  schoolId  String   // Добавляем связь со школой
  authorId  String
  fileUrl   String   // URL файла отчёта
  type      String   // Тип отчёта: "progress", "attendance", "custom"
  name      String?  // Название отчета (опционально)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  child  Child  @relation(fields: [childId], references: [id])
  school School @relation(fields: [schoolId], references: [id]) // Добавляем связь со школой
  author User   @relation(fields: [authorId], references: [id])
}

model Photo {
  id          String   @id @default(uuid())
  schoolId    String?  // Связь с садом (опционально)
  url         String   // Ссылка на фото
  description String?  // Описание фото
  uploadedBy  String   // Кто загрузил (User ID)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  school  School?  @relation(fields: [schoolId], references: [id])
  user    User     @relation(fields: [uploadedBy], references: [id])
  children Child[] @relation("ChildPhotos") // Связь многие-ко-многим
}

model RefreshToken {
  id         String   @id @default(uuid())
  userId     String   @unique
  user       User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  tokenHash  String   // Храним `refreshToken` в хешированном виде
  createdAt  DateTime @default(now())
  expiresAt  DateTime
}

model Schedule {
  id        String   @id @default(uuid())
  schoolId  String
  ageGroupId String   @default("children_3_6") // Добавляем поле для связи с возрастной группой
  fileUrl   String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  school School @relation(fields: [schoolId], references: [id])

  @@unique([schoolId, ageGroupId]) // Уникальное ограничение: одно расписание на школу и возрастную группу
}

model Menu {
  id        String   @id @default(uuid())
  schoolId  String   @unique
  fileUrl   String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  school School @relation(fields: [schoolId], references: [id])
}

model Calendar {
  id        String   @id @default(uuid())
  schoolId  String   @unique
  fileUrl   String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  school School @relation(fields: [schoolId], references: [id])
}
