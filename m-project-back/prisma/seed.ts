// prisma/seed.ts
import { PrismaClient } from '@prisma/client';
import * as bcrypt from 'bcryptjs';

const prisma = new PrismaClient();

const children = [
  {
    name: 'Егоров Никита',
    dateOfBirth: new Date(),
    groupId: '16460324-ef8f-4d5d-8a23-5b2e54743290',
    schoolId: '69c6b490-0aa7-455b-953d-b3787308fa7d',
    parentIds: [],
    photoUrl: '',
  },
  {
    name: 'Сысоля<PERSON>ин Макар',
    dateOfBirth: new Date(),
    groupId: '16460324-ef8f-4d5d-8a23-5b2e54743290',
    schoolId: '69c6b490-0aa7-455b-953d-b3787308fa7d',
    parentIds: [],
    photoUrl: '',
  },
  {
    name: '<PERSON><PERSON><PERSON><PERSON> Мирон',
    dateOfBirth: new Date(),
    groupId: '16460324-ef8f-4d5d-8a23-5b2e54743290',
    schoolId: '69c6b490-0aa7-455b-953d-b3787308fa7d',
    parentIds: [],
    photoUrl: '',
  },
  {
    name: 'Тимошук Майя',
    dateOfBirth: new Date(),
    groupId: '16460324-ef8f-4d5d-8a23-5b2e54743290',
    schoolId: '69c6b490-0aa7-455b-953d-b3787308fa7d',
    parentIds: [],
    photoUrl: '',
  },
  {
    name: 'Калашникова Стелла',
    dateOfBirth: new Date(),
    groupId: '16460324-ef8f-4d5d-8a23-5b2e54743290',
    schoolId: '69c6b490-0aa7-455b-953d-b3787308fa7d',
    parentIds: [],
    photoUrl: '',
  },
  {
    name: 'Агапова Полина',
    dateOfBirth: new Date(),
    groupId: '16460324-ef8f-4d5d-8a23-5b2e54743290',
    schoolId: '69c6b490-0aa7-455b-953d-b3787308fa7d',
    parentIds: [],
    photoUrl: '',
  },
  {
    name: 'Вахтина Паулина',
    dateOfBirth: new Date(),
    groupId: '16460324-ef8f-4d5d-8a23-5b2e54743290',
    schoolId: '69c6b490-0aa7-455b-953d-b3787308fa7d',
    parentIds: [],
    photoUrl: '',
  },
  {
    name: 'Холковская Мира',
    dateOfBirth: new Date(),
    groupId: '16460324-ef8f-4d5d-8a23-5b2e54743290',
    schoolId: '69c6b490-0aa7-455b-953d-b3787308fa7d',
    parentIds: [],
    photoUrl: '',
  },
  {
    name: 'Бриленков Арсений',
    dateOfBirth: new Date(),
    groupId: '16460324-ef8f-4d5d-8a23-5b2e54743290',
    schoolId: '69c6b490-0aa7-455b-953d-b3787308fa7d',
    parentIds: [],
    photoUrl: '',
  },
  {
    name: 'Ковалев Богдан',
    dateOfBirth: new Date(),
    groupId: '16460324-ef8f-4d5d-8a23-5b2e54743290',
    schoolId: '69c6b490-0aa7-455b-953d-b3787308fa7d',
    parentIds: [],
    photoUrl: '',
  },
  {
    name: 'Милкова Мия',
    dateOfBirth: new Date(),
    groupId: '16460324-ef8f-4d5d-8a23-5b2e54743290',
    schoolId: '69c6b490-0aa7-455b-953d-b3787308fa7d',
    parentIds: [],
    photoUrl: '',
  },
  {
    name: 'Бороздунов Влад',
    dateOfBirth: new Date(),
    groupId: '16460324-ef8f-4d5d-8a23-5b2e54743290',
    schoolId: '69c6b490-0aa7-455b-953d-b3787308fa7d',
    parentIds: [],
    photoUrl: '',
  },
  {
    name: 'Копытов Матвей',
    dateOfBirth: new Date(),
    groupId: '16460324-ef8f-4d5d-8a23-5b2e54743290',
    schoolId: '69c6b490-0aa7-455b-953d-b3787308fa7d',
    parentIds: [],
    photoUrl: '',
  },
  {
    name: 'Ченгина Вероника',
    dateOfBirth: new Date(),
    groupId: '16460324-ef8f-4d5d-8a23-5b2e54743290',
    schoolId: '69c6b490-0aa7-455b-953d-b3787308fa7d',
    parentIds: [],
    photoUrl: '',
  },
  {
    name: 'Ляшко Лева',
    dateOfBirth: new Date(),
    groupId: '16460324-ef8f-4d5d-8a23-5b2e54743290',
    schoolId: '69c6b490-0aa7-455b-953d-b3787308fa7d',
    parentIds: [],
    photoUrl: '',
  },
  {
    name: 'Новиков Матвей',
    dateOfBirth: new Date(),
    groupId: '16460324-ef8f-4d5d-8a23-5b2e54743290',
    schoolId: '69c6b490-0aa7-455b-953d-b3787308fa7d',
    parentIds: [],
    photoUrl: '',
  },
  {
    name: 'Шевелева Диана',
    dateOfBirth: new Date(),
    groupId: '16460324-ef8f-4d5d-8a23-5b2e54743290',
    schoolId: '69c6b490-0aa7-455b-953d-b3787308fa7d',
    parentIds: [],
    photoUrl: '',
  },
  {
    name: 'Ляпин Мирослав',
    dateOfBirth: new Date(),
    groupId: '16460324-ef8f-4d5d-8a23-5b2e54743290',
    schoolId: '69c6b490-0aa7-455b-953d-b3787308fa7d',
    parentIds: [],
    photoUrl: '',
  },
  {
    name: 'Кудрявский Арсений',
    dateOfBirth: new Date(),
    groupId: '16460324-ef8f-4d5d-8a23-5b2e54743290',
    schoolId: '69c6b490-0aa7-455b-953d-b3787308fa7d',
    parentIds: [],
    photoUrl: '',
  },
  {
    name: 'Варлашкина Есения',
    dateOfBirth: new Date(),
    groupId: '16460324-ef8f-4d5d-8a23-5b2e54743290',
    schoolId: '69c6b490-0aa7-455b-953d-b3787308fa7d',
    parentIds: [],
    photoUrl: '',
  },
  {
    name: 'Остроумова Алиса',
    dateOfBirth: new Date(),
    groupId: '16460324-ef8f-4d5d-8a23-5b2e54743290',
    schoolId: '69c6b490-0aa7-455b-953d-b3787308fa7d',
    parentIds: [],
    photoUrl: '',
  },
  {
    name: 'Евсеев Одиссей',
    dateOfBirth: new Date(),
    groupId: '16460324-ef8f-4d5d-8a23-5b2e54743290',
    schoolId: '69c6b490-0aa7-455b-953d-b3787308fa7d',
    parentIds: [],
    photoUrl: '',
  },
  {
    name: 'Гурьев Дема',
    dateOfBirth: new Date(),
    groupId: '16460324-ef8f-4d5d-8a23-5b2e54743290',
    schoolId: '69c6b490-0aa7-455b-953d-b3787308fa7d',
    parentIds: [],
    photoUrl: '',
  },
  {
    name: 'Евдокимов Матвей',
    dateOfBirth: new Date(),
    groupId: '16460324-ef8f-4d5d-8a23-5b2e54743290',
    schoolId: '69c6b490-0aa7-455b-953d-b3787308fa7d',
    parentIds: [],
    photoUrl: '',
  },
  {
    name: 'Гептин Глеб',
    dateOfBirth: new Date(),
    groupId: '16460324-ef8f-4d5d-8a23-5b2e54743290',
    schoolId: '69c6b490-0aa7-455b-953d-b3787308fa7d',
    parentIds: [],
    photoUrl: '',
  },
  {
    name: 'Лазареченко Роман',
    dateOfBirth: new Date(),
    groupId: '16460324-ef8f-4d5d-8a23-5b2e54743290',
    schoolId: '69c6b490-0aa7-455b-953d-b3787308fa7d',
    parentIds: [],
    photoUrl: '',
  },
  {
    name: 'Месяткина Вера',
    dateOfBirth: new Date(),
    groupId: '16460324-ef8f-4d5d-8a23-5b2e54743290',
    schoolId: '69c6b490-0aa7-455b-953d-b3787308fa7d',
    parentIds: [],
    photoUrl: '',
  },
  {
    name: 'Москалев Леша',
    dateOfBirth: new Date(),
    groupId: '16460324-ef8f-4d5d-8a23-5b2e54743290',
    schoolId: '69c6b490-0aa7-455b-953d-b3787308fa7d',
    parentIds: [],
    photoUrl: '',
  },
  {
    name: 'Тимохина Дарина',
    dateOfBirth: new Date(),
    groupId: '16460324-ef8f-4d5d-8a23-5b2e54743290',
    schoolId: '69c6b490-0aa7-455b-953d-b3787308fa7d',
    parentIds: [],
    photoUrl: '',
  },
  {
    name: 'Пашкевич Леонардо',
    dateOfBirth: new Date(),
    groupId: '16460324-ef8f-4d5d-8a23-5b2e54743290',
    schoolId: '69c6b490-0aa7-455b-953d-b3787308fa7d',
    parentIds: [],
    photoUrl: '',
  },
];

async function main() {
  // Удаление зависимых данных в правильном порядке
  await prisma.report.deleteMany({});
  await prisma.attendance.deleteMany({});
  await prisma.feed.deleteMany({});
  await prisma.note.deleteMany({});
  await prisma.progress.deleteMany({});
  await prisma.photo.deleteMany({});
  await prisma.child.deleteMany({});
  await prisma.group.deleteMany({});
  await prisma.menu.deleteMany({});
  await prisma.schedule.deleteMany({});
  await prisma.skill.deleteMany({});
  await prisma.user.deleteMany({});
  await prisma.school.deleteMany({});

  // Создание пользователя-админа
  const hashedPassword = await bcrypt.hash('password123', 10);

  // Создаём пользователя

  // Создание школы
  const school = await prisma.school.create({
    data: {
      name: 'Монтессори Дом',
      address: 'Калининград',
    },
  });

  const admin = await prisma.user.create({
    data: {
      email: '<EMAIL>',
      password: hashedPassword,
      role: 'superadmin',
      schoolId: school.id,
    },
  });

  console.log('Test user created:', admin);

  // Создание группы
  const group = await prisma.group.create({
    data: {
      name: 'Primary Group',
      schoolId: school.id,
      teacherIds: [admin.id],
      description: 'This is the primary group for younger children.',
    },
  });

  // Обновляем данные детей с правильным groupId
  const updatedChildren = children.map((child) => ({
    ...child,
    groupId: group.id, // Используем id только что созданной группы
    schoolId: school.id, // И id только что созданной школы
  }));

  // Создаём детей с обновленными данными
  for (const childData of updatedChildren) {
    await prisma.child.create({
      data: childData,
    });
  }

  // Создание навыков
  const skill = await prisma.skill.create({
    data: {
      name: 'Mathematics',
      description: 'Basic math skills for children.',
      category: 'Education',
    },
  });

  await prisma.skill.create({
    data: {
      name: 'Addition',
      description: 'Learning how to add numbers.',
      category: 'Education',
      parentSkillId: skill.id,
    },
  });

  // Создание событий в ленте
  await prisma.feed.create({
    data: {
      schoolId: school.id,
      authorId: admin.id,
      content: 'Welcome to Montessori School #1!',
      type: 'news',
    },
  });

  // Создание меню (обновлено)
  await prisma.menu.create({
    data: {
      schoolId: school.id,
      fileUrl: 'https://example.com/menu1.pdf',
    },
  });

  // Добавляем создание расписания
  await prisma.schedule.create({
    data: {
      schoolId: school.id,
      fileUrl: 'https://example.com/schedule1.pdf',
    },
  });

  console.log('Seed data successfully created!');
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
