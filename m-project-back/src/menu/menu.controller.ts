import {
  Controller,
  Get,
  Post,
  Delete,
  Param,
  UploadedFile,
  UseInterceptors,
  Res,
  MaxFileSizeValidator,
  ParseFilePipe,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { MenuService } from './menu.service';
import { Response } from 'express';
import * as path from 'path';
import * as fs from 'fs';
import { multerConfig } from '../common/multer.config';

@Controller('menu')
export class MenuController {
  constructor(private readonly menuService: MenuService) {}

  @Post('upload/:schoolId')
  @UseInterceptors(FileInterceptor('file', multerConfig))
  uploadFile(
    @Param('schoolId') schoolId: string,
    @UploadedFile(
      new ParseFilePipe({
        validators: [
          new MaxFileSizeValidator({ maxSize: 15 * 1024 * 1024 }), // 15 MB
        ],
        fileIsRequired: true,
      }),
    ) file: Express.Multer.File,
  ) {
    return this.menuService.uploadFile(schoolId, file);
  }

  @Get(':schoolId')
  getMenu(@Param('schoolId') schoolId: string) {
    return this.menuService.getMenu(schoolId);
  }

  @Get('download/:schoolId')
  async downloadFile(
    @Param('schoolId') schoolId: string,
    @Res() res: Response,
  ) {
    const filePath = path.join(
      __dirname,
      '..',
      '..',
      'uploads',
      'menus',
      `school-${schoolId}.pdf`,
    );

    if (!fs.existsSync(filePath)) {
      return res.status(404).json({ message: 'Файл не найден' });
    }

    return res.sendFile(filePath);
  }

  @Delete(':schoolId')
  deleteMenu(@Param('schoolId') schoolId: string) {
    return this.menuService.deleteMenu(schoolId);
  }
}
