import { Injectable, NotFoundException, Logger } from '@nestjs/common';
import { PrismaService } from 'src/prisma.service';
import * as fs from 'fs';
import * as path from 'path';

@Injectable()
export class MenuService {
  private readonly logger = new Logger(MenuService.name);
  private readonly uploadDir = path.join(process.cwd(), 'uploads', 'menus');

  constructor(private prisma: PrismaService) {
    if (!fs.existsSync(this.uploadDir)) {
      fs.mkdirSync(this.uploadDir, { recursive: true });
    }
  }

  async uploadFile(schoolId: string, file: Express.Multer.File) {
    try {
      const filePath = path.join(this.uploadDir, `school-${schoolId}.pdf`);
      fs.writeFileSync(filePath, file.buffer);

      const fileUrl = `/uploads/menus/school-${schoolId}.pdf`;

      return await this.prisma.menu.upsert({
        where: { schoolId },
        update: { fileUrl },
        create: { schoolId, fileUrl },
      });
    } catch (error) {
      this.logger.error(
        `Ошибка при загрузке меню для школы ${schoolId}:`,
        error.stack,
      );
      throw error;
    }
  }

  async getMenu(schoolId: string) {
    try {
      const menu = await this.prisma.menu.findUnique({
        where: { schoolId },
      });
      if (!menu) throw new NotFoundException('Меню не найдено');
      return menu;
    } catch (error) {
      if (error instanceof NotFoundException) throw error;
      this.logger.error(
        `Ошибка при получении меню для школы ${schoolId}:`,
        error.stack,
      );
      throw error;
    }
  }

  async deleteMenu(schoolId: string) {
    try {
      const filePath = path.join(this.uploadDir, `school-${schoolId}.pdf`);

      if (fs.existsSync(filePath)) {
        fs.unlinkSync(filePath);
      }

      await this.prisma.menu.delete({ where: { schoolId } });
      return { message: 'Меню удалено' };
    } catch (error) {
      this.logger.error(
        `Ошибка при удалении меню для школы ${schoolId}:`,
        error.stack,
      );
      throw error;
    }
  }
}
