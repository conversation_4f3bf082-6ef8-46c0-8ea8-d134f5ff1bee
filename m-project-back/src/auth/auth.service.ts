import {
  Injectable,
  UnauthorizedException,
  ConflictException,
  InternalServerErrorException,
  BadRequestException,
  NotFoundException,
  Logger,
} from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import * as bcrypt from 'bcryptjs';
import { PrismaService } from 'src/prisma.service';
import { Role } from '@prisma/client';
import * as crypto from 'crypto'; // 🔹 Импортируем crypto

@Injectable()
export class AuthService {
  constructor(
    private prisma: PrismaService,
    private jwtService: JwtService,
  ) {}
  private readonly logger = new Logger(AuthService.name);

  /** 🔹 Валидация пользователя (email + пароль) */
  async validateUser(email: string, password: string): Promise<any> {
    const user = await this.prisma.user.findUnique({
      where: { email },
    });

    if (!user || !(await bcrypt.compare(password, user.password))) {
      throw new UnauthorizedException('Неверный email или пароль');
    }

    const { ...result } = user;
    return result;
  }

  /** 🔹 Логин и генерация токенов */
  async login(user: any) {
    const payload = { email: user.email, sub: user.id, role: user.role };

    // 🔹 Генерация `refreshToken` и запись в БД
    const refreshToken = await this.generateRefreshToken(user.id);

    return {
      accessToken: this.jwtService.sign(payload, { expiresIn: '30d' }),
      refreshToken,
    };
  }

  /** 🔹 Логин через Telegram и генерация токенов */
  async loginWithTelegram(user: any) {
    // Для Telegram-пользователей используем telegramId вместо email в payload
    const payload = {
      email: user.email,
      sub: user.id,
      role: user.role,
      telegramId: user.telegramId,
    };

    // Генерация refreshToken и запись в БД
    const refreshToken = await this.generateRefreshToken(user.id);

    return {
      accessToken: this.jwtService.sign(payload, { expiresIn: '30d' }),
      refreshToken,
    };
  }

  /** 🔹 Генерация и сохранение `refreshToken` */
  async generateRefreshToken(userId: string): Promise<string> {
    const refreshToken = crypto.randomUUID();
    const hashedToken = await bcrypt.hash(refreshToken, 10);

    try {
      await this.prisma.refreshToken.upsert({
        where: { userId },
        update: {
          tokenHash: hashedToken,
          expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
        },
        create: {
          userId,
          tokenHash: hashedToken,
          expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
        },
      });

      return refreshToken;
    } catch (error) {
      throw new InternalServerErrorException(
        `Ошибка генерации refreshToken: ${error.message}`,
      );
    }
  }

  /** 🔹 Проверка `refreshToken` */
  async validateRefreshToken(
    userId: string,
    refreshToken: string,
  ): Promise<boolean> {
    const storedToken = await this.prisma.refreshToken.findUnique({
      where: { userId },
    });

    if (!storedToken) return false;
    if (storedToken.expiresAt < new Date()) return false; // Проверяем срок жизни токена

    return await bcrypt.compare(refreshToken, storedToken.tokenHash);
  }

  /** 🔹 Регистрация нового пользователя */
  async register(data: { email: string; password: string; role: string }) {
    const hashedPassword = await bcrypt.hash(data.password, 10);

    // 🔹 Проверка, существует ли такой email
    const existingUser = await this.prisma.user.findUnique({
      where: { email: data.email },
    });
    if (existingUser) {
      throw new ConflictException('Пользователь с таким email уже существует');
    }

    // 🔹 Проверка, что роль существует в `Role`
    if (!Object.values(Role).includes(data.role as Role)) {
      throw new BadRequestException('Неверная роль пользователя');
    }

    try {
      const user = await this.prisma.user.create({
        data: {
          email: data.email,
          password: hashedPassword,
          role: data.role as Role,
        },
      });

      const { ...result } = user;
      return result;
    } catch (error) {
      throw new InternalServerErrorException(
        `Ошибка создания пользователя: ${error.message}`,
      );
    }
  }

  /** 🔹 Логаут: удаляем `refreshToken` */
  async logout(userId: string) {
    try {
      await this.prisma.refreshToken.deleteMany({
        where: { userId },
      });
    } catch (error) {
      throw new InternalServerErrorException(
        `Ошибка при выходе из системы: ${error.message}`,
      );
    }
  }

  async getProfile(id: string) {
    try {
      const user = await this.prisma.user.findUnique({
        where: { id },
      });
      if (!user) {
        throw new NotFoundException(`Пользователь с ID ${id} не найден`);
      }
      return user;
    } catch (error) {
      this.handlePrismaError(error, 'findOne');
    }
  }

  private handlePrismaError(error: any, operation: string) {
    if (error.code === 'P2002') {
      throw new ConflictException(
        'Нарушено уникальное ограничение. Проверьте данные и попробуйте снова.',
      );
    }

    if (error.code === 'P2025') {
      throw new NotFoundException(
        'Запись для обновления или удаления не найдена',
      );
    }

    this.logger.error(`Ошибка в операции ${operation}:`, error.stack);
    throw new BadRequestException('Произошла ошибка при выполнении операции');
  }
}
