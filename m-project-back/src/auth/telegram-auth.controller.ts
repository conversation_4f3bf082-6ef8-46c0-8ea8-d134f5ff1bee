import {
  Controller,
  Post,
  Body,
  Headers,
  UnauthorizedException,
  NotFoundException,
  BadRequestException,
} from '@nestjs/common';
import { AuthService } from './auth.service';
import { PrismaService } from 'src/prisma.service';

@Controller('auth/telegram')
export class TelegramAuthController {
  constructor(
    private authService: AuthService,
    private prisma: PrismaService,
  ) {}

  /**
   * Эндпоинт для привязки Telegram аккаунта к ребенку через код доступа
   */
  @Post('connect-child')
  async connectChild(
    @Headers('X-Telegram-Init-Data') initData: string,
    @Body()
    body: {
      accessCode: string;
      firstName?: string;
      lastName?: string;
      phone?: string;
      skipProfileUpdate?: boolean; // Флаг для пропуска обновления профиля
    },
  ) {
    // Проверка наличия кода доступа
    if (!body.accessCode) {
      throw new BadRequestException('Код доступа обязателен');
    }

    // Проверка и декодирование данных Telegram
    const telegramData = this.validateAndDecodeTelegramData(initData);

    // Поиск ребенка по коду доступа
    const child = await this.prisma.child.findFirst({
      where: {
        accessCode: body.accessCode,
        isDeleted: false,
      },
    });

    if (!child) {
      throw new NotFoundException('Ребенок с таким кодом не найден');
    }

    // Проверяем, есть ли текущий Telegram ID в списке разрешенных
    if (
      child.allowedTelegramIds &&
      child.allowedTelegramIds.includes(telegramData.id.toString())
    ) {
      // Если текущий Telegram ID уже в списке разрешенных, продолжаем
      console.log(
        `Telegram ID ${telegramData.id} уже в списке разрешенных для ребенка ${child.id}`,
      );
    } else if (
      child.allowedTelegramIds &&
      child.allowedTelegramIds.length >= 3
    ) {
      // Если уже есть 3 или более подключений, запрещаем новое
      throw new UnauthorizedException(
        'Доступ запрещен. К этому ребенку уже подключено максимальное количество пользователей (3).',
      );
    } else {
      // Если количество подключений меньше 3 или список пуст, добавляем новый ID
      try {
        const updatedTelegramIds = child.allowedTelegramIds || [];
        updatedTelegramIds.push(telegramData.id.toString());

        await this.prisma.child.update({
          where: { id: child.id },
          data: {
            allowedTelegramIds: updatedTelegramIds,
          },
        });
        console.log(
          `Автоматически добавлен Telegram ID ${telegramData.id} для ребенка ${child.id}`,
        );
      } catch (error) {
        console.error(
          `Ошибка при автоматическом добавлении Telegram ID: ${error.message}`,
        );
        // Не выбрасываем ошибку, чтобы не блокировать авторизацию
      }
    }

    // Проверяем, существует ли пользователь с таким telegramId
    let user = await this.prisma.user.findUnique({
      where: { telegramId: telegramData.id.toString() },
    });

    // Если пользователя нет, создаем нового
    if (!user) {
      user = await this.prisma.user.create({
        data: {
          email: `telegram_${telegramData.id}@example.com`, // Временный email
          role: 'parent',
          telegramId: telegramData.id.toString(),
          telegramUsername: telegramData.username || null,
          firstName: body.firstName || telegramData.first_name || null,
          lastName: body.lastName || telegramData.last_name || null,
          phone: body.phone || null,
        },
      });
    } else if (!body.skipProfileUpdate) {
      // Обновляем данные пользователя, если не указан флаг skipProfileUpdate
      await this.prisma.user.update({
        where: { id: user.id },
        data: {
          firstName: body.firstName || user.firstName,
          lastName: body.lastName || user.lastName,
          phone: body.phone || user.phone,
        },
      });
    }

    // Обновляем parentIds ребенка, добавляя ID пользователя, если его там еще нет
    if (!child.parentIds.includes(user.id)) {
      await this.prisma.child.update({
        where: { id: child.id },
        data: {
          parentIds: [...child.parentIds, user.id],
          // Опционально: удаляем код доступа после использования
          // accessCode: null,
        },
      });
    }

    // Генерируем JWT токен для пользователя
    const { accessToken } = await this.authService.loginWithTelegram(user);

    // Логируем информацию о ребенке и пользователе
    console.log(
      `TelegramAuthController: Успешная авторизация для пользователя ${user.id}`,
    );
    console.log(
      `TelegramAuthController: Ребенок ID: ${child.id}, Имя: ${child.name}`,
    );

    // Возвращаем информацию о ребенке и токен
    return {
      accessToken,
      user: {
        id: user.id,
        firstName: user.firstName,
        lastName: user.lastName,
        role: user.role,
      },
      child: {
        id: child.id,
        name: child.name,
      },
    };
  }

  /**
   * Проверка и декодирование данных, полученных от Telegram
   */
  private validateAndDecodeTelegramData(initData: string): any {
    console.log('Received initData:', initData ? 'present' : 'missing');
    if (initData) {
      console.log('InitData first 100 chars:', initData.substring(0, 100));
    }

    // Для режима разработки или тестирования
    if (!initData || initData === 'mock_init_data_for_development') {
      console.log('Using test Telegram data');
      return {
        id: 12345678,
        first_name: 'Test',
        last_name: 'User',
        username: 'testuser',
      };
    }

    try {
      // В реальном приложении здесь должна быть проверка подписи данных
      // с использованием BOT_TOKEN и хеш-функции

      // Для MVP просто парсим данные
      const params = new URLSearchParams(initData);

      // Логируем все параметры для отладки
      console.log('Telegram initData params:');
      for (const [key, value] of params.entries()) {
        console.log(
          `${key}: ${value.length > 100 ? value.substring(0, 100) + '...' : value}`,
        );
      }

      const dataString = params.get('user');

      if (!dataString) {
        console.log('No user data in initData, using test data');
        return {
          id: 12345678,
          first_name: 'Test',
          last_name: 'User',
          username: 'testuser',
        };
      }

      const userData = JSON.parse(dataString);
      console.log('Parsed Telegram user data:', userData);
      return userData;
    } catch (error) {
      console.error('Error parsing Telegram data:', error);
      // Для MVP возвращаем тестовые данные вместо выброса исключения
      return {
        id: 12345678,
        first_name: 'Test',
        last_name: 'User',
        username: 'testuser',
      };
    }
  }

  /**
   * Эндпоинт для валидации кода доступа без создания пользователя
   */
  @Post('validate-code')
  async validateCode(
    @Headers('X-Telegram-Init-Data') initData: string,
    @Body() body: { accessCode: string },
  ) {
    // Проверка наличия кода доступа
    if (!body.accessCode) {
      throw new BadRequestException('Код доступа обязателен');
    }

    // Проверка и декодирование данных Telegram
    const telegramData = this.validateAndDecodeTelegramData(initData);

    // Поиск ребенка по коду доступа
    const child = await this.prisma.child.findFirst({
      where: {
        accessCode: body.accessCode,
        isDeleted: false,
      },
    });

    if (!child) {
      throw new NotFoundException('Ребенок с таким кодом не найден');
    }

    // Проверяем, есть ли текущий Telegram ID в списке разрешенных
    if (
      child.allowedTelegramIds &&
      child.allowedTelegramIds.includes(telegramData.id.toString())
    ) {
      // Если текущий Telegram ID уже в списке разрешенных, продолжаем
      console.log(
        `Telegram ID ${telegramData.id} уже в списке разрешенных для ребенка ${child.id}`,
      );
    } else if (
      child.allowedTelegramIds &&
      child.allowedTelegramIds.length >= 3
    ) {
      // Если уже есть 3 или более подключений, запрещаем новое
      throw new UnauthorizedException(
        'Доступ запрещен. К этому ребенку уже подключено максимальное количество пользователей (3).',
      );
    }

    // Возвращаем базовую информацию о ребенке и данные Telegram
    return {
      childId: child.id,
      childName: child.name,
      telegramData: {
        id: telegramData.id,
        first_name: telegramData.first_name,
        last_name: telegramData.last_name,
        username: telegramData.username,
      },
    };
  }

  /**
   * Эндпоинт для проверки существования пользователя и его связи с ребенком
   */
  @Post('check-user')
  async checkUser(
    @Headers('X-Telegram-Init-Data') initData: string,
    @Body() body: { telegramId: string; accessCode: string },
  ) {
    try {
      // Проверяем существование пользователя с таким telegramId
      const user = await this.prisma.user.findUnique({
        where: { telegramId: body.telegramId },
      });

      // Если пользователь не найден
      if (!user) {
        return { exists: false, isConnectedToChild: false };
      }

      // Поиск ребенка по коду доступа
      const child = await this.prisma.child.findFirst({
        where: {
          accessCode: body.accessCode,
          isDeleted: false,
        },
      });

      if (!child) {
        return { exists: true, isConnectedToChild: false };
      }

      // Проверяем, связан ли пользователь с ребенком
      const isConnected = child.parentIds.includes(user.id);

      return { exists: true, isConnectedToChild: isConnected };
    } catch (error) {
      console.error('Error checking user:', error);
      throw new BadRequestException('Error checking user');
    }
  }

  /**
   * Эндпоинт для получения информации о детях пользователя
   */
  @Post('get-children')
  async getChildren(@Headers('X-Telegram-Init-Data') initData: string) {
    const telegramData = this.validateAndDecodeTelegramData(initData);

    const user = await this.prisma.user.findUnique({
      where: { telegramId: telegramData.id.toString() },
    });

    if (!user) {
      return [];
    }

    // Добавляем дополнительную проверку allowedTelegramIds
    const children = await this.prisma.child.findMany({
      where: {
        AND: [
          { parentIds: { has: user.id } },
          { allowedTelegramIds: { has: user.telegramId } },
          { isDeleted: false },
        ],
      },
      select: {
        id: true,
        name: true,
        dateOfBirth: true,
        photoUrl: true,
        schoolId: true,
        group: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    });

    return children;
  }
}
