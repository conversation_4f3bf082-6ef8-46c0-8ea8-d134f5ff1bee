import {
  Controller,
  Post,
  Body,
  Request,
  UseGuards,
  Res,
  Get,
} from '@nestjs/common';
import { Response } from 'express';
import { AuthService } from './auth.service';
import { LocalAuthGuard } from '../guards/local-auth.guard';
import { JwtAuthGuard } from 'src/guards/jwt-auth.guard';

@Controller('auth')
export class AuthController {
  constructor(private authService: AuthService) {}

  /** 🔹 Логин */
  @Post('login')
  @UseGuards(LocalAuthGuard)
  async login(@Request() req, @Res({ passthrough: true }) res: Response) {
    const { accessToken, refreshToken } = await this.authService.login(
      req.user,
    );

    res.cookie('refreshToken', refreshToken, {
      httpOnly: true,
      // secure: process.env.NODE_ENV === 'production', // ⬅️ Работает только через HTTPS
      // sameSite: 'Strict', // ⬅️ Защита от CSRF-атак
      maxAge: 7 * 24 * 60 * 60 * 1000, // 7 дней
    });

    return { accessToken };
  }

  /** 🔹 Регистрация */
  @Post('register')
  async register(
    @Body() body: { email: string; password: string; role: string },
  ) {
    return this.authService.register(body);
  }

  /** 🔹 Получение профиля */
  @Get('me')
  @UseGuards(JwtAuthGuard) // Защищает маршрут, требует accessToken
  async getProfile(@Request() req) {
    console.log(req.user);
    return this.authService.getProfile(req.user.id); // Данные пользователя берутся из токена
  }

  /** 🔹 Выход (logout) */
  @Post('logout')
  @UseGuards(JwtAuthGuard) // ⬅️ Добавляем защиту, иначе `req.user.id` = undefined
  async logout(@Request() req, @Res({ passthrough: true }) res: Response) {
    await this.authService.logout(req.user.id); // Удаляем refreshToken из БД

    res.cookie('refreshToken', '', {
      httpOnly: true,
      // secure: process.env.NODE_ENV === 'production',
      // sameSite: process.env.NODE_ENV === 'production' ? 'Strict' : '',
      expires: new Date(0), // Очистка куки
    });

    return { message: 'Вы вышли из системы' };
  }
}
