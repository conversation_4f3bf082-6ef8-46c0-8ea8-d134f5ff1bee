import { Modu<PERSON> } from '@nestjs/common';
import { JwtModule } from '@nestjs/jwt';
import { PassportModule } from '@nestjs/passport';
import { AuthService } from './auth.service';
import { AuthController } from './auth.controller';
import { TelegramAuthController } from './telegram-auth.controller';
import { JwtStrategy } from '../strategies/jwt.strategy';
import { PrismaService } from 'src/prisma.service';
import { LocalStrategy } from '../strategies/local.strategy';

@Module({
  imports: [
    PassportModule,
    JwtModule.register({
      secret: process.env.JWT_SECRET || 'secretKey',
      signOptions: { expiresIn: '1h' },
    }),
  ],
  providers: [AuthService, JwtStrategy, PrismaService, LocalStrategy],
  controllers: [AuthController, TelegramAuthController],
})
export class AuthModule {}
