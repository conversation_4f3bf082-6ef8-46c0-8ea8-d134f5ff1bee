// src/user/user.service.ts
import {
  Injectable,
  Logger,
  NotFoundException,
  ConflictException,
  BadRequestException,
} from '@nestjs/common';
import * as bcrypt from 'bcryptjs';
import { PrismaService } from 'src/prisma.service';
import { User } from '@prisma/client';
import { CreateUserDto } from './dto/create-user.dto';
import { UpdateUserDto } from './dto/update-user.dto';

@Injectable()
export class UserService {
  private readonly logger = new Logger(UserService.name);

  constructor(private prisma: PrismaService) {}

  async createUser(dto: CreateUserDto): Promise<User> {
    try {
      // Проверяем, существует ли пользователь с таким email
      const existingUser = await this.prisma.user.findUnique({
        where: { email: dto.email },
      });

      if (existingUser) {
        throw new ConflictException(
          'Пользователь с таким email уже существует',
        );
      }

      // Хешируем пароль перед сохранением
      const hashedPassword = await bcrypt.hash(dto.password, 10);

      return this.prisma.user.create({
        data: {
          email: dto.email,
          password: hashedPassword,
          role: dto.role,
          schoolId: dto.schoolId,
          firstName: dto.firstName,
          lastName: dto.lastName,
          phone: dto.phone,
          profilePicture: dto.profilePicture,
        },
      });
    } catch (error) {
      this.handlePrismaError(error, 'create');
    }
  }

  async findAll(): Promise<User[]> {
    try {
      return await this.prisma.user.findMany({
        where: { isDeleted: false },
      });
    } catch (error) {
      this.handlePrismaError(error, 'findAll');
    }
  }

  async findAllParents(schoolId: string): Promise<User[]> {
    try {
      return await this.prisma.user.findMany({
        where: {
          isDeleted: false,
          role: 'parent',
          schoolId: schoolId,
        },
      });
    } catch (error) {
      this.handlePrismaError(error, 'findAllParents');
    }
  }

  async findOne(id: string): Promise<User> {
    try {
      const user = await this.prisma.user.findUnique({
        where: { id },
      });
      if (!user) {
        throw new NotFoundException(`Пользователь с ID ${id} не найден`);
      }
      return user;
    } catch (error) {
      this.handlePrismaError(error, 'findOne');
    }
  }

  async updateUser(id: string, dto: UpdateUserDto): Promise<User> {
    await this.findOne(id);

    try {
      const user = await this.prisma.user.update({
        where: { id },
        data: {
          ...dto,
          updatedAt: new Date(),
        },
      });
      this.logger.log(`Пользователь обновлен: ID ${user.id}`);
      return user;
    } catch (error) {
      this.handlePrismaError(error, 'update');
    }
  }

  async softDelete(id: string): Promise<User> {
    await this.findOne(id);

    try {
      const user = await this.prisma.user.update({
        where: { id },
        data: {
          isDeleted: true,
        },
      });
      this.logger.log(`Пользователь удален (soft): ID ${user.id}`);
      return user;
    } catch (error) {
      this.handlePrismaError(error, 'softDelete');
    }
  }

  private handlePrismaError(error: any, operation: string) {
    if (error.code === 'P2002') {
      throw new ConflictException(
        'Нарушено уникальное ограничение. Проверьте данные и попробуйте снова.',
      );
    }

    if (error.code === 'P2025') {
      throw new NotFoundException(
        'Запись для обновления или удаления не найдена',
      );
    }

    this.logger.error(`Ошибка в операции ${operation}:`, error.stack);
    throw new BadRequestException('Произошла ошибка при выполнении операции');
  }
}
