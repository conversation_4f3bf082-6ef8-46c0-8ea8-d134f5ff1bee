import {
  CanActivate,
  ExecutionContext,
  Injectable,
  ForbiddenException,
} from '@nestjs/common';
import { PrismaService } from 'src/prisma.service';

@Injectable()
export class ParentOwnershipGuard implements CanActivate {
  constructor(private prisma: PrismaService) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    const user = request.user; // Пользователь из JWT токена
    const childId = request.params.id; // ID ребёнка из маршрута
    console.log(user);
    // Проверяем, является ли пользователь админом или суперадмином
    if (user.role === 'admin' || user.role === 'superadmin') {
      return true;
    }

    const child = await this.prisma.child.findFirst({
      where: {
        id: childId,
        parentIds: { has: user.id }, // Проверяем связь ребёнка и родителя
      },
    });

    if (!child) {
      throw new ForbiddenException(
        `У вас нет доступа к данным ребёнка с ID ${childId}`,
      );
    }

    return true;
  }
}
