import { Injectable, NotFoundException, Logger } from '@nestjs/common';
import { PrismaService } from 'src/prisma.service';
import * as fs from 'fs';
import * as path from 'path';

@Injectable()
export class ScheduleService {
  private readonly logger = new Logger(ScheduleService.name);
  private readonly uploadDir = path.join(process.cwd(), 'uploads', 'schedules');

  constructor(private prisma: PrismaService) {
    if (!fs.existsSync(this.uploadDir)) {
      fs.mkdirSync(this.uploadDir, { recursive: true });
    }
  }

  async uploadFile(schoolId: string, ageGroupId: string, file: Express.Multer.File) {
    try {
      const fileName = `school-${schoolId}-${ageGroupId}.pdf`;
      const filePath = path.join(this.uploadDir, fileName);
      fs.writeFileSync(filePath, file.buffer);

      const fileUrl = `/uploads/schedules/${fileName}`;

      // Сначала проверяем, существует ли запись
      const schedules = await this.prisma.schedule.findMany({
        where: { schoolId }
      });

      // Фильтруем в памяти по ageGroupId
      const matchingSchedule = schedules.find(s => s['ageGroupId'] === ageGroupId);

      if (matchingSchedule) {
        // Обновляем существующую запись
        return await this.prisma.schedule.update({
          where: { id: matchingSchedule.id },
          data: { fileUrl }
        });
      } else {
        // Создаем новую запись
        // Создаем объект данных вручную
        const data = {
          schoolId,
          fileUrl
        };

        // Добавляем ageGroupId в объект данных
        Object.defineProperty(data, 'ageGroupId', {
          value: ageGroupId,
          enumerable: true,
          configurable: true,
          writable: true
        });

        // Создаем запись с подготовленными данными
        return await this.prisma.schedule.create({ data });
      }
    } catch (error) {
      this.logger.error(
        `Ошибка при загрузке файла для школы ${schoolId} и возрастной группы ${ageGroupId}:`,
        error.stack,
      );
      throw error;
    }
  }

  async getSchedule(schoolId: string, ageGroupId?: string) {
    try {
      // Если возрастная группа не указана, используем группу по умолчанию
      const targetAgeGroupId = ageGroupId || 'children_3_6';

      const schedules = await this.prisma.schedule.findMany({
        where: {
          schoolId
        },
      });

      // Фильтруем в памяти по ageGroupId
      const schedule = schedules.find(s => s['ageGroupId'] === targetAgeGroupId);

      if (!schedule) throw new NotFoundException(`Расписание для возрастной группы ${targetAgeGroupId} не найдено`);
      return schedule;
    } catch (error) {
      if (error instanceof NotFoundException) throw error;
      this.logger.error(
        `Ошибка при получении расписания для школы ${schoolId}:`,
        error.stack,
      );
      throw error;
    }
  }

  async getAllSchedules(schoolId: string) {
    try {
      const schedules = await this.prisma.schedule.findMany({
        where: { schoolId }
      });
      return schedules;
    } catch (error) {
      this.logger.error(
        `Ошибка при получении всех расписаний для школы ${schoolId}:`,
        error.stack,
      );
      throw error;
    }
  }

  async getScheduleByChildId(childId: string) {
    try {
      // Получаем информацию о ребенке, включая группу
      const child = await this.prisma.child.findUnique({
        where: { id: childId },
        select: {
          schoolId: true,
          group: {
            select: {
              id: true,
              name: true,
              ageGroupId: true
            }
          }
        }
      });

      if (!child) {
        throw new NotFoundException(`Ребенок с ID ${childId} не найден`);
      }

      // Получаем ageGroupId группы ребенка
      const ageGroupId = child.group?.ageGroupId || 'children_3_6';

      // Получаем расписание для школы и возрастной группы ребенка
      return this.getSchedule(child.schoolId, ageGroupId);
    } catch (error) {
      if (error instanceof NotFoundException) throw error;
      this.logger.error(
        `Ошибка при получении расписания для ребенка ${childId}:`,
        error.stack,
      );
      throw error;
    }
  }

  async deleteSchedule(schoolId: string, ageGroupId?: string) {
    try {
      if (ageGroupId) {
        // Удаляем конкретное расписание для указанной возрастной группы
        const filePath = path.join(this.uploadDir, `school-${schoolId}-${ageGroupId}.pdf`);

        if (fs.existsSync(filePath)) {
          fs.unlinkSync(filePath);
        }

        // Найдем сначала расписания, соответствующие критериям
        const schedulesToDelete = await this.prisma.schedule.findMany({
          where: {
            schoolId,
            // Используем строковое сравнение для поля ageGroupId
          },
        });

        // Фильтруем расписания по ageGroupId в памяти
        const filteredSchedules = schedulesToDelete.filter(s => s['ageGroupId'] === ageGroupId);

        // Удаляем каждое расписание по ID
        for (const schedule of filteredSchedules) {
          await this.prisma.schedule.delete({
            where: { id: schedule.id },
          });
        }

        return { message: `Расписание для возрастной группы ${ageGroupId} удалено` };
      } else {
        // Удаляем все расписания для школы
        const schedules = await this.prisma.schedule.findMany({
          where: { schoolId },
        });

        // Удаляем файлы
        for (const schedule of schedules) {
          // Получаем имя файла из URL
          const fileName = schedule.fileUrl.split('/').pop();
          const filePath = path.join(this.uploadDir, fileName);
          if (fs.existsSync(filePath)) {
            fs.unlinkSync(filePath);
          }
        }

        // Удаляем записи из базы данных
        await this.prisma.schedule.deleteMany({
          where: { schoolId },
        });
        return { message: 'Все расписания удалены' };
      }
    } catch (error) {
      this.logger.error(
        `Ошибка при удалении расписания для школы ${schoolId}:`,
        error.stack,
      );
      throw error;
    }
  }
}
