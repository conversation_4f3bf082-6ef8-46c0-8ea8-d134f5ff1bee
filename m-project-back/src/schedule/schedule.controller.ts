import {
  Controller,
  Get,
  Post,
  Delete,
  Param,
  Query,
  Body,
  UploadedFile,
  UseInterceptors,
  Res,
  MaxFileSizeValidator,
  ParseFilePipe,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { ScheduleService } from './schedule.service';
import { Response } from 'express';
import * as path from 'path';
import * as fs from 'fs';
import type { Express } from 'express';
import { multerConfig } from '../common/multer.config';

@Controller('schedule')
export class ScheduleController {
  constructor(private readonly scheduleService: ScheduleService) {}

  @Post('upload/:schoolId')
  @UseInterceptors(FileInterceptor('file', multerConfig))
  uploadFile(
    @Param('schoolId') schoolId: string,
    @Body('ageGroupId') ageGroupId: string = 'children_3_6',
    @UploadedFile(
      new ParseFilePipe({
        validators: [
          new MaxFileSizeValidator({ maxSize: 15 * 1024 * 1024 }), // 15 MB
        ],
        fileIsRequired: true,
      }),
    ) file: Express.Multer.File,
  ) {
    return this.scheduleService.uploadFile(schoolId, ageGroupId, file);
  }

  @Get('child/:childId')
  getScheduleByChildId(@Param('childId') childId: string) {
    return this.scheduleService.getScheduleByChildId(childId);
  }

  @Get(':schoolId')
  getSchedule(
    @Param('schoolId') schoolId: string,
    @Query('ageGroupId') ageGroupId?: string,
  ) {
    return this.scheduleService.getSchedule(schoolId, ageGroupId);
  }

  @Get('all/:schoolId')
  getAllSchedules(@Param('schoolId') schoolId: string) {
    return this.scheduleService.getAllSchedules(schoolId);
  }

  @Get('download/:schoolId')
  async downloadFile(
    @Param('schoolId') schoolId: string,
    @Query('ageGroupId') ageGroupId: string = 'children_3_6',
    @Res() res: Response,
  ) {
    const filePath = path.join(
      __dirname,
      '..',
      '..',
      'uploads',
      'schedules',
      `school-${schoolId}-${ageGroupId}.pdf`,
    );

    if (!fs.existsSync(filePath)) {
      return res.status(404).json({ message: 'Файл не найден' });
    }

    return res.sendFile(filePath);
  }

  @Delete(':schoolId')
  deleteSchedule(
    @Param('schoolId') schoolId: string,
    @Query('ageGroupId') ageGroupId?: string,
  ) {
    return this.scheduleService.deleteSchedule(schoolId, ageGroupId);
  }
}
