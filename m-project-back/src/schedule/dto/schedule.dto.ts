import { IsString, IsOptional } from 'class-validator';

export class ScheduleDto {
  @IsString()
  schoolId: string;

  @IsString()
  @IsOptional()
  ageGroupId?: string;

  @IsString()
  fileUrl: string;
}

export class CreateScheduleDto {
  @IsString()
  schoolId: string;

  @IsString()
  @IsOptional()
  ageGroupId?: string;

  @IsString()
  fileUrl: string;
}

export class UpdateScheduleDto {
  @IsString()
  @IsOptional()
  fileUrl?: string;

  @IsString()
  @IsOptional()
  ageGroupId?: string;
}
