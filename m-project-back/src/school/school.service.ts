import {
  Injectable,
  NotFoundException,
  ConflictException,
  Logger,
} from '@nestjs/common';
import { PrismaService } from 'src/prisma.service';
import { School } from '@prisma/client';
import { CreateSchoolDto } from './dto/create-school.dto';
import { UpdateSchoolDto } from './dto/update-school.dto';

@Injectable()
export class SchoolService {
  private readonly logger = new Logger(SchoolService.name);

  constructor(private prisma: PrismaService) {}

  async create(dto: CreateSchoolDto): Promise<School> {
    try {
      return await this.prisma.school.create({
        data: {
          name: dto.name,
          ownerId: dto.ownerId,
          address: dto.address,
          description: dto.description,
          contact: dto.contact,
          logoUrl: dto.logoUrl,
          location: dto.location,
        },
      });
    } catch (error) {
      this.handlePrismaError(error, 'create');
    }
  }

  async findAll(): Promise<School[]> {
    return this.prisma.school.findMany({
      where: { isDeleted: false },
    });
  }

  async findOne(id: string): Promise<School> {
    const school = await this.prisma.school.findUnique({
      where: { id },
    });
    if (!school) {
      throw new NotFoundException(`Школа с ID ${id} не найдена`);
    }
    return school;
  }

  async update(id: string, dto: UpdateSchoolDto): Promise<School> {
    await this.findOne(id); // Проверяем существование школы

    try {
      return await this.prisma.school.update({
        where: { id },
        data: {
          ...dto,
          updatedAt: new Date(),
        },
      });
    } catch (error) {
      this.handlePrismaError(error, 'update');
    }
  }

  async softDelete(id: string): Promise<School> {
    await this.findOne(id); // Проверяем существование школы

    return this.prisma.school.update({
      where: { id },
      data: {
        isDeleted: true,
      },
    });
  }

  private handlePrismaError(error: any, operation: string) {
    if (error.code === 'P2002') {
      throw new ConflictException('Школа с таким названием уже существует');
    }
    this.logger.error(`Ошибка в операции ${operation}:`, error.stack);
    throw error;
  }
}
