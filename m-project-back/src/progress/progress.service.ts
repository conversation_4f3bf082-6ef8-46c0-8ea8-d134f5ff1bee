import {
  Injectable,
  Logger,
  NotFoundException,
  ConflictException,
  BadRequestException,
} from '@nestjs/common';
import { PrismaService } from 'src/prisma.service';
import { Progress } from '@prisma/client';
import { CreateProgressDto } from './dto/create-progress.dto';
import { UpdateProgressDto } from './dto/update-progress.dto';

@Injectable()
export class ProgressService {
  private readonly logger = new Logger(ProgressService.name);

  constructor(private prisma: PrismaService) {}

  async create(dto: CreateProgressDto): Promise<Progress> {
    try {
      const progress = await this.prisma.progress.create({
        data: {
          childId: dto.childId,
          skillId: dto.skillId,
          status: dto.status,
        },
      });
      this.logger.log(`Прогресс создан: ID ${progress.id}`);
      return progress;
    } catch (error) {
      this.handlePrismaError(error, 'create');
    }
  }

  async findAll(): Promise<Progress[]> {
    try {
      return await this.prisma.progress.findMany();
    } catch (error) {
      this.handlePrismaError(error, 'findAll');
    }
  }

  async findOne(id: string): Promise<Progress> {
    try {
      const progress = await this.prisma.progress.findUnique({
        where: { id },
      });
      if (!progress) {
        throw new NotFoundException(`Прогресс с ID ${id} не найден`);
      }
      return progress;
    } catch (error) {
      this.handlePrismaError(error, 'findOne');
    }
  }

  async update(id: string, dto: UpdateProgressDto): Promise<Progress> {
    await this.findOne(id);

    try {
      const progress = await this.prisma.progress.update({
        where: { id },
        data: dto,
      });
      this.logger.log(`Прогресс обновлен: ID ${progress.id}`);
      return progress;
    } catch (error) {
      this.handlePrismaError(error, 'update');
    }
  }

  async remove(id: string): Promise<Progress> {
    await this.findOne(id);

    try {
      const progress = await this.prisma.progress.delete({
        where: { id },
      });
      this.logger.log(`Прогресс удален: ID ${progress.id}`);
      return progress;
    } catch (error) {
      this.handlePrismaError(error, 'remove');
    }
  }

  private handlePrismaError(error: any, operation: string) {
    if (error.code === 'P2002') {
      throw new ConflictException(
        'Нарушено уникальное ограничение. Проверьте данные и попробуйте снова.',
      );
    }

    if (error.code === 'P2025') {
      throw new NotFoundException(
        'Запись для обновления или удаления не найдена',
      );
    }

    this.logger.error(`Ошибка в операции ${operation}:`, error.stack);
    throw new BadRequestException('Произошла ошибка при выполнении операции');
  }
}
