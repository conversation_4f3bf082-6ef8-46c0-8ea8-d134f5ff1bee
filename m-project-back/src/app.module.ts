// src/app.module.ts
import { Module } from '@nestjs/common';
import { PrismaService } from './prisma.service';
import { UserModule } from './user/user.module';
import { SchoolModule } from './school/school.module';
import { ChildrenModule } from './children/children.module';
import { GroupsModule } from './groups/groups.module';
import { NotesModule } from './notes/notes.module';
import { SkillsModule } from './skills/skills.module';
import { AttendanceModule } from './attendance/attendance.module';
import { FeedModule } from './feed/feed.module';
import { ReportsModule } from './reports/reports.module';
import { ProgressModule } from './progress/progress.module';
import { SkillsCategoryModule } from './skills-category/skills-category.module';
import { MenuModule } from './menu/menu.module';
import { PhotosModule } from './photos/photos.module';
import { AuthModule } from './auth/auth.module';
import { ScheduleModule } from './schedule/schedule.module';
import { CalendarModule } from './calendar/calendar.module';
import { ServeStaticModule } from '@nestjs/serve-static';
import { join } from 'path';

@Module({
  imports: [
    UserModule,
    SchoolModule,
    ChildrenModule,
    GroupsModule,
    NotesModule,
    SkillsModule,
    SkillsCategoryModule,
    AttendanceModule,
    ProgressModule,
    FeedModule,
    ReportsModule,
    MenuModule,
    PhotosModule,
    AuthModule,
    ScheduleModule,
    CalendarModule,
    ServeStaticModule.forRoot({
      rootPath: join(process.cwd(), 'uploads'),
      serveRoot: '/uploads',
      exclude: ['/api*'],
      serveStaticOptions: {
        index: false,
      },
    }),
  ],
  providers: [PrismaService],
})
export class AppModule {}
