import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { json, urlencoded } from 'express';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);

  // Увеличиваем лимит размера запроса для Express
  app.use(json({ limit: "15mb" }));
  app.use(urlencoded({ limit: "15mb", extended: true }));

  app.setGlobalPrefix('api');

  const corsOrigins = [
    process.env.CORS_ORIGIN || 'http://*************:3000',
    'https://m-project-telegram.vercel.app',
    'http://localhost:3000',
    'http://mymontessory.ru',
    'http://www.mymontessory.ru',
  ];

  app.enableCors({
    origin: corsOrigins,
    methods: ['GET', 'POST', 'PUT', 'PATCH', 'DELETE', 'OPTIONS'],
    credentials: true,
    allowedHeaders: [
      'Content-Type',
      'Authorization',
      'Accept',
      'X-Telegram-Init-Data',
    ],
    exposedHeaders: ['Authorization'],
  });

  await app.listen(process.env.PORT || 3002, '0.0.0.0');
  console.log(
    `Server is running on http://localhost:${process.env.PORT || 3002}`,
  );
}

bootstrap();
