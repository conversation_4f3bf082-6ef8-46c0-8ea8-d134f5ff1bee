import {
  Injectable,
  Logger,
  NotFoundException,
  ConflictException,
  BadRequestException,
} from '@nestjs/common';
import { PrismaService } from 'src/prisma.service';
import { Skill } from '@prisma/client';
import { CreateSkillDto } from './dto/create-skill.dto';
import { UpdateSkillDto } from './dto/update-skill.dto';

@Injectable()
export class SkillsService {
  private readonly logger = new Logger(SkillsService.name);

  constructor(private prisma: PrismaService) {}

  async create(dto: CreateSkillDto): Promise<Skill> {
    try {
      const skill = await this.prisma.skill.create({
        data: {
          name: dto.name,
          description: dto.description,
          category: dto.category,
          parentSkillId: dto.parentSkillId,
        },
      });
      this.logger.log(`Навык создан: ID ${skill.id}`);
      return skill;
    } catch (error) {
      this.handlePrismaError(error, 'create');
    }
  }

  async findAll(): Promise<Skill[]> {
    try {
      return await this.prisma.skill.findMany();
    } catch (error) {
      this.handlePrismaError(error, 'findAll');
    }
  }

  async findOne(id: string): Promise<Skill> {
    try {
      const skill = await this.prisma.skill.findUnique({
        where: { id },
      });
      if (!skill) {
        throw new NotFoundException(`Навык с ID ${id} не найден`);
      }
      return skill;
    } catch (error) {
      this.handlePrismaError(error, 'findOne');
    }
  }

  async update(id: string, dto: UpdateSkillDto): Promise<Skill> {
    await this.findOne(id);

    try {
      const skill = await this.prisma.skill.update({
        where: { id },
        data: dto,
      });
      this.logger.log(`Навык обновлен: ID ${skill.id}`);
      return skill;
    } catch (error) {
      this.handlePrismaError(error, 'update');
    }
  }

  async remove(id: string): Promise<Skill> {
    await this.findOne(id);

    try {
      const skill = await this.prisma.skill.delete({
        where: { id },
      });
      this.logger.log(`Навык удален: ID ${skill.id}`);
      return skill;
    } catch (error) {
      this.handlePrismaError(error, 'remove');
    }
  }

  private handlePrismaError(error: any, operation: string) {
    if (error.code === 'P2002') {
      throw new ConflictException(
        'Нарушено уникальное ограничение. Проверьте данные и попробуйте снова.',
      );
    }

    if (error.code === 'P2025') {
      throw new NotFoundException(
        'Запись для обновления или удаления не найдена',
      );
    }

    this.logger.error(`Ошибка в операции ${operation}:`, error.stack);
    throw new BadRequestException('Произошла ошибка при выполнении операции');
  }
}
