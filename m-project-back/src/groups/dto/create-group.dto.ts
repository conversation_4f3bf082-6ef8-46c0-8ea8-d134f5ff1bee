import { IsString, <PERSON>Array, IsOptional } from 'class-validator';

export class CreateGroupDto {
  @IsString()
  name: string;

  @IsString()
  schoolId: string;

  @IsString()
  @IsOptional()
  ageGroupId?: string; // Новое поле для связи с возрастной группой

  @IsArray()
  @IsString({ each: true })
  teacherIds: string[]; // Массив UUID учителей

  @IsString()
  @IsOptional()
  description?: string; // Необязательное поле
}
