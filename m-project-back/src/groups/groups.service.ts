import {
  Injectable,
  Logger,
  NotFoundException,
  ConflictException,
  BadRequestException,
} from '@nestjs/common';
import { PrismaService } from 'src/prisma.service';
import { Group } from '@prisma/client';
import { CreateGroupDto } from './dto/create-group.dto';
import { UpdateGroupDto } from './dto/update-group.dto';

@Injectable()
export class GroupsService {
  private readonly logger = new Logger(GroupsService.name);

  constructor(private prisma: PrismaService) {}

  async create(dto: CreateGroupDto): Promise<Group> {
    try {
      const group = await this.prisma.group.create({
        data: dto,
      });
      this.logger.log(`Группа создана: ID ${group.id}`);
      return group;
    } catch (error) {
      this.handlePrismaError(error, 'create');
    }
  }

  async findOne(id: string): Promise<Group> {
    try {
      const group = await this.prisma.group.findUnique({
        where: { id },
      });
      if (!group) {
        throw new NotFoundException(`Группа с ID ${id} не найдена`);
      }
      return group;
    } catch (error) {
      this.handlePrismaError(error, 'findOne');
    }
  }

  async update(id: string, dto: UpdateGroupDto): Promise<Group> {
    await this.findOne(id);

    try {
      const group = await this.prisma.group.update({
        where: { id },
        data: dto,
      });
      this.logger.log(`Группа обновлена: ID ${group.id}`);
      return group;
    } catch (error) {
      this.handlePrismaError(error, 'update');
    }
  }

  async findAll(): Promise<Group[]> {
    try {
      return await this.prisma.group.findMany({
        where: { isDeleted: false },
      });
    } catch (error) {
      this.handlePrismaError(error, 'findAll');
    }
  }

  async findByAgeGroup(ageGroupId: string): Promise<Group[]> {
    try {
      return await this.prisma.group.findMany({
        where: {
          isDeleted: false,
          ageGroupId: ageGroupId
        },
      });
    } catch (error) {
      this.handlePrismaError(error, 'findByAgeGroup');
    }
  }

  async softDelete(id: string): Promise<Group> {
    await this.findOne(id);

    try {
      const group = await this.prisma.group.update({
        where: { id },
        data: { isDeleted: true },
      });
      this.logger.log(`Группа удалена: ID ${group.id}`);
      return group;
    } catch (error) {
      this.handlePrismaError(error, 'softDelete');
    }
  }

  private handlePrismaError(error: any, operation: string) {
    if (error.code === 'P2002') {
      throw new ConflictException(
        'Нарушено уникальное ограничение. Проверьте данные и попробуйте снова.',
      );
    }

    if (error.code === 'P2025') {
      throw new NotFoundException(
        'Запись для обновления или удаления не найдена',
      );
    }

    this.logger.error(`Ошибка в операции ${operation}:`, error.stack);
    throw new BadRequestException('Произошла ошибка при выполнении операции');
  }
}
