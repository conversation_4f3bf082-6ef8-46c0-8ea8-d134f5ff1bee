import {
  Controller,
  Get,
  Post,
  Delete,
  Param,
  UseInterceptors,
  UploadedFile,
  ParseFilePipe,
  MaxFileSizeValidator,
  Logger,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { CalendarService } from './calendar.service';
import { multerConfig } from 'src/common/multer.config';

@Controller('calendar')
export class CalendarController {
  private readonly logger = new Logger(CalendarController.name);

  constructor(private readonly calendarService: CalendarService) {}

  @Post('upload/:schoolId')
  @UseInterceptors(FileInterceptor('file', multerConfig))
  uploadFile(
    @Param('schoolId') schoolId: string,
    @UploadedFile(
      new ParseFilePipe({
        validators: [
          new MaxFileSizeValidator({ maxSize: 15 * 1024 * 1024 }), // 15 MB
        ],
        fileIsRequired: true,
      }),
    ) file: Express.Multer.File,
  ) {
    return this.calendarService.uploadFile(schoolId, file);
  }

  @Get(':schoolId')
  getCalendar(@Param('schoolId') schoolId: string) {
    return this.calendarService.getCalendar(schoolId);
  }

  @Delete(':schoolId')
  deleteCalendar(@Param('schoolId') schoolId: string) {
    return this.calendarService.deleteCalendar(schoolId);
  }
}
