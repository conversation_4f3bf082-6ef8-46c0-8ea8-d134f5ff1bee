import { Injectable, NotFoundException, Logger } from '@nestjs/common';
import { PrismaService } from 'src/prisma.service';
import * as fs from 'fs';
import * as path from 'path';

@Injectable()
export class CalendarService {
  private readonly logger = new Logger(CalendarService.name);
  private readonly uploadDir = path.join(process.cwd(), 'uploads', 'calendars');

  constructor(private prisma: PrismaService) {
    if (!fs.existsSync(this.uploadDir)) {
      fs.mkdirSync(this.uploadDir, { recursive: true });
    }
  }

  async uploadFile(schoolId: string, file: Express.Multer.File) {
    try {
      const filePath = path.join(this.uploadDir, `school-${schoolId}.pdf`);
      fs.writeFileSync(filePath, file.buffer);

      const fileUrl = `/uploads/calendars/school-${schoolId}.pdf`;

      return await this.prisma.calendar.upsert({
        where: { schoolId },
        update: { fileUrl },
        create: { schoolId, fileUrl },
      });
    } catch (error) {
      this.logger.error(
        `Ошибка при загрузке календаря для школы ${schoolId}:`,
        error.stack,
      );
      throw error;
    }
  }

  async getCalendar(schoolId: string) {
    try {
      const calendar = await this.prisma.calendar.findUnique({
        where: { schoolId },
      });
      if (!calendar) throw new NotFoundException('Календарь не найден');
      return calendar;
    } catch (error) {
      if (error instanceof NotFoundException) throw error;
      this.logger.error(
        `Ошибка при получении календаря для школы ${schoolId}:`,
        error.stack,
      );
      throw error;
    }
  }

  async deleteCalendar(schoolId: string) {
    try {
      const filePath = path.join(this.uploadDir, `school-${schoolId}.pdf`);

      if (fs.existsSync(filePath)) {
        fs.unlinkSync(filePath);
      }

      await this.prisma.calendar.delete({ where: { schoolId } });
      return { message: 'Календарь удален' };
    } catch (error) {
      this.logger.error(
        `Ошибка при удалении календаря для школы ${schoolId}:`,
        error.stack,
      );
      throw error;
    }
  }
}
