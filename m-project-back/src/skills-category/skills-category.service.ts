import { Injectable } from '@nestjs/common';
import { SKILL_CATEGORIES } from './constants';
import { AGE_GROUPS } from './age-groups';

@Injectable()
export class SkillsCategoryService {
  private readonly skillCategories = SKILL_CATEGORIES;
  private readonly ageGroups = AGE_GROUPS;

  getAllCategories() {
    return this.skillCategories;
  }

  getCategoryById(id: string) {
    return this.skillCategories.find((category) => category.id === id);
  }

  getAllAgeGroups() {
    return this.ageGroups;
  }

  getAgeGroupById(id: string) {
    return this.ageGroups.find((group) => group.id === id);
  }

  getCategoriesByAgeGroup(ageGroupId: string) {
    const ageGroup = this.getAgeGroupById(ageGroupId);
    return ageGroup ? ageGroup.skillCategories : [];
  }
}
