import { Controller, Get, Param } from '@nestjs/common';
import { SkillsCategoryService } from './skills-category.service';

@Controller('skills-category')
export class SkillsCategoryController {
  constructor(private readonly skillCategoryService: SkillsCategoryService) {}

  @Get()
  getAllCategories() {
    return this.skillCategoryService.getAllCategories();
  }

  @Get(':id')
  getCategoryById(@Param('id') id: string) {
    return this.skillCategoryService.getCategoryById(id);
  }

  @Get('age-groups/all')
  getAllAgeGroups() {
    return this.skillCategoryService.getAllAgeGroups();
  }

  @Get('age-groups/:id')
  getAgeGroupById(@Param('id') id: string) {
    return this.skillCategoryService.getAgeGroupById(id);
  }

  @Get('by-age-group/:ageGroupId')
  getCategoriesByAgeGroup(@Param('ageGroupId') ageGroupId: string) {
    return this.skillCategoryService.getCategoriesByAgeGroup(ageGroupId);
  }
}
