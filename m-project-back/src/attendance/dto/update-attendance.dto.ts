import { IsDate, IsOptional, IsString, IsUUID } from 'class-validator';

export class UpdateAttendanceDto {
  @IsOptional()
  @IsUUID()
  childId?: string;

  @IsOptional()
  @IsDate()
  date?: Date;

  @IsOptional()
  @IsString()
  status?: string;

  @IsOptional()
  @IsDate()
  checkInTime?: Date;

  @IsOptional()
  @IsDate()
  checkOutTime?: Date;

  @IsOptional()
  @IsString()
  schoolId?: string;
}
