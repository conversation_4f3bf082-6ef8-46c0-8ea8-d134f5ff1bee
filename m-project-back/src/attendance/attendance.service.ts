import {
  Injectable,
  Logger,
  NotFoundException,
  ConflictException,
  BadRequestException,
} from '@nestjs/common';
import { PrismaService } from 'src/prisma.service';
import { Attendance } from '@prisma/client';
import { CreateAttendanceDto } from './dto/create-attendance.dto';
import { UpdateAttendanceDto } from './dto/update-attendance.dto';

@Injectable()
export class AttendanceService {
  private readonly logger = new Logger(AttendanceService.name);

  constructor(private prisma: PrismaService) {}

  async create(createAttendanceDto: CreateAttendanceDto): Promise<Attendance> {
    try {
      const attendance = await this.prisma.attendance.create({
        data: {
          childId: createAttendanceDto.childId,
          schoolId: createAttendanceDto.schoolId,
          date: createAttendanceDto.date,
          status: createAttendanceDto.status,
          checkInTime: createAttendanceDto.checkInTime,
          checkOutTime: createAttendanceDto.checkOutTime,
        },
      });
      this.logger.log(`Посещаемость создана: ID ${attendance.id}`);
      return attendance;
    } catch (error) {
      this.handlePrismaError(error, 'create');
    }
  }

  async findAll(childId: string): Promise<Attendance[]> {
    try {
      return await this.prisma.attendance.findMany({
        where: {
          childId,
        },
      });
    } catch (error) {
      this.handlePrismaError(error, 'findAll');
    }
  }

  async findOne(id: string): Promise<Attendance> {
    try {
      const attendance = await this.prisma.attendance.findFirst({
        where: {
          id,
        },
      });
      if (!attendance) {
        throw new NotFoundException(`Посещаемость с ID ${id} не найдена`);
      }
      return attendance;
    } catch (error) {
      this.handlePrismaError(error, 'findOne');
    }
  }

  async update(id: string, dto: UpdateAttendanceDto): Promise<Attendance> {
    await this.findOne(id);

    try {
      const attendance = await this.prisma.attendance.update({
        where: { id },
        data: dto,
      });
      this.logger.log(`Посещаемость обновлена: ID ${attendance.id}`);
      return attendance;
    } catch (error) {
      this.handlePrismaError(error, 'update');
    }
  }

  async remove(id: string): Promise<Attendance> {
    await this.findOne(id);

    try {
      const attendance = await this.prisma.attendance.delete({
        where: { id },
      });
      this.logger.log(`Посещаемость удалена: ID ${attendance.id}`);
      return attendance;
    } catch (error) {
      this.handlePrismaError(error, 'remove');
    }
  }

  private handlePrismaError(error: any, operation: string) {
    if (error.code === 'P2002') {
      throw new ConflictException(
        'Нарушено уникальное ограничение. Проверьте данные и попробуйте снова.',
      );
    }

    if (error.code === 'P2025') {
      throw new NotFoundException(
        'Запись для обновления или удаления не найдена',
      );
    }

    this.logger.error(`Ошибка в операции ${operation}:`, error.stack);
    throw new BadRequestException('Произошла ошибка при выполнении операции');
  }
}
