import { IsOptional, IsString, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>y } from 'class-validator';

export class UpdatePhotoDto {
  @IsOptional()
  @IsUUID()
  schoolId?: string;

  @IsOptional()
  @IsString()
  url?: string;

  @IsOptional()
  @IsString()
  description?: string;

  @IsOptional()
  @IsUUID()
  uploadedBy?: string;

  @IsOptional()
  @IsArray()
  @IsUUID(undefined, { each: true })
  childIds?: string[];
}
