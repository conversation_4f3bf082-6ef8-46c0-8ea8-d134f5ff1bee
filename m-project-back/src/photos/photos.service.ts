import {
  Injectable,
  Logger,
  NotFoundException,
  ConflictException,
  BadRequestException,
} from '@nestjs/common';
import { PrismaService } from 'src/prisma.service';
import { Photo } from '@prisma/client';
import { CreatePhotoDto } from './dto/create-photo.dto';
import { UpdatePhotoDto } from './dto/update-photo.dto';

@Injectable()
export class PhotoService {
  private readonly logger = new Logger(PhotoService.name);

  constructor(private prisma: PrismaService) {}

  async create(dto: CreatePhotoDto): Promise<Photo> {
    try {
      const photo = await this.prisma.photo.create({
        data: {
          schoolId: dto.schoolId,
          url: dto.url,
          description: dto.description,
          uploadedBy: dto.uploadedBy,
          children: {
            connect: dto.childIds.map((id) => ({ id })),
          },
        },
      });
      this.logger.log(`Фото создано: ID ${photo.id}`);
      return photo;
    } catch (error) {
      this.handlePrismaError(error, 'create');
    }
  }

  async findAll(): Promise<any[]> {
    try {
      const photos = await this.prisma.photo.findMany({
        select: {
          id: true,
          schoolId: true,
          url: true,
          description: true,
          uploadedBy: true,
          createdAt: true,
          updatedAt: true,
          children: {
            select: {
              id: true,
            },
          },
        },
      });
      return photos.map((photo) => ({
        ...photo,
        children: photo.children.map((child) => child.id),
      }));
    } catch (error) {
      this.handlePrismaError(error, 'findAll');
    }
  }

  async findOne(id: string): Promise<any> {
    try {
      const photo = await this.prisma.photo.findUnique({
        where: { id },
        select: {
          id: true,
          schoolId: true,
          url: true,
          description: true,
          uploadedBy: true,
          createdAt: true,
          updatedAt: true,
          children: {
            select: {
              id: true,
            },
          },
        },
      });

      if (!photo) {
        throw new NotFoundException(`Фото с ID ${id} не найдено`);
      }

      return {
        ...photo,
        children: photo.children.map((child) => child.id),
      };
    } catch (error) {
      this.handlePrismaError(error, 'findOne');
    }
  }

  async update(id: string, dto: UpdatePhotoDto): Promise<any> {
    await this.findOne(id);

    try {
      const updatedPhoto = await this.prisma.photo.update({
        where: { id },
        data: {
          ...dto,
          children: dto.childIds
            ? {
                set: dto.childIds.map((id) => ({ id })),
              }
            : undefined,
        },
        select: {
          id: true,
          schoolId: true,
          url: true,
          description: true,
          uploadedBy: true,
          createdAt: true,
          updatedAt: true,
          children: {
            select: {
              id: true,
            },
          },
        },
      });
      this.logger.log(`Фото обновлено: ID ${updatedPhoto.id}`);
      return {
        ...updatedPhoto,
        children: updatedPhoto.children.map((child) => child.id),
      };
    } catch (error) {
      this.handlePrismaError(error, 'update');
    }
  }

  async remove(id: string): Promise<any> {
    await this.findOne(id);

    try {
      const photo = await this.prisma.photo.delete({
        where: { id },
        select: {
          id: true,
          schoolId: true,
          url: true,
          description: true,
          uploadedBy: true,
          createdAt: true,
          updatedAt: true,
        },
      });
      this.logger.log(`Фото удалено: ID ${photo.id}`);
      return photo;
    } catch (error) {
      this.handlePrismaError(error, 'remove');
    }
  }

  private handlePrismaError(error: any, operation: string) {
    if (error.code === 'P2002') {
      throw new ConflictException(
        'Нарушено уникальное ограничение. Проверьте данные и попробуйте снова.',
      );
    }

    if (error.code === 'P2025') {
      throw new NotFoundException(
        'Запись для обновления или удаления не найдена',
      );
    }

    this.logger.error(`Ошибка в операции ${operation}:`, error.stack);
    throw new BadRequestException('Произошла ошибка при выполнении операции');
  }
}
