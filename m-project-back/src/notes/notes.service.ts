import {
  Injectable,
  Logger,
  NotFoundException,
  ConflictException,
  BadRequestException,
} from '@nestjs/common';
import { PrismaService } from 'src/prisma.service';
import { Note } from '@prisma/client';
import { CreateNoteDto } from './dto/create-note.dto';
import { UpdateNoteDto } from './dto/update-note.dto';

@Injectable()
export class NotesService {
  private readonly logger = new Logger(NotesService.name);

  constructor(private prisma: PrismaService) {}

  async create(dto: CreateNoteDto): Promise<Note> {
    try {
      const note = await this.prisma.note.create({
        data: {
          childId: dto.childId,
          authorId: dto.authorId,
          content: dto.content,
        },
      });
      this.logger.log(`Заметка создана: ID ${note.id}`);
      return note;
    } catch (error) {
      this.handlePrismaError(error, 'create');
    }
  }

  async findAll(): Promise<Note[]> {
    try {
      return await this.prisma.note.findMany();
    } catch (error) {
      this.handlePrismaError(error, 'findAll');
    }
  }

  async findOne(id: string): Promise<Note> {
    try {
      const note = await this.prisma.note.findUnique({
        where: { id },
      });
      if (!note) {
        throw new NotFoundException(`Заметка с ID ${id} не найдена`);
      }
      return note;
    } catch (error) {
      this.handlePrismaError(error, 'findOne');
    }
  }

  async update(id: string, dto: UpdateNoteDto): Promise<Note> {
    await this.findOne(id);

    try {
      const note = await this.prisma.note.update({
        where: { id },
        data: {
          ...dto,
          updatedAt: new Date(),
        },
      });
      this.logger.log(`Заметка обновлена: ID ${note.id}`);
      return note;
    } catch (error) {
      this.handlePrismaError(error, 'update');
    }
  }

  async remove(id: string): Promise<Note> {
    await this.findOne(id);

    try {
      const note = await this.prisma.note.delete({
        where: { id },
      });
      this.logger.log(`Заметка удалена: ID ${note.id}`);
      return note;
    } catch (error) {
      this.handlePrismaError(error, 'remove');
    }
  }

  private handlePrismaError(error: any, operation: string) {
    if (error.code === 'P2002') {
      throw new ConflictException(
        'Нарушено уникальное ограничение. Проверьте данные и попробуйте снова.',
      );
    }

    if (error.code === 'P2025') {
      throw new NotFoundException(
        'Запись для обновления или удаления не найдена',
      );
    }

    this.logger.error(`Ошибка в операции ${operation}:`, error.stack);
    throw new BadRequestException('Произошла ошибка при выполнении операции');
  }
}
