import { IsString, IsUUID, IsOptional } from 'class-validator';

export class CreateReportDto {
  @IsUUID()
  childId: string;

  @IsUUID()
  schoolId: string;

  @IsUUID()
  authorId: string;

  @IsString()
  @IsOptional()
  fileUrl?: string; // Это поле не обязательно, т.к. URL генерируется в сервисе

  @IsString()
  type: string; // Например: "progress", "attendance", "custom"

  @IsString()
  @IsOptional()
  name?: string; // Название отчета (необязательное поле)
}
