import {
  Injectable,
  Logger,
  NotFoundException,
  ConflictException,
  BadRequestException,
} from '@nestjs/common';
import { PrismaService } from 'src/prisma.service';
import { Report } from '@prisma/client';
import { CreateReportDto } from './dto/create-report.dto';
import { UpdateReportDto } from './dto/update-report.dto';
import { ReportResponseDto } from './dto/report-response.dto';
import * as fs from 'fs';
import * as path from 'path';

@Injectable()
export class ReportsService {
  private readonly logger = new Logger(ReportsService.name);
  private readonly uploadDir = path.join(process.cwd(), 'uploads', 'reports');

  constructor(private prisma: PrismaService) {
    if (!fs.existsSync(this.uploadDir)) {
      fs.mkdirSync(this.uploadDir, { recursive: true });
    }
  }

  async create(
    dto: CreateReportDto,
    file: Express.Multer.File,
  ): Promise<Report> {
    try {
      // Сохраняем файл и генерируем URL независимо от того, что пришло в DTO
      const fileName = `report-${Date.now()}-${file.originalname}`;
      const filePath = path.join(this.uploadDir, fileName);
      fs.writeFileSync(filePath, file.buffer);

      const fileUrl = `/uploads/reports/${fileName}`;

      const report = await this.prisma.report.create({
        data: {
          childId: dto.childId,
          schoolId: dto.schoolId,
          authorId: dto.authorId,
          fileUrl: fileUrl, // Используем сгенерированный URL, а не из DTO
          type: dto.type,
          name: dto.name, // Добавляем название отчета
        },
      });
      this.logger.log(`Отчет создан: ID ${report.id}`);
      return report;
    } catch (error) {
      this.handlePrismaError(error, 'create');
      throw error; // Добавляем явный выброс ошибки
    }
  }

  async findAll(schoolId: string): Promise<ReportResponseDto[]> {
    try {
      const reports = await this.prisma.report.findMany({
        where: {
          schoolId,
        },
        orderBy: {
          createdAt: 'desc',
        },
        select: {
          id: true,
          childId: true,
          schoolId: true,
          fileUrl: true,
          type: true,
          name: true,
          createdAt: true,
          author: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
            },
          },
          child: {
            select: {
              name: true,
            },
          },
        },
      });

      if (!reports.length) {
        this.logger.log(`Отчеты для школы с ID ${schoolId} не найдены`);
        return [];
      }

      return reports.map(
        (report): ReportResponseDto => ({
          id: report.id,
          childId: report.childId,
          schoolId: report.schoolId,
          fileUrl: report.fileUrl,
          type: report.type,
          name: report.name,
          createdAt: report.createdAt,
          author: {
            id: report.author.id,
            firstName: report.author.firstName,
            lastName: report.author.lastName,
            fullName:
              `${report.author.firstName || ''} ${report.author.lastName || ''}`.trim(),
          },
          childName: report.child.name, // Добавляем имя ребенка для удобства отображения
        }),
      );
    } catch (error) {
      this.handlePrismaError(error, 'findAll');
      throw error;
    }
  }

  async findOne(id: string, schoolId: string): Promise<Report> {
    try {
      const report = await this.prisma.report.findFirst({
        where: {
          id,
          schoolId,
        },
      });
      if (!report) {
        throw new NotFoundException(`Отчет с ID ${id} не найден`);
      }
      return report;
    } catch (error) {
      this.handlePrismaError(error, 'findOne');
      throw error;
    }
  }

  async update(id: string, dto: UpdateReportDto, file?: Express.Multer.File): Promise<Report> {
    const existingReport = await this.findOne(id, dto.schoolId);

    try {
      let fileUrl = existingReport.fileUrl;

      // Если есть новый файл, сохраняем его и обновляем URL
      if (file) {
        // Удаляем старый файл, если он существует
        const oldFileName = existingReport.fileUrl.split('/').pop();
        const oldFilePath = path.join(this.uploadDir, oldFileName);
        if (fs.existsSync(oldFilePath)) {
          fs.unlinkSync(oldFilePath);
        }

        // Сохраняем новый файл
        const fileName = `report-${Date.now()}-${file.originalname}`;
        const filePath = path.join(this.uploadDir, fileName);
        fs.writeFileSync(filePath, file.buffer);

        fileUrl = `/uploads/reports/${fileName}`;
      }

      const report = await this.prisma.report.update({
        where: { id },
        data: {
          ...dto,
          fileUrl: fileUrl, // Используем новый URL, если файл был загружен
        },
      });
      this.logger.log(`Отчет обновлен: ID ${report.id}`);
      return report;
    } catch (error) {
      this.handlePrismaError(error, 'update');
      throw error;
    }
  }

  async remove(id: string): Promise<Report> {
    // Сначала получаем отчет, чтобы узнать schoolId и fileUrl
    const report = await this.prisma.report.findUnique({
      where: { id },
    });

    if (!report) {
      throw new NotFoundException(`Отчет с ID ${id} не найден`);
    }

    try {
      // Удаляем файл
      const fileName = report.fileUrl.split('/').pop();
      const filePath = path.join(this.uploadDir, fileName);

      if (fs.existsSync(filePath)) {
        fs.unlinkSync(filePath);
      }

      const deletedReport = await this.prisma.report.delete({
        where: { id },
      });
      this.logger.log(`Отчет удален: ID ${deletedReport.id}`);
      return deletedReport;
    } catch (error) {
      this.handlePrismaError(error, 'remove');
      throw error; // Добавляем явный выброс ошибки после обработки
    }
  }

  async findByChildId(childId: string): Promise<ReportResponseDto[]> {
    try {
      const reports = await this.prisma.report.findMany({
        where: {
          childId,
        },
        orderBy: {
          createdAt: 'desc',
        },
        select: {
          id: true,
          childId: true,
          schoolId: true,
          fileUrl: true,
          type: true,
          name: true,
          createdAt: true,
          author: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
            },
          },
          child: {
            select: {
              name: true,
            },
          },
        },
      });

      if (!reports.length) {
        this.logger.log(`Отчеты для ребенка с ID ${childId} не найдены`);
        return [];
      }

      return reports.map(
        (report): ReportResponseDto => ({
          id: report.id,
          childId: report.childId,
          schoolId: report.schoolId,
          fileUrl: report.fileUrl,
          type: report.type,
          name: report.name,
          createdAt: report.createdAt,
          childName: report.child.name,
          author: {
            id: report.author.id,
            firstName: report.author.firstName,
            lastName: report.author.lastName,
            fullName:
              `${report.author.firstName || ''} ${report.author.lastName || ''}`.trim(),
          },
        }),
      );
    } catch (error) {
      this.handlePrismaError(error, 'findByChildId');
      throw error;
    }
  }

  private handlePrismaError(error: any, operation: string) {
    if (error.code === 'P2002') {
      throw new ConflictException(
        'Нарушено уникальное ограничение. Проверьте данные и попробуйте снова.',
      );
    }

    if (error.code === 'P2025') {
      throw new NotFoundException(
        'Запись для обновления или удаления не найдена',
      );
    }

    this.logger.error(`Ошибка в операции ${operation}:`, error.stack);
    throw new BadRequestException('Произошла ошибка при выполнении операции');
  }
}
