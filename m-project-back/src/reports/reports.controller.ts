import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  UseInterceptors,
  UploadedFile,
  HttpCode,
  HttpStatus,
  ParseUUIDPipe,
  MaxFileSizeValidator,
  ParseFilePipe,
} from '@nestjs/common';
import { ReportsService } from './reports.service';
import { CreateReportDto } from './dto/create-report.dto';
import { UpdateReportDto } from './dto/update-report.dto';
import { JwtAuthGuard } from 'src/guards/jwt-auth.guard';
import { FileInterceptor } from '@nestjs/platform-express';
import { ReportResponseDto } from './dto/report-response.dto';
import { multerConfig } from '../common/multer.config';

@Controller('reports')
@UseGuards(JwtAuthGuard)
export class ReportsController {
  constructor(private readonly reportsService: ReportsService) {}

  @Post()
  @UseInterceptors(FileInterceptor('file', multerConfig))
  create(
    @Body() createReportDto: CreateReportDto,
    @UploadedFile(
      new ParseFilePipe({
        validators: [
          new MaxFileSizeValidator({ maxSize: 15 * 1024 * 1024 }), // 15 MB
        ],
        fileIsRequired: true,
      }),
    ) file: Express.Multer.File,
  ) {
    return this.reportsService.create(createReportDto, file);
  }

  @Get('school/:schoolId')
  findAll(@Param('schoolId') schoolId: string) {
    return this.reportsService.findAll(schoolId);
  }

  @Get('child/:childId')
  @HttpCode(HttpStatus.OK)
  async findByChildId(
    @Param('childId', new ParseUUIDPipe()) childId: string,
  ): Promise<ReportResponseDto[]> {
    return this.reportsService.findByChildId(childId);
  }

  @Get(':id/:schoolId')
  findOne(@Param('id') id: string, @Param('schoolId') schoolId: string) {
    return this.reportsService.findOne(id, schoolId);
  }

  @Patch(':id/:schoolId')
  @UseInterceptors(FileInterceptor('file', multerConfig))
  update(
    @Param('id') id: string,
    @Param('schoolId') schoolId: string,
    @Body() updateReportDto: UpdateReportDto,
    @UploadedFile(
      new ParseFilePipe({
        validators: [
          new MaxFileSizeValidator({ maxSize: 15 * 1024 * 1024 }), // 15 MB
        ],
        fileIsRequired: false,
      }),
    )
    file?: Express.Multer.File,
  ) {
    // Добавляем schoolId в DTO, если он не был передан в теле запроса
    if (!updateReportDto.schoolId) {
      updateReportDto.schoolId = schoolId;
    }
    return this.reportsService.update(id, updateReportDto, file);
  }

  @Delete(':id')
  remove(@Param('id') id: string) {
    return this.reportsService.remove(id);
  }
}
