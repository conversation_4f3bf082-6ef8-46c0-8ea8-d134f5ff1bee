import { IsString, IsDateString, <PERSON><PERSON><PERSON><PERSON>, IsOptional } from 'class-validator';

export class CreateChildDto {
  @IsString()
  name: string;

  @IsDateString()
  dateOfBirth: Date;

  @IsString()
  groupId: string;

  @IsString()
  schoolId: string;

  @IsArray()
  @IsString({ each: true })
  parentIds: string[];

  @IsOptional()
  @IsString()
  photoUrl?: string;

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  allowedTelegramIds?: string[];
}
