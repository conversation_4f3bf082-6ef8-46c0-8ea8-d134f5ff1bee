import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Delete,
  Param,
  UseGuards,
  Query,
  NotFoundException,
  Request,
  Logger,
} from '@nestjs/common';
import { ChildrenService } from './children.service';
import { AccessCodeService } from './access-code.service';
import { CreateChildDto } from './dto/create-child.dto';
import { UpdateChildDto } from './dto/update-child.dto';
import { JwtAuthGuard } from 'src/guards/jwt-auth.guard';
// import { ParentOwnershipGuard } from 'src/guards/parent-ownership.guard';

@Controller('children')
@UseGuards(JwtAuthGuard)
export class ChildrenController {
  private readonly logger = new Logger(ChildrenController.name);

  constructor(
    private readonly childrenService: ChildrenService,
    private readonly accessCodeService: AccessCodeService,
  ) {}

  @Post()
  create(@Body() dto: CreateChildDto) {
    return this.childrenService.create(dto);
  }

  @Get()
  findAll(@Query('schoolId') schoolId: string) {
    return this.childrenService.findAll(schoolId);
  }

  @UseGuards(JwtAuthGuard)
  @Get('my-children')
  async findMyChildren(@Request() req) {
    // Получаем ID пользователя из запроса (устанавливается JwtAuthGuard)
    const userId = req.user.id;
    console.log('Получен ID пользователя из JWT:', userId);

    // Если ID не найден, возвращаем пустой массив
    if (!userId) {
      console.error('Не удалось получить ID пользователя из JWT');
      return [];
    }

    // Получаем всех детей, связанных с этим пользователем
    return this.childrenService.findChildrenByParentId(userId);
  }

  @Get('with-progress')
  findAllWithProgress(
    @Query('schoolId') schoolId: string,
    @Query('groupId') groupId?: string,
  ) {
    return this.childrenService.findAllWithProgress(schoolId, groupId);
  }

  @Get(':id')
  findOne(@Param('id') id: string, @Param('schoolId') schoolId: string) {
    return this.childrenService.findOne(id, schoolId);
  }

  @Patch(':id')
  update(
    @Param('id') id: string,
    @Body() dto: UpdateChildDto,
    @Param('schoolId') schoolId: string,
  ) {
    return this.childrenService.update(id, dto, schoolId);
  }

  @Delete(':id')
  softDelete(@Param('id') id: string) {
    return this.childrenService.softDelete(id);
  }

  @Get(':id/details')
  async getChildDetails(@Param('id') id: string) {
    this.logger.log(`[ДЕТАЛИ РЕБЕНКА] Запрос деталей ребенка с ID: ${id}`);
    console.log(`[ДЕТАЛИ РЕБЕНКА] Запрос деталей ребенка с ID: ${id}`);
    process.stdout.write(`[ДЕТАЛИ РЕБЕНКА] Запрос деталей ребенка с ID: ${id}\n`);

    try {
      const childDetails = await this.childrenService.getChildDetails(id);

      this.logger.log(
        `[ДЕТАЛИ РЕБЕНКА] Результат запроса: ${childDetails ? 'Найден' : 'Не найден'}`,
      );
      console.log(
        `[ДЕТАЛИ РЕБЕНКА] Результат запроса: ${childDetails ? 'Найден' : 'Не найден'}`,
      );
      process.stdout.write(`[ДЕТАЛИ РЕБЕНКА] Результат запроса: ${childDetails ? 'Найден' : 'Не найден'}\n`);

      if (!childDetails) {
        this.logger.warn(`[ДЕТАЛИ РЕБЕНКА] Выбрасываем NotFoundException для ID ${id}`);
        console.log(`[ДЕТАЛИ РЕБЕНКА] Выбрасываем NotFoundException для ID ${id}`);
        process.stdout.write(`[ДЕТАЛИ РЕБЕНКА] Выбрасываем NotFoundException для ID ${id}\n`);
        throw new NotFoundException(`Ребенок с ID ${id} не найден`);
      }

      this.logger.log(`[ДЕТАЛИ РЕБЕНКА] Успешно возвращаем данные для ребенка: ${childDetails.name}`);
      console.log(`[ДЕТАЛИ РЕБЕНКА] Успешно возвращаем данные для ребенка: ${childDetails.name}`);
      process.stdout.write(`[ДЕТАЛИ РЕБЕНКА] Успешно возвращаем данные для ребенка: ${childDetails.name}\n`);

      return childDetails;
    } catch (error) {
      this.logger.error(
        `[ДЕТАЛИ РЕБЕНКА] Ошибка при получении деталей ребенка: ${error.message}`,
        error.stack,
      );
      console.error(
        `[ДЕТАЛИ РЕБЕНКА] Ошибка при получении деталей ребенка: ${error.message}`,
      );
      process.stderr.write(`[ДЕТАЛИ РЕБЕНКА] Ошибка при получении деталей ребенка: ${error.message}\n`);
      throw error;
    }
  }

  @Post(':id/generate-access-code')
  async generateAccessCode(@Param('id') id: string) {
    try {
      const accessCode =
        await this.accessCodeService.generateAccessCodeForChild(id);
      return { accessCode };
    } catch (error) {
      throw new NotFoundException(
        `Не удалось создать код доступа: ${error.message}`,
      );
    }
  }

  @Post(':id/reset-access-code')
  async resetAccessCode(@Param('id') id: string) {
    try {
      const accessCode = await this.accessCodeService.resetAccessCode(id);
      return { accessCode };
    } catch (error) {
      throw new NotFoundException(
        `Не удалось сбросить код доступа: ${error.message}`,
      );
    }
  }

  @Delete(':id/access-code')
  async removeAccessCode(@Param('id') id: string) {
    try {
      await this.accessCodeService.removeAccessCode(id);
      return { message: 'Код доступа успешно удален' };
    } catch (error) {
      throw new NotFoundException(
        `Не удалось удалить код доступа: ${error.message}`,
      );
    }
  }

  @Get(':id/allowed-telegram-ids')
  async getAllowedTelegramIds(@Param('id') id: string) {
    try {
      const telegramIds =
        await this.accessCodeService.getAllowedTelegramIds(id);
      return { telegramIds };
    } catch (error) {
      throw new NotFoundException(
        `Не удалось получить список Telegram ID: ${error.message}`,
      );
    }
  }

  @Post(':id/allowed-telegram-ids')
  async addAllowedTelegramId(
    @Param('id') id: string,
    @Body() body: { telegramId: string },
  ) {
    try {
      if (!body.telegramId) {
        throw new Error('Telegram ID обязателен');
      }
      await this.accessCodeService.addAllowedTelegramId(id, body.telegramId);
      return { message: `Telegram ID ${body.telegramId} успешно добавлен` };
    } catch (error) {
      throw new NotFoundException(
        `Не удалось добавить Telegram ID: ${error.message}`,
      );
    }
  }

  @Delete(':id/allowed-telegram-ids/:telegramId')
  async removeAllowedTelegramId(
    @Param('id') id: string,
    @Param('telegramId') telegramId: string,
  ) {
    try {
      await this.accessCodeService.removeAllowedTelegramId(id, telegramId);
      return { message: `Telegram ID ${telegramId} успешно удален` };
    } catch (error) {
      throw new NotFoundException(
        `Не удалось удалить Telegram ID: ${error.message}`,
      );
    }
  }
}
