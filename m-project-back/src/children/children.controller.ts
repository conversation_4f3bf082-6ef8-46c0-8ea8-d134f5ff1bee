import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Delete,
  Param,
  UseGuards,
  Query,
  NotFoundException,
  Request,
} from '@nestjs/common';
import { ChildrenService } from './children.service';
import { AccessCodeService } from './access-code.service';
import { CreateChildDto } from './dto/create-child.dto';
import { UpdateChildDto } from './dto/update-child.dto';
import { JwtAuthGuard } from 'src/guards/jwt-auth.guard';
// import { ParentOwnershipGuard } from 'src/guards/parent-ownership.guard';

@Controller('children')
@UseGuards(JwtAuthGuard)
export class ChildrenController {
  constructor(
    private readonly childrenService: ChildrenService,
    private readonly accessCodeService: AccessCodeService,
  ) {}

  @Post()
  create(@Body() dto: CreateChildDto) {
    return this.childrenService.create(dto);
  }

  @Get()
  findAll(@Query('schoolId') schoolId: string) {
    return this.childrenService.findAll(schoolId);
  }

  @UseGuards(JwtAuthGuard)
  @Get('my-children')
  async findMyChildren(@Request() req) {
    // Получаем ID пользователя из запроса (устанавливается JwtAuthGuard)
    const userId = req.user.id;
    console.log('Получен ID пользователя из JWT:', userId);

    // Если ID не найден, возвращаем пустой массив
    if (!userId) {
      console.error('Не удалось получить ID пользователя из JWT');
      return [];
    }

    // Получаем всех детей, связанных с этим пользователем
    return this.childrenService.findChildrenByParentId(userId);
  }

  @Get('with-progress')
  findAllWithProgress(
    @Query('schoolId') schoolId: string,
    @Query('groupId') groupId?: string,
  ) {
    return this.childrenService.findAllWithProgress(schoolId, groupId);
  }

  @Get(':id')
  findOne(@Param('id') id: string, @Param('schoolId') schoolId: string) {
    return this.childrenService.findOne(id, schoolId);
  }

  @Patch(':id')
  update(
    @Param('id') id: string,
    @Body() dto: UpdateChildDto,
    @Param('schoolId') schoolId: string,
  ) {
    return this.childrenService.update(id, dto, schoolId);
  }

  @Delete(':id')
  softDelete(@Param('id') id: string) {
    return this.childrenService.softDelete(id);
  }

  @Get(':id/details')
  async getChildDetails(@Param('id') id: string) {
    console.log(`Контроллер: Запрос деталей ребенка с ID: ${id}`);

    try {
      const childDetails = await this.childrenService.getChildDetails(id);
      console.log(
        `Контроллер: Результат запроса:`,
        childDetails ? 'Найден' : 'Не найден',
      );

      if (!childDetails) {
        console.log(`Контроллер: Выбрасываем NotFoundException для ID ${id}`);
        throw new NotFoundException(`Ребенок с ID ${id} не найден`);
      }

      return childDetails;
    } catch (error) {
      console.error(
        `Контроллер: Ошибка при получении деталей ребенка: ${error.message}`,
      );
      throw error;
    }
  }

  @Post(':id/generate-access-code')
  async generateAccessCode(@Param('id') id: string) {
    try {
      const accessCode =
        await this.accessCodeService.generateAccessCodeForChild(id);
      return { accessCode };
    } catch (error) {
      throw new NotFoundException(
        `Не удалось создать код доступа: ${error.message}`,
      );
    }
  }

  @Post(':id/reset-access-code')
  async resetAccessCode(@Param('id') id: string) {
    try {
      const accessCode = await this.accessCodeService.resetAccessCode(id);
      return { accessCode };
    } catch (error) {
      throw new NotFoundException(
        `Не удалось сбросить код доступа: ${error.message}`,
      );
    }
  }

  @Delete(':id/access-code')
  async removeAccessCode(@Param('id') id: string) {
    try {
      await this.accessCodeService.removeAccessCode(id);
      return { message: 'Код доступа успешно удален' };
    } catch (error) {
      throw new NotFoundException(
        `Не удалось удалить код доступа: ${error.message}`,
      );
    }
  }

  @Get(':id/allowed-telegram-ids')
  async getAllowedTelegramIds(@Param('id') id: string) {
    try {
      const telegramIds =
        await this.accessCodeService.getAllowedTelegramIds(id);
      return { telegramIds };
    } catch (error) {
      throw new NotFoundException(
        `Не удалось получить список Telegram ID: ${error.message}`,
      );
    }
  }

  @Post(':id/allowed-telegram-ids')
  async addAllowedTelegramId(
    @Param('id') id: string,
    @Body() body: { telegramId: string },
  ) {
    try {
      if (!body.telegramId) {
        throw new Error('Telegram ID обязателен');
      }
      await this.accessCodeService.addAllowedTelegramId(id, body.telegramId);
      return { message: `Telegram ID ${body.telegramId} успешно добавлен` };
    } catch (error) {
      throw new NotFoundException(
        `Не удалось добавить Telegram ID: ${error.message}`,
      );
    }
  }

  @Delete(':id/allowed-telegram-ids/:telegramId')
  async removeAllowedTelegramId(
    @Param('id') id: string,
    @Param('telegramId') telegramId: string,
  ) {
    try {
      await this.accessCodeService.removeAllowedTelegramId(id, telegramId);
      return { message: `Telegram ID ${telegramId} успешно удален` };
    } catch (error) {
      throw new NotFoundException(
        `Не удалось удалить Telegram ID: ${error.message}`,
      );
    }
  }
}
