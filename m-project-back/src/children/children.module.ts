import { Module } from '@nestjs/common';
import { ChildrenService } from './children.service';
import { ChildrenController } from './children.controller';
import { PrismaService } from 'src/prisma.service';
import { AccessCodeService } from './access-code.service';

@Module({
  controllers: [ChildrenController],
  providers: [ChildrenService, PrismaService, AccessCodeService],
})
export class ChildrenModule {}
