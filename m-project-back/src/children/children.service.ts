import {
  Injectable,
  Logger,
  NotFoundException,
  ConflictException,
  BadRequestException,
} from '@nestjs/common';
import { PrismaService } from 'src/prisma.service';
import { Child } from '@prisma/client';
import { CreateChildDto } from './dto/create-child.dto';
import { UpdateChildDto } from './dto/update-child.dto';

@Injectable()
export class ChildrenService {
  private readonly logger = new Logger(ChildrenService.name);

  constructor(private prisma: PrismaService) {}

  // Получить всех детей
  async findAll(schoolId: string): Promise<Child[]> {
    return this.prisma.child.findMany({
      where: {
        schoolId,
        isDeleted: false,
      },
    });
  }

  // Получить одного ребёнка по ID
  async findOne(id: string, schoolId: string): Promise<Child> {
    try {
      const child = await this.prisma.child.findFirst({
        where: {
          id,
          schoolId,
          isDeleted: false,
        },
      });
      if (!child) {
        throw new NotFoundException(`Ребёнок с ID ${id} не найден`);
      }
      return child;
    } catch (error) {
      this.handlePrismaError(error, 'findOne');
    }
  }

  // Создать запись о ребёнке
  async create(createChildDto: CreateChildDto): Promise<Child> {
    try {
      const child = await this.prisma.child.create({
        data: {
          name: createChildDto.name,
          dateOfBirth: createChildDto.dateOfBirth,
          groupId: createChildDto.groupId,
          schoolId: createChildDto.schoolId,
          parentIds: createChildDto.parentIds,
          photoUrl: createChildDto.photoUrl,
        },
      });
      this.logger.log(`Ребёнок создан: ID ${child.id}`);
      return child;
    } catch (error) {
      this.handlePrismaError(error, 'create');
    }
  }

  // Обновить запись о ребёнке
  async update(
    id: string,
    updateChildDto: UpdateChildDto,
    schoolId: string,
  ): Promise<Child> {
    const existingChild = await this.findOne(id, schoolId);

    if (updateChildDto.parentIds) {
      const removedParentIds = existingChild.parentIds.filter(
        (id) => !updateChildDto.parentIds.includes(id),
      );

      for (const parentId of removedParentIds) {
        await this.removeParent(id, parentId);
      }
    }

    return this.prisma.child.update({
      where: { id },
      data: updateChildDto,
    });
  }

  // Мягкое удаление записи о ребёнке
  async softDelete(id: string): Promise<Child> {
    return this.prisma.child.update({
      where: { id },
      data: { isDeleted: true },
    });
  }

  async getChildDetails(childId: string) {
    this.logger.log(`Запрос деталей ребенка с ID: ${childId}`);
    console.log(`Запрос деталей ребенка с ID: ${childId}`);
    const child = await this.prisma.child.findUnique({
      where: { id: childId },
      select: {
        id: true,
        name: true,
        dateOfBirth: true,
        schoolId: true,
        group: {
          select: {
            id: true,
            name: true,
          },
        },
        progress: {
          select: {
            id: true,
            skillId: true,
            status: true,
            updatedAt: true,
          },
        },
        attendance: {
          select: {
            id: true,
            date: true,
            status: true,
            checkInTime: true,
            checkOutTime: true,
          },
        },
        reports: {
          select: {
            id: true,
            fileUrl: true,
            type: true,
            createdAt: true,
          },
        },
        photos: {
          select: {
            id: true,
            url: true,
            description: true,
          },
        },
      },
    });

    if (!child) {
      this.logger.warn(`Ребенок с ID ${childId} не найден в базе данных`);
      console.log(`Ребенок с ID ${childId} не найден в базе данных`);
      return null; // Возвращаем null вместо выбрасывания исключения
    }

    // const schoolId = child.group?.id;
    // let menu = [];

    // if (schoolId) {
    //   menu = await this.prisma.menu.findMany({
    //     where: { schoolId },
    //     select: {
    //       date: true,
    //       meals: true,
    //     },
    //   });
    // }

    this.logger.log(`Ребенок найден: ${child.name}`);
    console.log(`Ребенок найден: ${child.name}`);

    return {
      ...child,
      // menu,
    };
  }

  // Получить всех детей по ID родителя
  async findChildrenByParentId(parentId: string) {
    console.log('Получен ID родителя в findChildrenByParentId:', parentId);

    if (!parentId) {
      console.error('Не удалось получить ID родителя');
      return [];
    }

    try {
      // Получаем telegramId пользователя
      const user = await this.prisma.user.findUnique({
        where: { id: parentId },
        select: { telegramId: true },
      });

      return this.prisma.child.findMany({
        where: {
          AND: [
            { parentIds: { has: parentId } },
            user?.telegramId
              ? { allowedTelegramIds: { has: user.telegramId } }
              : {},
            { isDeleted: false },
          ],
        },
        select: {
          id: true,
          name: true,
          dateOfBirth: true,
          photoUrl: true,
          schoolId: true,
          group: {
            select: {
              id: true,
              name: true,
            },
          },
        },
      });
    } catch (error) {
      this.logger.error(
        `Ошибка при получении детей по ID родителя: ${error.message}`,
      );
      throw new Error(
        `Не удалось получить детей по ID родителя: ${error.message}`,
      );
    }
  }

  // Получить всех детей с прогрессом
  async findAllWithProgress(schoolId: string, groupId?: string) {
    return this.prisma.child.findMany({
      where: {
        schoolId,
        groupId: groupId || undefined,
        isDeleted: false,
      },
      select: {
        id: true,
        name: true,
        dateOfBirth: true,
        progress: {
          select: {
            id: true,
            skillId: true,
            status: true,
            updatedAt: true,
          },
        },
      },
    });
  }

  private handlePrismaError(error: any, operation: string) {
    if (error.code === 'P2002') {
      throw new ConflictException(
        'Нарушено уникальное ограничение. Проверьте данные и попробуйте снова.',
      );
    }

    if (error.code === 'P2025') {
      throw new NotFoundException(
        'Запись для обновления или удаления не найдена',
      );
    }

    this.logger.error(`Ошибка в операции ${operation}:`, error.stack);
    throw new BadRequestException('Произошла ошибка при выполнении операции');
  }

  async removeParent(childId: string, parentId: string): Promise<Child> {
    const child = await this.prisma.child.findUnique({
      where: { id: childId },
    });

    if (!child) {
      throw new NotFoundException(`Ребенок с ID ${childId} не найден`);
    }

    const parent = await this.prisma.user.findUnique({
      where: { id: parentId },
    });

    const updatedParentIds = child.parentIds.filter((id) => id !== parentId);
    let updatedTelegramIds = [...child.allowedTelegramIds];

    if (parent?.telegramId) {
      updatedTelegramIds = updatedTelegramIds.filter(
        (id) => id !== parent.telegramId,
      );
    }

    return this.prisma.child.update({
      where: { id: childId },
      data: {
        parentIds: updatedParentIds,
        allowedTelegramIds: updatedTelegramIds,
      },
    });
  }
}
