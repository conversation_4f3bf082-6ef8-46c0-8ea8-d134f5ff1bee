import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from 'src/prisma.service';

@Injectable()
export class AccessCodeService {
  private readonly logger = new Logger(AccessCodeService.name);

  constructor(private prisma: PrismaService) {}

  /**
   * Генерирует случайный 6-значный код
   */
  private generateCode(): string {
    // Генерируем 6-значный числовой код
    return Math.floor(100000 + Math.random() * 900000).toString();
  }

  /**
   * Генерирует и сохраняет код доступа для ребенка
   */
  async generateAccessCodeForChild(childId: string): Promise<string> {
    try {
      // Проверяем существование ребенка
      const child = await this.prisma.child.findUnique({
        where: { id: childId },
      });

      if (!child) {
        throw new Error(`Ребенок с ID ${childId} не найден`);
      }

      // Генерируем уникальный код
      let accessCode = this.generateCode();
      let isUnique = false;

      // Проверяем, что код уникален
      while (!isUnique) {
        const existingChild = await this.prisma.child.findUnique({
          where: { accessCode },
        });

        if (!existingChild) {
          isUnique = true;
        } else {
          accessCode = this.generateCode();
        }
      }

      // Сохраняем код для ребенка
      await this.prisma.child.update({
        where: { id: childId },
        data: { accessCode },
      });

      this.logger.log(
        `Код доступа создан для ребенка ${childId}: ${accessCode}`,
      );
      return accessCode;
    } catch (error) {
      this.logger.error(`Ошибка при создании кода доступа: ${error.message}`);
      throw error;
    }
  }

  /**
   * Сбрасывает код доступа для ребенка
   */
  async resetAccessCode(childId: string): Promise<string | null> {
    try {
      // Удаляем текущий код
      await this.prisma.child.update({
        where: { id: childId },
        data: { accessCode: null },
      });

      // Генерируем новый код
      return this.generateAccessCodeForChild(childId);
    } catch (error) {
      this.logger.error(`Ошибка при сбросе кода доступа: ${error.message}`);
      throw error;
    }
  }

  /**
   * Удаляет код доступа для ребенка
   */
  async removeAccessCode(childId: string): Promise<void> {
    try {
      await this.prisma.child.update({
        where: { id: childId },
        data: { accessCode: null },
      });
      this.logger.log(`Код доступа удален для ребенка ${childId}`);
    } catch (error) {
      this.logger.error(`Ошибка при удалении кода доступа: ${error.message}`);
      throw error;
    }
  }

  /**
   * Добавляет разрешенный Telegram ID для ребенка
   */
  async addAllowedTelegramId(
    childId: string,
    telegramId: string,
  ): Promise<void> {
    try {
      const child = await this.prisma.child.findUnique({
        where: { id: childId },
        select: { allowedTelegramIds: true },
      });

      if (!child) {
        throw new Error(`Ребенок с ID ${childId} не найден`);
      }

      // Проверяем, что ID еще нет в списке
      if (!child.allowedTelegramIds.includes(telegramId)) {
        await this.prisma.child.update({
          where: { id: childId },
          data: {
            allowedTelegramIds: {
              push: telegramId,
            },
          },
        });
        this.logger.log(
          `Telegram ID ${telegramId} добавлен для ребенка ${childId}`,
        );
      }
    } catch (error) {
      this.logger.error(`Ошибка при добавлении Telegram ID: ${error.message}`);
      throw error;
    }
  }

  /**
   * Удаляет разрешенный Telegram ID для ребенка
   */
  async removeAllowedTelegramId(
    childId: string,
    telegramId: string,
  ): Promise<void> {
    try {
      const child = await this.prisma.child.findUnique({
        where: { id: childId },
        select: { allowedTelegramIds: true },
      });

      if (!child) {
        throw new Error(`Ребенок с ID ${childId} не найден`);
      }

      // Удаляем ID из списка
      const updatedIds = child.allowedTelegramIds.filter(
        (id) => id !== telegramId,
      );

      await this.prisma.child.update({
        where: { id: childId },
        data: {
          allowedTelegramIds: updatedIds,
        },
      });

      this.logger.log(
        `Telegram ID ${telegramId} удален для ребенка ${childId}`,
      );
    } catch (error) {
      this.logger.error(`Ошибка при удалении Telegram ID: ${error.message}`);
      throw error;
    }
  }

  /**
   * Получает список разрешенных Telegram ID для ребенка
   */
  async getAllowedTelegramIds(childId: string): Promise<string[]> {
    try {
      const child = await this.prisma.child.findUnique({
        where: { id: childId },
        select: { allowedTelegramIds: true },
      });

      if (!child) {
        throw new Error(`Ребенок с ID ${childId} не найден`);
      }

      return child.allowedTelegramIds;
    } catch (error) {
      this.logger.error(
        `Ошибка при получении списка Telegram ID: ${error.message}`,
      );
      throw error;
    }
  }
}
