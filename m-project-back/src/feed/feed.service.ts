import {
  Injectable,
  Logger,
  NotFoundException,
  ConflictException,
  BadRequestException,
} from '@nestjs/common';
import { PrismaService } from 'src/prisma.service';
import { Feed } from '@prisma/client';
import { CreateFeedDto } from './dto/create-feed.dto';
import { UpdateFeedDto } from './dto/update-feed.dto';

@Injectable()
export class FeedService {
  private readonly logger = new Logger(FeedService.name);

  constructor(private prisma: PrismaService) {}

  async create(dto: CreateFeedDto): Promise<Feed> {
    try {
      const feed = await this.prisma.feed.create({
        data: dto,
      });
      this.logger.log(`Запись в ленте создана: ID ${feed.id}`);
      return feed;
    } catch (error) {
      this.handlePrismaError(error, 'create');
    }
  }

  async findAll(): Promise<Feed[]> {
    return this.prisma.feed.findMany();
  }

  async findOne(id: string): Promise<Feed> {
    try {
      const feed = await this.prisma.feed.findUnique({
        where: { id },
      });
      if (!feed) {
        throw new NotFoundException(`Запись в ленте с ID ${id} не найдена`);
      }
      return feed;
    } catch (error) {
      this.handlePrismaError(error, 'findOne');
    }
  }

  async update(id: string, dto: UpdateFeedDto): Promise<Feed> {
    await this.findOne(id);

    try {
      const feed = await this.prisma.feed.update({
        where: { id },
        data: dto,
      });
      this.logger.log(`Запись в ленте обновлена: ID ${feed.id}`);
      return feed;
    } catch (error) {
      this.handlePrismaError(error, 'update');
    }
  }

  async remove(id: string): Promise<Feed> {
    return this.prisma.feed.delete({
      where: { id },
    });
  }

  private handlePrismaError(error: any, operation: string) {
    if (error.code === 'P2002') {
      throw new ConflictException(
        'Нарушено уникальное ограничение. Проверьте данные и попробуйте снова.',
      );
    }

    if (error.code === 'P2025') {
      throw new NotFoundException(
        'Запись для обновления или удаления не найдена',
      );
    }

    this.logger.error(`Ошибка в операции ${operation}:`, error.stack);
    throw new BadRequestException('Произошла ошибка при выполнении операции');
  }
}
