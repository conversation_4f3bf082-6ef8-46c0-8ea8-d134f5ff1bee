const fs = require('fs');
const path = require('path');

const BASE_DIR = path.join(__dirname, 'src', 'menu');
const DTO_DIR = path.join(BASE_DIR, 'dto');

// Файлы для создания
const files = {
  'menu.module.ts': `import { Module } from '@nestjs/common';
import { MenuService } from './menu.service';
import { MenuController } from './menu.controller';
import { PrismaService } from 'src/prisma.service';

@Module({
  controllers: [MenuController],
  providers: [MenuService, PrismaService],
  exports: [MenuService],
})
export class MenuModule {}`,

  'menu.controller.ts': `import { Controller, Get, Post, Delete, Param, UploadedFile, UseInterceptors, Res } from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { MenuService } from './menu.service';
import { Response } from 'express';
import * as path from 'path';
import * as fs from 'fs';

@Controller('menu')
export class MenuController {
  constructor(private readonly menuService: MenuService) {}

  @Post('upload/:schoolId')
  @UseInterceptors(FileInterceptor('file'))
  uploadFile(@Param('schoolId') schoolId: string, @UploadedFile() file: Express.Multer.File) {
    return this.menuService.uploadFile(schoolId, file);
  }

  @Get(':schoolId')
  getMenu(@Param('schoolId') schoolId: string) {
    return this.menuService.getMenu(schoolId);
  }

  @Get('download/:schoolId')
  async downloadFile(@Param('schoolId') schoolId: string, @Res() res: Response) {
    const filePath = path.join(__dirname, '..', '..', 'public', 'uploads', 'menus', \`school-\${schoolId}.pdf\`);

    if (!fs.existsSync(filePath)) {
      return res.status(404).json({ message: 'Файл не найден' });
    }

    return res.sendFile(filePath);
  }

  @Delete(':schoolId')
  deleteMenu(@Param('schoolId') schoolId: string) {
    return this.menuService.deleteMenu(schoolId);
  }
}`,

  'menu.service.ts': `import { Injectable, NotFoundException } from '@nestjs/common';
import { PrismaService } from 'src/prisma.service';
import * as fs from 'fs';
import * as path from 'path';

@Injectable()
export class MenuService {
  private readonly uploadDir = path.join(__dirname, '..', '..', 'public', 'uploads', 'menus');

  constructor(private prisma: PrismaService) {
    if (!fs.existsSync(this.uploadDir)) {
      fs.mkdirSync(this.uploadDir, { recursive: true });
    }
  }

  async uploadFile(schoolId: string, file: Express.Multer.File) {
    const filePath = path.join(this.uploadDir, \`school-\${schoolId}.pdf\`);
    fs.writeFileSync(filePath, file.buffer);

    const fileUrl = \`/uploads/menus/school-\${schoolId}.pdf\`;

    return this.prisma.menu.upsert({
      where: { schoolId },
      update: { fileUrl },
      create: { schoolId, fileUrl },
    });
  }

  async getMenu(schoolId: string) {
    const menu = await this.prisma.menu.findUnique({ where: { schoolId } });
    if (!menu) throw new NotFoundException('Меню не найдено');
    return menu;
  }

  async deleteMenu(schoolId: string) {
    const filePath = path.join(this.uploadDir, \`school-\${schoolId}.pdf\`);

    if (fs.existsSync(filePath)) {
      fs.unlinkSync(filePath);
    }

    await this.prisma.menu.delete({ where: { schoolId } });
    return { message: 'Меню удалено' };
  }
}`,
};

// DTO файлы
const dtoFiles = {
  'create-menu.dto.ts': `export class CreateMenuDto {
  schoolId: string;
  fileUrl: string;
}`,

  'update-menu.dto.ts': `export class UpdateMenuDto {
  fileUrl?: string;
}`,
};

// Функция для создания файлов
function createOrUpdateFiles() {
  if (!fs.existsSync(BASE_DIR)) {
    fs.mkdirSync(BASE_DIR, { recursive: true });
  }

  Object.entries(files).forEach(([fileName, content]) => {
    const filePath = path.join(BASE_DIR, fileName);

    if (fs.existsSync(filePath)) {
      console.log(`✅ Файл \${fileName} уже существует. Пропускаем.`);
    } else {
      fs.writeFileSync(filePath, content);
      console.log(`📄 Создан файл: \${fileName}`);
    }
  });

  if (!fs.existsSync(DTO_DIR)) {
    fs.mkdirSync(DTO_DIR, { recursive: true });
  }

  Object.entries(dtoFiles).forEach(([fileName, content]) => {
    const filePath = path.join(DTO_DIR, fileName);

    if (fs.existsSync(filePath)) {
      console.log(`✅ DTO файл \${fileName} уже существует. Пропускаем.`);
    } else {
      fs.writeFileSync(filePath, content);
      console.log(`📄 Создан DTO файл: ${fileName}`);
    }
  });
}

// Запуск скрипта
createOrUpdateFiles();
console.log('🎉 Файлы для Menu успешно обновлены!');
