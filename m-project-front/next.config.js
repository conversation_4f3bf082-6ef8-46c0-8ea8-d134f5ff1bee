/** @type {import('next').NextConfig} */
const nextConfig = {
  webpack: (config) => {
    // Решаем проблему с модулями Node.js в браузере
    config.resolve.alias.canvas = false;
    config.resolve.alias.encoding = false;

    // Добавляем fallback для других Node.js модулей
    config.resolve.fallback = {
      ...config.resolve.fallback,
      fs: false,
      path: false,
      stream: false,
      util: false,
      zlib: false,
      crypto: false,
    };

    return config;
  },
  // Добавляем конфигурацию для копирования ассетов pspdfkit
  async headers() {
    return [
      {
        source: '/pspdfkit-lib/:path*',
        headers: [
          {
            key: 'Cache-Control',
            value: 'public, max-age=31536000, immutable',
          },
        ],
      },
    ];
  },
  // Добавляем настройки для API
  env: {
    NEXT_PUBLIC_API_URL: process.env.NEXT_PUBLIC_API_URL || 'http://backend:3002',
    // Используем пустую строку для относительных путей
    NEXT_PUBLIC_CLIENT_API_URL: '',
  },
}

module.exports = nextConfig;