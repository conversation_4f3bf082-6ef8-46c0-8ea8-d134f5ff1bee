{"name": "<PERSON><PERSON><PERSON><PERSON>", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@emotion/cache": "^11.14.0", "@emotion/react": "^11.14.0", "@emotion/server": "^11.11.0", "@emotion/styled": "^11.14.0", "@faker-js/faker": "^9.3.0", "@mui/icons-material": "^6.2.1", "@mui/lab": "^6.0.0-beta.20", "@mui/material": "^6.2.1", "@mui/material-nextjs": "^6.2.1", "@mui/x-date-pickers": "^7.26.0", "@mui/x-tree-view": "^7.23.2", "@simplepdf/react-embed-pdf": "^1.9.0", "@react-pdf-viewer/core": "^3.12.0", "@react-pdf-viewer/default-layout": "^3.12.0", "axios": "^1.7.9", "date-fns": "^4.1.0", "jsonwebtoken": "^9.0.2", "next": "15.1.2", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.54.2", "zustand": "^5.0.3", "pdfjs-dist": "3.4.120"}, "devDependencies": {"@eslint/eslintrc": "^3", "eslint": "^9", "eslint-config-next": "15.1.2", "postcss": "^8", "sass": "^1.83.0", "tailwindcss": "^3.4.1"}}