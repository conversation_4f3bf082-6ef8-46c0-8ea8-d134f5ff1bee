# Этап сборки
FROM node:18-alpine AS builder

WORKDIR /usr/src/app

# Копируем только файлы зависимостей для лучшего кэширования
COPY package*.json ./
RUN npm ci

# Копируем исходный код
COPY . .

# Собираем приложение
RUN npm run build

# Этап production
FROM node:18-alpine AS production

WORKDIR /usr/src/app

# Копируем только необходимые файлы из этапа сборки
COPY --from=builder /usr/src/app/package*.json ./
COPY --from=builder /usr/src/app/.next ./.next
COPY --from=builder /usr/src/app/public ./public
COPY --from=builder /usr/src/app/node_modules ./node_modules

# Устанавливаем только production зависимости
ENV NODE_ENV production

# Открываем порт
EXPOSE 3000

# Запускаем приложение
CMD ["npm", "start"]