"use client";

import { create } from "zustand";
import api from '@/axiosInstance';

// Инициализируем состояние с проверкой наличия токена
const useAuthStore = create((set) => {
  // Автоматически загружаем пользователя при инициализации, если есть токен
  if (typeof window !== 'undefined' && localStorage.getItem("accessToken")) {
    // Используем setTimeout, чтобы избежать проблем с циклическими зависимостями
    setTimeout(() => {
      useAuthStore.getState().fetchUser();
    }, 0);
  }

  return {
    user: null,
    accessToken: typeof window !== 'undefined' ? localStorage.getItem("accessToken") : null,
    isAuthenticated: typeof window !== 'undefined' && !!localStorage.getItem("accessToken"),

    // Логин
    login: async (email, password) => {
      try {
        const response = await api.post(
          "/auth/login",
          { email, password },
          {
            withCredentials: true,
          }
        );
        const { accessToken } = response.data;

        // TODO: В продакшн-версии использовать httpOnly cookies вместо localStorage
        // Сохраняем в localStorage и в cookie (для MVP)
        localStorage.setItem("accessToken", accessToken);

        // Устанавливаем cookie для middleware
        document.cookie = `accessToken=${accessToken}; path=/; max-age=86400; samesite=strict`;

        // Загружаем пользователя с API
        await useAuthStore.getState().fetchUser();

        console.log('Устанавливаем isAuthenticated = true');
        set({ isAuthenticated: true });
        console.log('После установки:', useAuthStore.getState().isAuthenticated);
      } catch (error) {
        console.error("Ошибка входа", error);
        throw error;
      }
    },

    registerUser: async (role, email, password) => {
      try {
        const response = await api.post('/auth/register', { role, email, password });
        set({ isAuthenticated: true });
        return response.data;
      } catch (error) {
        throw error;
      }
    },

    // Получаем пользователя из API
    fetchUser: async () => {
      try {
        const token = localStorage.getItem("accessToken");

        if (!token) return;

        const response = await api.get("/auth/me", {
          headers: { Authorization: `Bearer ${token}` },
        });

        set({ user: response.data, isAuthenticated: true });
      } catch (error) {
        console.error("Ошибка загрузки пользователя", error);
      }
    },

    // Выход из системы
    logout: async () => {
      try {
        // Удаляем из localStorage и cookie
        localStorage.removeItem("accessToken");

        // Удаляем cookie
        document.cookie = "accessToken=; path=/; max-age=0; expires=Thu, 01 Jan 1970 00:00:00 GMT; samesite=strict";

        // Сбрасываем состояние
        set({
          user: null,
          accessToken: null,
          isAuthenticated: false,
        });

        window.location.href = "/login";
      } catch (error) {
        console.error("Ошибка при выходе", error);
        throw error;
      }
    }
  };
});

export default useAuthStore;
