import { NextResponse } from 'next/server'

// TODO: Реализовать безопасное хранение токена через httpOnly cookies на сервере
// Текущая реализация с заголовками - временное решение для MVP
// В продакшн-версии необходимо:
// 1. Установить httpOnly, secure, sameSite cookies на сервере
// 2. Убрать хранение токена в localStorage
// 3. Использовать refresh token для обновления access token

export function middleware(request) {
  // Получаем токен из cookie
  const accessToken = request.cookies.get('accessToken')?.value
  const isLoginPage = request.nextUrl.pathname === '/login'

  // Если пользователь авторизован и пытается зайти на страницу логина
  if (accessToken && isLoginPage) {
    // Получаем referer (предыдущую страницу)
    const referer = request.headers.get('referer')
    // Если есть referer и он с того же домена - редиректим туда
    if (referer && referer.includes(request.nextUrl.origin)) {
      return NextResponse.redirect(referer)
    }
    // Иначе на главную
    return NextResponse.redirect(new URL('/', request.url))
  }

  console.log(accessToken, isLoginPage)

  // Если пользователь не авторизован и пытается зайти на защищенные страницы
  if (!accessToken && !isLoginPage) {
    // Сохраняем текущий URL для редиректа после логина в query параметре
    const redirectUrl = new URL('/login', request.url)
    redirectUrl.searchParams.set('redirectTo', request.nextUrl.pathname)
    return NextResponse.redirect(redirectUrl)
  }

  return NextResponse.next()
}

export const config = {
  matcher: [
    // Применяем middleware ко всем путям, кроме:
    // - api routes (/api/*)
    // - статических файлов (_next/static/*, _next/image/*, favicon.ico и т.д.)
    '/((?!api|_next/static|_next/image|favicon.ico).*)'
  ]
}