"use client";

import * as React from "react";
import Box from "@mui/material/Box";
import Drawer from "@mui/material/Drawer";
import CssBaseline from "@mui/material/CssBaseline";
import AppBar from "@mui/material/AppBar";
import Toolbar from "@mui/material/Toolbar";
import List from "@mui/material/List";
import Typography from "@mui/material/Typography";
import Divider from "@mui/material/Divider";
import ListItem from "@mui/material/ListItem";
import ListItemButton from "@mui/material/ListItemButton";
import ListItemIcon from "@mui/material/ListItemIcon";
import ListItemText from "@mui/material/ListItemText";
import InboxIcon from "@mui/icons-material/MoveToInbox";
import MailIcon from "@mui/icons-material/Mail";
import LogoutIcon from "@mui/icons-material/Logout";
import AccountCircleIcon from "@mui/icons-material/AccountCircle";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { pages } from "@/constants/routes";
import useAuthStore from "@/store/authStore";

const drawerWidth = 240; // Базовая ширина

export function Sidebar() {
  const currentPath = usePathname();
  const logout = useAuthStore((state) => state.logout);
  const isAuth = useAuthStore((state) => state.isAuthenticated);
  const user = useAuthStore((state) => state.user);

  // Добавляем проверку роли пользователя
  const userRole = user?.role || '';

  // Add state to handle client-side rendering
  const [mounted, setMounted] = React.useState(false);

  // Use useEffect to handle client-side mounting
  React.useEffect(() => {
    setMounted(true);
  }, []);

  // Don't render anything until mounted
  if (!mounted) {
    return null;
  }

  // Фильтруем страницы в зависимости от роли пользователя
  const filteredPages = pages.filter(page => 
    page.roles.includes(userRole)
  );

  const handleLogout = async () => {
    try {
      await logout();
    } catch (error) {
      console.error("Ошибка при выходе:", error);
    }
  };

  function getIcon(icon) {
    return React.createElement(icon);
  }

  return (
    <Drawer
      sx={{
        flexShrink: 0,
        "& .MuiDrawer-paper": {
          width: { xs: 60, lg: drawerWidth }, // 60px для экранов менее 768px
          boxSizing: "border-box",
          display: "flex",
          flexDirection: "column",
          justifyContent: "space-between",
        },
      }}
      variant="permanent"
      anchor="left"
    >
      <Box>
        <Toolbar />
        <Divider />
        {isAuth && (
          <List sx={{ flexGrow: 1 }}>
            {filteredPages.map((page, index) => (
              <ListItem
                key={page.id}
                disablePadding
                component={Link}
                href={page.path}
              >
                <ListItemButton selected={currentPath === page.path}>
                  <ListItemIcon
                    sx={{
                      minWidth: { xs: 0, lg: 56 }, // Убираем min-width на малых экранах, включаем на средних и больших
                    }}
                  >
                    {getIcon(page.icon)}
                  </ListItemIcon>
                  <ListItemText
                    primary={page.name}
                    sx={{
                      display: { xs: 'none', lg: 'block' }
                    }}
                  />
                </ListItemButton>
              </ListItem>
            ))}
          </List>
        )}
      </Box>

      {isAuth && (
        <Box sx={{ padding: 2, borderTop: "1px solid #ddd" }}>
          <List>
            {/* Блок с именем пользователя */}
            <ListItem disablePadding>
              <ListItemButton sx={{ p: { xs: 0, lg: 1 } }}>
                <ListItemIcon sx={{ mr: '5px', minWidth: 'auto' }}>
                  <AccountCircleIcon />
                </ListItemIcon>
                <ListItemText
                  primary={user?.email}
                  sx={{
                    display: { xs: 'none', lg: 'block' }
                  }}
                />
              </ListItemButton>
            </ListItem>

            {/* Кнопка выхода */}
            <ListItem disablePadding>
              <ListItemButton onClick={handleLogout} sx={{ p: { xs: 0, lg: 1 } }}>
                <ListItemIcon sx={{ mr: '5px', minWidth: 'auto' }} >
                  <LogoutIcon/>
                </ListItemIcon>
                <ListItemText
                  primary="Выйти"
                  sx={{
                    display: { xs: 'none', lg: 'block' }
                  }}
                />
              </ListItemButton>
            </ListItem>
          </List>
        </Box>
      )}
    </Drawer>
  );
}