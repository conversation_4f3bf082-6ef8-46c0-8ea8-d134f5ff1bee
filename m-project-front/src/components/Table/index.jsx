"use client";

import React, { useEffect, useState } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Box,
  TextField,
  Button,
} from "@mui/material";
import { KeyboardArrowDown, KeyboardArrowRight } from "@mui/icons-material";
import AssessmentIcon from '@mui/icons-material/Assessment';
import "./styles.scss";
import Legend from "./Legend";
import { tableColors, tableLetters } from "./constants";
import api from "@/axiosInstance";
import useAuthStore from "@/store/authStore";
import { API_STATUSES, DISPLAY_STATUSES, apiToDisplay, displayToApi, getNextStatus } from "@/constants/statuses";
import ProgressModal from "../ProgressModal";

// Отображение статуса
const renderStatusIcon = (status) => {
  return (
    <Box
      sx={{
        display: "flex",
        justifyContent: "center",
        alignItems: "center",
        backgroundColor: tableColors[status] || "gray",
        color: "white",
        borderRadius: "50%",
        width: { xs: "24px", md: "30px" },
        height: { xs: "24px", md: "30px" },
        fontSize: { xs: "0.7rem", md: "0.875rem" },
        fontWeight: "bold",
      }}
    >
      {tableLetters[status] || "?"}
    </Box>
  );
};

// Основной компонент
const CollapsibleTable = () => {
  const [selectedCategory, setSelectedCategory] = useState(null);
  const [selectedGroup, setSelectedGroup] = useState(null);
  const [groups, setGroups] = useState([]);
  const [ageGroups, setAgeGroups] = useState([]);
  const [categories, setCategories] = useState([]);
  const [openSkills, setOpenSkills] = useState({});
  const [openExercises, setOpenExercises] = useState({});
  const [children, setChildren] = useState([]);
  const [skillsData, setSkillsData] = useState([]);
  const { user } = useAuthStore();
  const [selectedChild, setSelectedChild] = useState("");
  const [currentAgeGroupId, setCurrentAgeGroupId] = useState("children_3_6"); // По умолчанию группа 3-6 лет
  const [progressModalOpen, setProgressModalOpen] = useState(false);
  const [progressText, setProgressText] = useState("");

  // Используем импортированные объекты для преобразования статусов
  const apiStatusToDisplay = apiToDisplay;
  const displayStatusToApi = displayToApi;

  const toggleSkill = (skillName) => {
    setOpenSkills((prev) => ({ ...prev, [skillName]: !prev[skillName] }));
  };

  const toggleExercise = (exerciseId) => {
    setOpenExercises((prev) => ({ ...prev, [exerciseId]: !prev[exerciseId] }));
  };

  const handleStatusChange = async (childId, skillId, currentStatus) => {
    // Используем функцию getNextStatus из констант для определения следующего статуса
    const currentApiStatus = displayStatusToApi[currentStatus];
    const nextApiStatus = getNextStatus(currentApiStatus);

    try {
      const existingProgress = children
        .find((child) => child.id === childId)
        ?.progress.find((p) => p.skillId === skillId);

      let newProgressId;

      if (existingProgress) {
        // Используем PATCH с progressId для обновления
        await api.patch(`/progress/${existingProgress.id}`, {
          status: nextApiStatus,
        });
        newProgressId = existingProgress.id;
      } else {
        // Используем POST для создания нового и получаем ответ
        const response = await api.post("/progress", {
          childId,
          skillId,
          status: nextApiStatus,
        });
        newProgressId = response.data.id; // Предполагаем, что сервер возвращает id нового прогресса
      }

      // Обновляем локальное состояние после успешного сохранения
      setChildren((prevChildren) =>
        prevChildren.map((child) => {
          if (child.id === childId) {
            return {
              ...child,
              progress: [
                ...child.progress.filter((p) => p.skillId !== skillId),
                { skillId, status: nextApiStatus, id: newProgressId },
              ],
            };
          }
          return child;
        })
      );
    } catch (error) {
      console.error(error);
    }
  };

  // Функция для сбора прогресса выбранного ребенка
  const collectProgress = () => {
    if (!selectedChild) return;

    const selectedChildData = children.find(child => child.id === selectedChild);
    if (!selectedChildData) return;

    let progressOutput = `Ребенок: ${selectedChildData.name}\n\n`;

    // Проходим по всем категориям
    categories.forEach(category => {
      // Добавляем категорию в вывод
      progressOutput += `Категория: ${category.name}\n`;

      // Для каждой категории проходим по подкатегориям
      category.subcategories.forEach(subcategory => {
        // Добавляем подкатегорию в вывод
        progressOutput += `Подкатегория: ${subcategory.name}\n`;

        // Проверяем упражнения в подкатегории
        subcategory.exercises.forEach(exercise => {
          // Находим прогресс для этого упражнения
          const progress = selectedChildData.progress.find(p => p.skillId === exercise.id);

          // Добавляем навык в вывод с его статусом (если есть)
          const statusDisplay = progress ? apiToDisplay[progress.status] || "Не определен" : "Не начат";
          progressOutput += `${exercise.name} - ${statusDisplay}\n`;

          // Если у упражнения есть дочерние элементы, добавляем и их
          if (exercise.children) {
            exercise.children.forEach(childExercise => {
              const childProgress = selectedChildData.progress.find(p => p.skillId === childExercise.id);
              const childStatusDisplay = childProgress ? apiToDisplay[childProgress.status] || "Не определен" : "Не начат";
              progressOutput += `  ${childExercise.name} - ${childStatusDisplay}\n`;
            });
          }
        });

        // Добавляем пустую строку после подкатегории
        progressOutput += "\n";
      });

      // Добавляем пустую строку после категории
      progressOutput += "\n";
    });

    setProgressText(progressOutput);
    setProgressModalOpen(true);
  };

  async function getGroups() {
    try {
      // Сначала получаем список групп и возрастных групп
      const [groupsResponse, ageGroupsResponse] = await Promise.all([
        api.get("/groups"),
        api.get("/skills-category/age-groups/all"),
      ]);

      setGroups(groupsResponse.data);
      setAgeGroups(ageGroupsResponse.data);

      // Если группа не выбрана, выбираем первую из списка
      if (!selectedGroup && groupsResponse.data.length > 0) {
        setSelectedGroup(groupsResponse.data[0].id);
      }

      // Получаем данные о выбранной группе
      if (selectedGroup) {
        const selectedGroupData = groupsResponse.data.find(group => group.id === selectedGroup);
        if (selectedGroupData) {
          // Устанавливаем текущую возрастную группу на основе выбранной группы
          setCurrentAgeGroupId(selectedGroupData.ageGroupId || "children_3_6");

          // Получаем детей и категории навыков для выбранной возрастной группы
          const [childrenResponse, skillsResponse] = await Promise.all([
            api.get(`/children/with-progress`, {
              params: {
                schoolId: user.schoolId,
                groupId: selectedGroup,
              },
            }),
            api.get(`/skills-category/by-age-group/${selectedGroupData.ageGroupId || "children_3_6"}`),
          ]);

          setChildren(childrenResponse.data);
          setSkillsData(skillsResponse.data);
          setSelectedCategory(skillsResponse.data[0]?.id || "");
          setCategories(skillsResponse.data);
        }
      }
    } catch (error) {
      console.error(error);
    }
  }

  useEffect(() => {
    if (user?.schoolId) {
      getGroups();
    }
  }, [user?.schoolId, selectedGroup]);

  return (
    <div>
      {/* Выбор категории и группы */}
      <Box
        display="flex"
        flexDirection={{ xs: "column", md: "row" }}
        alignItems={{ xs: "stretch", md: "flex-start" }}
        gap={2}
        sx={{ width: { xs: "100%", md: "auto" } }}
      >
        <FormControl
          style={{ marginBottom: "20px", width: { xs: "100%", md: "200px" } }}
        >
          <InputLabel shrink>Выберите группу</InputLabel>
          <Select
            value={selectedGroup || ""}
            onChange={(e) => {
              setSelectedGroup(e.target.value);
            }}
            label="Выберите группу"
            fullWidth
          >
            {groups.map((group) => (
              <MenuItem key={group.id} value={group.id}>
                {group.name}
                {group.ageGroupId && ageGroups.find(ag => ag.id === group.ageGroupId) ?
                  ` (${ageGroups.find(ag => ag.id === group.ageGroupId).name})` : ''}
              </MenuItem>
            ))}
          </Select>
        </FormControl>

        <FormControl
          style={{ marginBottom: "20px", width: { xs: "100%", md: "200px" } }}
        >
          <InputLabel shrink>Выберите категорию</InputLabel>
          <Select
            value={selectedCategory || ""}
            onChange={(e) => {
              setSelectedCategory(e.target.value);
            }}
            label="Выберите категорию"
            fullWidth
          >
            {categories.map((category) => (
              <MenuItem key={category.id} value={category.id}>
                {category.name}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
      </Box>
      <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 3 }}>
        <TextField
          select
          fullWidth={{ xs: true, md: false }}
          label="Выберите ребенка"
          value={selectedChild}
          onChange={(e) => setSelectedChild(e.target.value)}
          margin="normal"
          sx={{ width: { xs: '100%', md: '200px'} }}
        >
          <MenuItem value="">Все дети</MenuItem>
          {children.map((child) => (
            <MenuItem key={child.id} value={child.id}>
              {child.name}
            </MenuItem>
          ))}
        </TextField>

        {selectedChild && (
          <Button
            variant="contained"
            color="primary"
            startIcon={<AssessmentIcon />}
            onClick={collectProgress}
            sx={{ height: 40, mt: 1 }}
          >
            Собрать прогресс
          </Button>
        )}
      </Box>
      <Legend />

      {/* Модальное окно с прогрессом */}
      <ProgressModal
        open={progressModalOpen}
        onClose={() => setProgressModalOpen(false)}
        progressData={progressText}
        childName={children.find(child => child.id === selectedChild)?.name || ""}
      />
      {/* Таблица */}
      <TableContainer
        component={Paper}
        sx={{
          width: { xs: "100%", md: "auto" },
          overflowX: "auto",
          ".MuiTable-root": {
            minWidth: { xs: "auto", md: "100%" },
          },
        }}
      >
        <Table size={{ xs: "small", md: "medium" }}>
          <TableHead>
            <TableRow>
              <TableCell size="small">Навыки / Упражнения</TableCell>
              {(selectedChild
                ? children.filter((child) => child.id === selectedChild)
                : children
              ).map((child) => (
                <TableCell
                  key={child.id}
                  align="center"
                  size="small"
                  sx={{
                    padding: { xs: "8px 4px", md: "16px" },
                    fontSize: { xs: "0.75rem", md: "0.875rem" },
                  }}
                >
                  {child.name}
                </TableCell>
              ))}
            </TableRow>
          </TableHead>
          {selectedCategory && (
            <TableBody>
              {categories
                .find((cat) => cat.id === selectedCategory)
                ?.subcategories.map((subcategory) => (
                  <React.Fragment key={subcategory.id}>
                    {/* Навык */}
                    <TableRow
                      style={{
                        backgroundColor: "#f0f0f0",
                        cursor: "pointer",
                      }}
                    >
                      <TableCell
                        onClick={() => toggleSkill(subcategory.name)}
                        size="small"
                        sx={{
                          padding: { xs: "8px 4px", md: "16px" },
                          fontSize: { xs: "0.75rem", md: "0.875rem" },
                          padding: { xs: "8px 4px", md: "16px" },
                          fontSize: { xs: "0.75rem", md: "0.875rem" },
                          maxWidth: "200px", // добавлено ограничение ширины
                          overflow: "hidden", // добавлено для обрезки текста
                          textOverflow: "ellipsis", // добавлено для отображения многоточия
                        }}
                      >
                        <Box
                          display="flex"
                          justifyContent="space-between"
                          alignItems="center"
                        >
                          {subcategory.name}
                          <IconButton size="small">
                            {openSkills[subcategory.name] ? (
                              <KeyboardArrowDown />
                            ) : (
                              <KeyboardArrowRight />
                            )}
                          </IconButton>
                        </Box>
                      </TableCell>
                      {/* Пустые ячейки для выравнивания */}
                      {(selectedChild
                        ? children.filter((child) => child.id === selectedChild)
                        : children
                      ).map((child) => (
                        <TableCell
                          key={child.id}
                          size="small"
                          sx={{ padding: { xs: "8px 4px", md: "16px" } }}
                        />
                      ))}
                    </TableRow>

                    {/* Упражнения навыка */}
                    {openSkills[subcategory.name] &&
                      subcategory.exercises.map((exercise) => (
                        <React.Fragment key={exercise.id}>
                          <TableRow>
                            <TableCell
                              size="small"
                              style={{
                                paddingLeft: exercise.children
                                  ? "32px"
                                  : "16px",
                                backgroundColor: exercise.children
                                  ? "#f0f0f0"
                                  : "none",
                                cursor: exercise.children
                                  ? "pointer"
                                  : "default",
                              }}
                              sx={{
                                padding: { xs: "8px 4px", md: "16px" },
                                paddingLeft: {
                                  xs: exercise.children ? "20px" : "12px",
                                  md: exercise.children ? "32px" : "16px",
                                },
                                fontSize: { xs: "0.7rem", md: "0.875rem" },
                              }}
                              onClick={
                                exercise.children
                                  ? () => toggleExercise(exercise.id)
                                  : undefined
                              }
                            >
                              <Box
                                display="flex"
                                justifyContent="space-between"
                                alignItems="center"
                              >
                                {exercise.name}
                                {exercise.children && (
                                  <IconButton size="small">
                                    {openExercises[exercise.id] ? (
                                      <KeyboardArrowDown />
                                    ) : (
                                      <KeyboardArrowRight />
                                    )}
                                  </IconButton>
                                )}
                              </Box>
                            </TableCell>
                            {!exercise.children &&
                              (selectedChild
                                ? children.filter(
                                    (child) => child.id === selectedChild
                                  )
                                : children
                              ).map((child) => (
                                <TableCell
                                  key={`${child.id}-${exercise.id}`}
                                  align="center"
                                  size="small"
                                >
                                  <div
                                    className="cursor-pointer flex justify-center"
                                    onClick={() =>
                                      handleStatusChange(
                                        child.id,
                                        exercise.id,
                                        apiStatusToDisplay[
                                          child.progress.find(
                                            (p) => p.skillId === exercise.id
                                          )?.status
                                        ]
                                      )
                                    }
                                  >
                                    {renderStatusIcon(
                                      apiStatusToDisplay[
                                        child.progress.find(
                                          (p) => p.skillId === exercise.id
                                        )?.status
                                      ]
                                    )}
                                  </div>
                                </TableCell>
                              ))}
                          </TableRow>

                          {/* Вложенные упражнения */}
                          {openExercises[exercise.id] &&
                            exercise.children &&
                            exercise.children.map((childExercise) => (
                              <TableRow key={childExercise.id}>
                                <TableCell
                                  style={{ paddingLeft: "48px" }}
                                  size="small"
                                >
                                  {childExercise.name}
                                </TableCell>
                                {(selectedChild
                                  ? children.filter(
                                      (child) => child.id === selectedChild
                                    )
                                  : children
                                ).map((child) => (
                                  <TableCell
                                    key={`${child.id}-${childExercise.id}`}
                                    align="center"
                                    size="small"
                                  >
                                    <div
                                      className="cursor-pointer flex justify-center"
                                      onClick={() =>
                                        handleStatusChange(
                                          child.id,
                                          childExercise.id,
                                          apiStatusToDisplay[
                                            child.progress.find(
                                              (p) =>
                                                p.skillId === childExercise.id
                                            )?.status
                                          ]
                                        )
                                      }
                                    >
                                      {renderStatusIcon(
                                        apiStatusToDisplay[
                                          child.progress.find(
                                            (p) =>
                                              p.skillId === childExercise.id
                                          )?.status
                                        ]
                                      )}
                                    </div>
                                  </TableCell>
                                ))}
                              </TableRow>
                            ))}
                        </React.Fragment>
                      ))}
                  </React.Fragment>
                ))}
            </TableBody>
          )}
        </Table>
      </TableContainer>
    </div>
  );
};

export default CollapsibleTable;
