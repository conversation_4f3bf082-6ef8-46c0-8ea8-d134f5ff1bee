import React from "react";
import { Box, Typography, Stack, colors } from "@mui/material";
import { tableColors, tableLetters } from "./constants";

const Legend = () => {
  return (
    <Box>
      <Stack
        direction="row"
        spacing={1}
        style={{
          marginBottom: "20px",
        }}
        sx={{ overflow: 'auto' }}
      >
        {Object.entries(tableLetters).map(([status, letter]) => (
          <Box key={status} display="flex" alignItems="center">
            <Box
              sx={{
                width: 20,
                height: 20,
                backgroundColor: tableColors[status], // Использование новых цветов
                marginRight: 1,
                display: 'flex', // Добавлено для выравнивания текста
                alignItems: 'center', // Добавлено для выравнивания текста
                justifyContent: 'center', // Добавлено для выравнивания текста
              }}
            >

            <Typography sx={{ color: 'white'}}>{letter}</Typography>
            </Box>
            <Typography>{status}</Typography>
          </Box>
        ))}
      </Stack>
    </Box>
  );
};

export default Legend;
