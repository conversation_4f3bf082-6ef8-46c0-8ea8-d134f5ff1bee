.MuiTableCell-head {
  &:first-child {
    width: 300px;

    @media screen and (max-width: 700px) {
      width: 165px;
    }
  }

}

.MuiTableHead-root {
  position: sticky;
  top: 0px;
  background-color: white;
  z-index: 1;
}

.sticky-header {
  position: sticky;
  top: 0;
  z-index: 1;
  background-color: white;
}
.MuiTableContainer-root {
  overflow: auto; /* Включает прокрутку */
  max-height: 600px; /* Ограничивает высоту контейнера для активации прокрутки */
}

.sticky-column {
  position: sticky; /* Фиксирует колонку */
  left: 0; /* Указывает, где колонка должна быть закреплена */
  z-index: 2; /* Увеличиваем приоритет, чтобы колонка не перекрывалась */
  background-color: #fff; /* Устанавливаем белый фон для видимости */
  font-weight: bold; /* Опционально — выделение текста */
  box-shadow: 2px 0 5px -2px rgba(0, 0, 0, 0.2);
}
