"use client";

import React from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Typography,
  Box,
  IconButton,
} from '@mui/material';
import ContentCopyIcon from '@mui/icons-material/ContentCopy';
import CheckIcon from '@mui/icons-material/Check';

const ProgressModal = ({ open, onClose, progressData, childName }) => {
  const [copied, setCopied] = React.useState(false);

/*************  ✨ Windsurf Command ⭐  *************/
  /**
   * Функция для копирования текста прогресса в буфер обмена
   * Использует современный API navigator.clipboard, если доступен
   * Иначе использует запасной вариант с созданием текстовой области
   * @returns {void}
   */
/*******  ca719eb6-8352-4358-9057-c9d3f9fde205  *******/
  const handleCopy = () => {
    // Используем современный API для копирования в буфер обмена
    if (navigator.clipboard && window.isSecureContext) {
      // Для современных браузеров
      navigator.clipboard.writeText(progressData)
        .then(() => {
          setCopied(true);
          setTimeout(() => setCopied(false), 2000);
        })
        .catch(err => {
          console.error('Не удалось скопировать текст: ', err);
        });
    } else {
      // Запасной вариант для старых браузеров
      const textArea = document.createElement('textarea');
      textArea.value = progressData;
      textArea.style.position = 'fixed';  // Предотвращаем прокрутку до элемента
      document.body.appendChild(textArea);
      textArea.focus();
      textArea.select();

      try {
        document.execCommand('copy');
        setCopied(true);
        setTimeout(() => setCopied(false), 2000);
      } catch (err) {
        console.error('Не удалось скопировать текст: ', err);
      }

      document.body.removeChild(textArea);
    }
  };

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="md"
      fullWidth
      sx={{
        marginTop: '60px'
      }}
    >
      <DialogTitle>
        <Box display="flex" justifyContent="space-between" alignItems="center">
          <Typography variant="h6">
            Прогресс ребенка: {childName}
          </Typography>
          <IconButton
            color={copied ? "success" : "primary"}
            onClick={handleCopy}
            title="Копировать в буфер обмена"
          >
            {copied ? <CheckIcon /> : <ContentCopyIcon />}
          </IconButton>
        </Box>
      </DialogTitle>
      <DialogContent dividers>
        <Box
          sx={{
            whiteSpace: 'pre-wrap',
            fontFamily: 'monospace',
            fontSize: '14px',
            p: 2
          }}
        >
          {progressData}
        </Box>
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose} color="primary">
          Закрыть
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default ProgressModal;
