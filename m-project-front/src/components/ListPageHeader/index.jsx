import React from 'react';
import { Box, Typography, Button } from '@mui/material';
import { Add as AddIcon } from '@mui/icons-material';

const ListPageHeader = ({ title, buttonText, onButtonClick }) => (
  <Box
    display="flex"
    justifyContent="space-between"
    alignItems="center"
    mb={3}
    sx={{ flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start' }}
  >
    <Typography variant="h4" sx={{ mb: 2 }}>{title}</Typography>
    <Button
      variant="contained"
      color="primary"
      startIcon={<AddIcon />}
      onClick={onButtonClick}
    >
      {buttonText}
    </Button>
  </Box>
);

export default ListPageHeader;