"use client";

import { pages } from "@/constants/routes";
import { AppBar, Toolbar, Typography } from "@mui/material";
import { usePathname } from "next/navigation";

export function Bar() {
  const currentPath = usePathname();
  const currentPageTitle =
    pages.find((page) => page.path === currentPath)?.name || "";

  return (
    <AppBar
      position="fixed"
      sx={{
        width: `100%`,
        backgroundColor: "#6B8E23",
        zIndex: '3000'
      }}
    >
      <Toolbar>
        <Typography variant="h6" component="div" sx={{ flexGrow: 1 }}>
          {currentPageTitle}
        </Typography>
      </Toolbar>
    </AppBar>
  );
}
