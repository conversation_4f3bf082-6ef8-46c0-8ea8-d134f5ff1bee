"use client";

import { useState, useEffect } from "react";
import axios from "@/axiosInstance";
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Button,
  Typography,
  Box,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
} from "@mui/material";
import { Add as AddIcon, Edit as EditIcon, Delete as DeleteIcon } from "@mui/icons-material";
import { useRouter } from "next/navigation";
import useAuthStore from "@/store/authStore";
import ListPageHeader from '@/components/ListPageHeader';

const SchoolsPage = () => {
  const router = useRouter();
  const { user } = useAuthStore();
  const [schools, setSchools] = useState([]);
  const [loading, setLoading] = useState(false);
  const [errorMessage, setErrorMessage] = useState(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [selectedSchool, setSelectedSchool] = useState(null);

  useEffect(() => {
    fetchSchools();
  }, []);

  const fetchSchools = async () => {
    setLoading(true);
    try {
      const response = await axios.get("/schools");
      setSchools(response.data);
      setErrorMessage(null);
    } catch (error) {
      setErrorMessage("Ошибка загрузки списка школ.");
      console.error("Error fetching schools:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleAddSchool = () => {
    router.push("/schools/create");
  };

  const handleEditSchool = (schoolId) => {
    router.push(`/schools/${schoolId}/edit`);
  };

  const handleDeleteClick = (school) => {
    setSelectedSchool(school);
    setDeleteDialogOpen(true);
  };

  const handleDeleteConfirm = async () => {
    if (!selectedSchool) return;

    setLoading(true);
    try {
      await axios.delete(`/schools/${selectedSchool.id}`);
      setDeleteDialogOpen(false);
      setSelectedSchool(null);
      fetchSchools();
    } catch (error) {
      setErrorMessage("Ошибка при удалении школы.");
      console.error("Error deleting school:", error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return <Box>Загрузка...</Box>;
  }

  if (errorMessage) {
    return (
      <Box color="error.main">
        {errorMessage}
      </Box>
    );
  }

  return (
    <Box>
      <ListPageHeader
        title="Школы"
        buttonText="Добавить школу"
        onButtonClick={handleAddSchool}
      />

      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>Название</TableCell>
              <TableCell>Адрес</TableCell>
              <TableCell>Телефон</TableCell>
              <TableCell>Действия</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {schools.map((school) => (
              <TableRow key={school.id}>
                <TableCell>{school.name}</TableCell>
                <TableCell>{school.address}</TableCell>
                <TableCell>{school.contact}</TableCell>
                <TableCell>
                  <Button
                    size="small"
                    startIcon={<EditIcon />}
                    onClick={() => handleEditSchool(school.id)}
                    sx={{ mr: 1 }}
                  >
                    Редактировать
                  </Button>
                  <Button
                    size="small"
                    color="error"
                    startIcon={<DeleteIcon />}
                    onClick={() => handleDeleteClick(school)}
                  >
                    Удалить
                  </Button>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>

      <Dialog
        open={deleteDialogOpen}
        onClose={() => setDeleteDialogOpen(false)}
      >
        <DialogTitle>Подтверждение удаления</DialogTitle>
        <DialogContent>
          Вы уверены, что хотите удалить школу "{selectedSchool?.name}"?
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialogOpen(false)}>Отмена</Button>
          <Button onClick={handleDeleteConfirm} color="error" autoFocus>
            Удалить
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default SchoolsPage;