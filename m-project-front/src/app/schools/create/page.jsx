"use client";

import { useState } from "react";
import { useForm } from "react-hook-form";
import { useRouter } from "next/navigation";
import axios from "@/axiosInstance";
import {
  Container,
  Box,
  Typography,
  TextField,
  Button,
  CircularProgress,
  Alert,
} from "@mui/material";

export default function AddSchoolPage() {
  const router = useRouter();
  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
  } = useForm();
  const [loading, setLoading] = useState(false);
  const [errorMessage, setErrorMessage] = useState(null);

  // 🔹 Отправка формы для создания школы
  const onSubmit = async (data) => {
    setLoading(true);
    setErrorMessage(null);

    try {
      await axios.post("/schools", data);
      reset();
      router.push("/schools"); // Перенаправление на список школ после добавления
    } catch (error) {
      setErrorMessage(
        error.response?.data?.message || "Ошибка при создании школы"
      );
    } finally {
      setLoading(false);
    }
  };

  return (
    <Container maxWidth="sm">
      <Box display="flex" flexDirection="column" alignItems="center" mt={4}>
        <Typography variant="h4" gutterBottom>
          Добавить новую школу
        </Typography>

        {errorMessage && (
          <Alert severity="error" sx={{ width: "100%", mb: 2 }}>
            {errorMessage}
          </Alert>
        )}

        <Box
          component="form"
          onSubmit={handleSubmit(onSubmit)}
          width="100%"
          mt={2}
        >
          <TextField
            fullWidth
            label="Название школы"
            variant="outlined"
            margin="normal"
            {...register("name", { required: "Введите название школы" })}
            error={!!errors.name}
            helperText={errors.name?.message}
          />
          <TextField
            fullWidth
            label="Адрес"
            variant="outlined"
            margin="normal"
            {...register("address", { required: "Введите адрес" })}
            error={!!errors.address}
            helperText={errors.address?.message}
          />
          <TextField
            fullWidth
            label="Описание"
            variant="outlined"
            margin="normal"
            {...register("description")}
          />
          <TextField
            fullWidth
            label="Контактный телефон"
            variant="outlined"
            margin="normal"
            {...register("contact", { required: "Введите контактный телефон" })}
            error={!!errors.contact}
            helperText={errors.contact?.message}
          />
          <TextField
            fullWidth
            label="Город"
            variant="outlined"
            margin="normal"
            {...register("location", { required: "Введите город" })}
            error={!!errors.location}
            helperText={errors.location?.message}
          />

          <Button
            type="submit"
            fullWidth
            variant="contained"
            color="primary"
            disabled={loading}
            sx={{ mt: 2 }}
          >
            {loading ? <CircularProgress size={24} /> : "Создать школу"}
          </Button>
        </Box>
      </Box>
    </Container>
  );
}
