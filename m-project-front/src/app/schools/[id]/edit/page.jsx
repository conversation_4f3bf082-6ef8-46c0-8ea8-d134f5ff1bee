"use client";

import { useState, useEffect, use } from "react";
import { useRouter } from "next/navigation";
import {
  Box,
  Button,
  TextField,
  Typography,
  Container,
  CircularProgress,
  Alert,
} from "@mui/material";
import { useForm } from "react-hook-form";
import axios from "@/axiosInstance";
import useAuthStore from "@/store/authStore";

const EditSchoolPage = ({ params }) => {
  const router = useRouter();
  const resolvedParams = use(params);
  const { id } = resolvedParams;
  const [loading, setLoading] = useState(false);
  const [errorMessage, setErrorMessage] = useState(null);

  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
  } = useForm();

  useEffect(() => {
    fetchSchool();
  }, [id]);

  const fetchSchool = async () => {
    setLoading(true);
    try {
      const response = await axios.get(`/schools/${id}`);
      const school = response.data;
      
      // Заполняем форму данными школы
      setValue("name", school.name);
      setValue("address", school.address);
      setValue("contact", school.contact);
      
    } catch (error) {
      console.error("Error fetching school:", error);
      setErrorMessage("Ошибка загрузки данных школы.");
    } finally {
      setLoading(false);
    }
  };

  const onSubmit = async (data) => {
    setLoading(true);
    setErrorMessage(null);

    try {
      await axios.patch(`/schools/${id}`, data);
      router.push("/schools");
    } catch (error) {
      console.error("Ошибка:", error.response?.data);
      setErrorMessage(
        error.response?.data?.message || "Ошибка при обновлении школы"
      );
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return <Box p={3}>Загрузка...</Box>;
  }

  return (
    <Container maxWidth="sm">
      <Box display="flex" flexDirection="column" alignItems="center" mt={4}>
        <Typography variant="h4" gutterBottom>
          Редактирование школы
        </Typography>

        {errorMessage && (
          <Alert severity="error" sx={{ width: "100%", mb: 2 }}>
            {errorMessage}
          </Alert>
        )}

        <Box
          component="form"
          onSubmit={handleSubmit(onSubmit)}
          width="100%"
          mt={2}
        >
          <TextField
            fullWidth
            label="Название школы"
            variant="outlined"
            margin="normal"
            {...register("name", { required: "Название школы обязательно" })}
            error={!!errors.name}
            helperText={errors.name?.message}
          />

          <TextField
            fullWidth
            label="Адрес"
            variant="outlined"
            margin="normal"
            {...register("address", { required: "Адрес обязателен" })}
            error={!!errors.address}
            helperText={errors.address?.message}
          />

          <TextField
            fullWidth
            label="Телефон"
            variant="outlined"
            margin="normal"
            {...register("contact", { required: "Телефон обязателен" })}
            error={!!errors.contact}
            helperText={errors.contact?.message}
          />

          <Button
            type="submit"
            fullWidth
            variant="contained"
            color="primary"
            disabled={loading}
            sx={{ mt: 2 }}
          >
            {loading ? <CircularProgress size={24} /> : "Сохранить изменения"}
          </Button>
        </Box>
      </Box>
    </Container>
  );
};

export default EditSchoolPage; 