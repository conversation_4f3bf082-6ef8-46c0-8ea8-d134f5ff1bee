"use client";

import { useEffect } from "react";
import { useRouter } from "next/navigation";
import useAuthStore from "@/store/authStore";
import { ROLES } from '@/constants/roles';

const SchoolsLayout = ({ children }) => {
  const router = useRouter();
  const { user } = useAuthStore();

  useEffect(() => {

    console.log(user)
    if (!user || user.role !== ROLES.SUPERADMIN) {
      router.push("/");
    }
  }, [user, router]);

  // Если пользователь не superadmin, не рендерим содержимое
  if (!user || user.role !== ROLES.SUPERADMIN) {
    return null;
  }

  return <>{children}</>;
};

export default SchoolsLayout; 