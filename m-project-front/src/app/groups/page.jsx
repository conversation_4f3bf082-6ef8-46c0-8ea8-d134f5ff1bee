"use client";

import { useState, useEffect } from "react";
import axios from "@/axiosInstance";
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Button,
  Typography,
  Box,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
} from "@mui/material";
import { Add as AddIcon, Edit as EditIcon, Delete as DeleteIcon } from "@mui/icons-material";
import { useRouter } from "next/navigation";
import useAuthStore from "@/store/authStore";
import ListPageHeader from '@/components/ListPageHeader';

const GroupsPage = () => {
  const router = useRouter();
  const { user } = useAuthStore();
  const [groups, setGroups] = useState([]);
  const [ageGroups, setAgeGroups] = useState([]);
  const [loading, setLoading] = useState(false);
  const [errorMessage, setErrorMessage] = useState(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [selectedGroup, setSelectedGroup] = useState(null);

  useEffect(() => {
    fetchGroups();
    fetchAgeGroups();
  }, []);

  const fetchAgeGroups = async () => {
    try {
      const response = await axios.get('/skills-category/age-groups/all');
      setAgeGroups(response.data);
    } catch (error) {
      console.error('Error fetching age groups:', error);
    }
  };

  const fetchGroups = async () => {
    setLoading(true);
    try {
      const response = await axios.get("/groups");
      setGroups(response.data);
      setErrorMessage(null);
    } catch (error) {
      setErrorMessage("Ошибка загрузки списка групп.");
      console.error("Error fetching groups:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleAddGroup = () => {
    router.push("/groups/create");
  };

  const handleEditGroup = (groupId) => {
    router.push(`/groups/${groupId}/edit`);
  };

  const handleDeleteClick = (group) => {
    setSelectedGroup(group);
    setDeleteDialogOpen(true);
  };

  const handleDeleteConfirm = async () => {
    if (!selectedGroup) return;

    setLoading(true);
    try {
      await axios.delete(`/groups/${selectedGroup.id}`);
      setDeleteDialogOpen(false);
      setSelectedGroup(null);
      fetchGroups(); // Обновляем список после удаления
    } catch (error) {
      setErrorMessage("Ошибка при удалении группы.");
      console.error("Error deleting group:", error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return <Box>Загрузка...</Box>;
  }

  if (errorMessage) {
    return (
      <Box color="error.main">
        {errorMessage}
      </Box>
    );
  }

  return (
    <Box>
      <ListPageHeader
        title="Группы"
        buttonText="Добавить группу"
        onButtonClick={handleAddGroup}
      />

      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>Название группы</TableCell>
              <TableCell>Возрастная группа</TableCell>
              <TableCell>Описание</TableCell>
              <TableCell>Действия</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {groups.map((group) => (
              <TableRow key={group.id}>
                <TableCell>{group.name}</TableCell>
                <TableCell>
                  {ageGroups.find(ag => ag.id === group.ageGroupId)?.name || 'Не указана'}
                </TableCell>
                <TableCell>{group.description}</TableCell>
                <TableCell>
                  <Button
                    size="small"
                    startIcon={<EditIcon />}
                    onClick={() => handleEditGroup(group.id)}
                    sx={{ mr: 1 }}
                  >
                    Редактировать
                  </Button>
                  <Button
                    size="small"
                    color="error"
                    startIcon={<DeleteIcon />}
                    onClick={() => handleDeleteClick(group)}
                  >
                    Удалить
                  </Button>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>

      <Dialog
        open={deleteDialogOpen}
        onClose={() => setDeleteDialogOpen(false)}
      >
        <DialogTitle>Подтверждение удаления</DialogTitle>
        <DialogContent>
          Вы уверены, что хотите удалить группу "{selectedGroup?.name}"?
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialogOpen(false)}>Отмена</Button>
          <Button onClick={handleDeleteConfirm} color="error" autoFocus>
            Удалить
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default GroupsPage;