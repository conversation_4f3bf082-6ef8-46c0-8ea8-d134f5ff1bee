"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import {
  Box,
  Button,
  TextField,
  Typography,
  Container,
  CircularProgress,
  Alert,
  MenuItem,
  FormControl,
  InputLabel,
  Select,
} from "@mui/material";
import { useForm, Controller } from "react-hook-form";
import axios from "@/axiosInstance";
import useAuthStore from "@/store/authStore";

const CreateGroupPage = () => {
  const router = useRouter();
  const { user } = useAuthStore();
  const [loading, setLoading] = useState(false);
  const [errorMessage, setErrorMessage] = useState(null);
  const [ageGroups, setAgeGroups] = useState([]);

  const {
    register,
    handleSubmit,
    control,
    formState: { errors },
  } = useForm({
    defaultValues: {
      ageGroupId: 'children_3_6', // По умолчанию дети 3-6 лет
    },
  });

  useEffect(() => {
    // Загрузка возрастных групп
    const fetchAgeGroups = async () => {
      try {
        const response = await axios.get('/skills-category/age-groups/all');
        setAgeGroups(response.data);
      } catch (error) {
        console.error('Error fetching age groups:', error);
      }
    };

    fetchAgeGroups();
  }, []);

  const onSubmit = async (data) => {
    setLoading(true);
    setErrorMessage(null);

    try {
      data.schoolId = user.schoolId;
      data.teacherIds = [];

      console.log("Отправляемые данные:", data);

      await axios.post("/groups", data);
      router.push("/groups");
    } catch (error) {
      console.error("Ошибка:", error.response?.data);
      setErrorMessage(
        error.response?.data?.message || "Ошибка при создании группы"
      );
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return <Box p={3}>Загрузка...</Box>;
  }

  return (
    <Container maxWidth="sm">
      <Box display="flex" flexDirection="column" alignItems="center" mt={4}>
        <Typography variant="h4" gutterBottom>
          Создание группы
        </Typography>

        {errorMessage && (
          <Alert severity="error" sx={{ width: "100%", mb: 2 }}>
            {errorMessage}
          </Alert>
        )}

        <Box
          component="form"
          onSubmit={handleSubmit(onSubmit)}
          width="100%"
          mt={2}
        >
          <TextField
            fullWidth
            label="Название группы"
            variant="outlined"
            margin="normal"
            {...register("name", { required: "Название группы обязательно" })}
            error={!!errors.name}
            helperText={errors.name?.message}
          />

          <Controller
            name="ageGroupId"
            control={control}
            rules={{ required: "Возрастная группа обязательна" }}
            render={({ field }) => (
              <FormControl fullWidth margin="normal">
                <InputLabel id="age-group-label">Возрастная группа</InputLabel>
                <Select
                  {...field}
                  labelId="age-group-label"
                  label="Возрастная группа"
                  error={!!errors.ageGroupId}
                >
                  {ageGroups.map((ageGroup) => (
                    <MenuItem key={ageGroup.id} value={ageGroup.id}>
                      {ageGroup.name}
                    </MenuItem>
                  ))}
                </Select>
                {errors.ageGroupId && (
                  <Typography color="error" variant="caption">
                    {errors.ageGroupId.message}
                  </Typography>
                )}
              </FormControl>
            )}
          />

          <TextField
            fullWidth
            label="Описание"
            variant="outlined"
            margin="normal"
            multiline
            rows={4}
            {...register("description")}
          />
          <Button
            type="submit"
            fullWidth
            variant="contained"
            color="primary"
            disabled={loading}
            sx={{ mt: 2 }}
          >
            {loading ? <CircularProgress size={24} /> : "Создать группу"}
          </Button>
        </Box>
      </Box>
    </Container>
  );
};

export default CreateGroupPage;