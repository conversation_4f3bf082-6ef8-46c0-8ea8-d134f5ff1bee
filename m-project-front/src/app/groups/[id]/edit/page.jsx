"use client";

import { useState, useEffect, use } from "react";
import { useRouter } from "next/navigation";
import {
  Box,
  Button,
  TextField,
  Typography,
  Container,
  CircularProgress,
  Alert,
  MenuItem,
  Checkbox,
  ListItemText,
  FormControl,
  InputLabel,
  Select,
} from "@mui/material";
import { useForm, Controller } from "react-hook-form";
import axios from "@/axiosInstance";
import useAuthStore from "@/store/authStore";

const EditGroupPage = ({ params }) => {
  const router = useRouter();
  const resolvedParams = use(params);
  const { id } = resolvedParams;
  const { user } = useAuthStore();
  const [loading, setLoading] = useState(false);
  const [errorMessage, setErrorMessage] = useState(null);
  const [ageGroups, setAgeGroups] = useState([]);

  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    control,
  } = useForm();

  useEffect(() => {
    // Загрузка возрастных групп
    const fetchAgeGroups = async () => {
      try {
        const response = await axios.get('/skills-category/age-groups/all');
        setAgeGroups(response.data);
      } catch (error) {
        console.error('Error fetching age groups:', error);
      }
    };

    fetchAgeGroups();
  }, []);

  useEffect(() => {
    fetchGroup();
  }, [id]);

  const fetchGroup = async () => {
    setLoading(true);
    try {
      const response = await axios.get(`/groups/${id}`);
      const group = response.data;

      // Заполняем форму данными группы
      setValue("name", group.name);
      setValue("description", group.description);
      setValue("ageGroupId", group.ageGroupId || "children_3_6"); // Устанавливаем возрастную группу

    } catch (error) {
      console.error("Error fetching group:", error);
      setErrorMessage("Ошибка загрузки данных группы.");
    } finally {
      setLoading(false);
    }
  };

  const onSubmit = async (data) => {
    setLoading(true);
    setErrorMessage(null);

    try {
      const updateData = {
        ...data,
      };

      await axios.patch(`/groups/${id}`, updateData);
      router.push("/groups");
    } catch (error) {
      console.error("Ошибка:", error.response?.data);
      setErrorMessage(
        error.response?.data?.message || "Ошибка при обновлении группы"
      );
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return <Box p={3}>Загрузка...</Box>;
  }

  return (
    <Container maxWidth="sm">
      <Box display="flex" flexDirection="column" alignItems="center" mt={4}>
        <Typography variant="h4" gutterBottom>
          Редактирование группы
        </Typography>

        {errorMessage && (
          <Alert severity="error" sx={{ width: "100%", mb: 2 }}>
            {errorMessage}
          </Alert>
        )}

        <Box
          component="form"
          onSubmit={handleSubmit(onSubmit)}
          width="100%"
          mt={2}
        >
          <TextField
            fullWidth
            label="Название группы"
            variant="outlined"
            margin="normal"
            {...register("name", { required: "Название группы обязательно" })}
            error={!!errors.name}
            helperText={errors.name?.message}
          />

          <Controller
            name="ageGroupId"
            control={control}
            rules={{ required: "Возрастная группа обязательна" }}
            render={({ field }) => (
              <FormControl fullWidth margin="normal">
                <InputLabel id="age-group-label">Возрастная группа</InputLabel>
                <Select
                  {...field}
                  labelId="age-group-label"
                  label="Возрастная группа"
                  error={!!errors.ageGroupId}
                >
                  {ageGroups.map((ageGroup) => (
                    <MenuItem key={ageGroup.id} value={ageGroup.id}>
                      {ageGroup.name}
                    </MenuItem>
                  ))}
                </Select>
                {errors.ageGroupId && (
                  <Typography color="error" variant="caption">
                    {errors.ageGroupId.message}
                  </Typography>
                )}
              </FormControl>
            )}
          />

          <TextField
            fullWidth
            label="Описание"
            variant="outlined"
            margin="normal"
            multiline
            rows={4}
            {...register("description")}
          />

          <Button
            type="submit"
            fullWidth
            variant="contained"
            color="primary"
            disabled={loading}
            sx={{ mt: 2 }}
          >
            {loading ? <CircularProgress size={24} /> : "Сохранить изменения"}
          </Button>
        </Box>
      </Box>
    </Container>
  );
};

export default EditGroupPage;