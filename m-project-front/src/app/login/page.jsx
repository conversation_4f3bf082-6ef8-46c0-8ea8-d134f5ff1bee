"use client";

import { useState, useEffect, Suspense } from "react";
import { useForm } from "react-hook-form";
import { useRouter, useSearchParams } from "next/navigation";
import useAuthStore from "@/store/authStore";
import { TextField, Button, Container, Box, Typography, Alert, CircularProgress } from "@mui/material";

// Компонент для работы с параметрами URL
function LoginContent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { login, isAuthenticated } = useAuthStore();

  // Получаем параметр redirectTo из URL
  const redirectTo = searchParams.get('redirectTo') || '/';
  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm();

  const [errorMessage, setErrorMessage] = useState(null);
  const [loading, setLoading] = useState(false);

  // Проверяем авторизацию при загрузке страницы
  useEffect(() => {
    console.log('useEffect в LoginPage: isAuthenticated =', isAuthenticated, 'redirectTo =', redirectTo);
    if (isAuthenticated) {
      console.log('Перенаправление на:', redirectTo);
      router.push(redirectTo);
    }
  }, [isAuthenticated, router, redirectTo]);

  const onSubmit = async (data) => {
    setErrorMessage(null);
    setLoading(true);

    try {
      await login(data.email, data.password);
      // Перенаправление будет выполнено в useEffect при изменении isAuthenticated
    } catch (error) {
      setErrorMessage(error.response?.data?.message || "Ошибка авторизации");
    } finally {
      setLoading(false);
    }
  };

  return (
    <Container maxWidth="xs">
      <Box display="flex" flexDirection="column" alignItems="center" mt={8}>
        <Typography variant="h4" component="h1" gutterBottom>
          Вход в систему
        </Typography>

        {errorMessage && <Alert severity="error" sx={{ width: "100%", mb: 2 }}>{errorMessage}</Alert>}

        <Box component="form" onSubmit={handleSubmit(onSubmit)} width="100%" mt={2}>
          <TextField
            fullWidth
            label="Email"
            variant="outlined"
            margin="normal"
            {...register("email", { required: "Введите email" })}
            error={!!errors.email}
            helperText={errors.email?.message}
          />
          <TextField
            fullWidth
            label="Пароль"
            variant="outlined"
            type="password"
            margin="normal"
            {...register("password", { required: "Введите пароль" })}
            error={!!errors.password}
            helperText={errors.password?.message}
          />

          <Button
            type="submit"
            fullWidth
            variant="contained"
            color="primary"
            disabled={loading}
            sx={{ mt: 2 }}
          >
            {loading ? <CircularProgress size={24} /> : "Войти"}
          </Button>
        </Box>
      </Box>
    </Container>
  );
}

// Основной компонент страницы
export default function LoginPage() {
  return (
    <Suspense fallback={<div>Загрузка...</div>}>
      <LoginContent />
    </Suspense>
  );
}
