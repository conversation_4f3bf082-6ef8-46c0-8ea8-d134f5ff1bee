"use client";

import { useState, useEffect } from "react";
import {
  Box,
  Typography,
  Paper,
  Button,
  CircularProgress,
  Alert,
} from "@mui/material";
import { Add as AddIcon, Edit as EditIcon } from "@mui/icons-material";
import { useRouter } from "next/navigation";
import useAuthStore from "@/store/authStore";
import axios from "@/axiosInstance";
import { EmbedPDF } from "@simplepdf/react-embed-pdf";

export default function HomePage() {
  const [calendar, setCalendar] = useState(null);
  const [loading, setLoading] = useState(true);
  const [errorMessage, setErrorMessage] = useState(null);
  const router = useRouter();
  const user = useAuthStore((state) => state.user);

  useEffect(() => {
    const fetchCalendar = async () => {
      if (!user?.schoolId) return;

      try {
        setLoading(true);
        const response = await axios.get(`calendar/${user.schoolId}`);
        setCalendar(response.data);
      } catch (error) {
        console.error("Ошибка при загрузке календаря:", error);
        if (error.response?.status !== 404) {
          setErrorMessage(
            error.response?.data?.message || "Ошибка при загрузке календаря"
          );
        }
      } finally {
        setLoading(false);
      }
    };

    fetchCalendar();
  }, [user]);

  const handleAddCalendar = () => {
    router.push("/calendar/create");
  };

  const handleEditCalendar = () => {
    router.push("/calendar/edit");
  };

  if (loading) {
    return (
      <Box p={3} display="flex" justifyContent="center">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box>
      <Box
        display="flex"
        justifyContent="space-between"
        alignItems="center"
        mb={3}
      >
        <Typography variant="h4">Календарь событий месяца</Typography>

        <Box>
          {calendar?.fileUrl ? (
            <Button
              variant="contained"
              color="primary"
              startIcon={<EditIcon />}
              onClick={handleEditCalendar}
            >
              Обновить
            </Button>
          ) : (
            <Button
              variant="contained"
              color="primary"
              startIcon={<AddIcon />}
              onClick={handleAddCalendar}
            >
              Загрузить
            </Button>
          )}
        </Box>
      </Box>

      {errorMessage && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {errorMessage}
        </Alert>
      )}

      <Paper sx={{ p: 3, mb: 4 }}>
        <Typography variant="h5" gutterBottom>
          Добро пожаловать в Montessori School!
        </Typography>
        <Typography variant="body1" paragraph>
          Используйте боковое меню для навигации по разделам. Ниже вы можете
          ознакомиться с календарем событий на текущий месяц.
        </Typography>
      </Paper>

      {calendar?.fileUrl ? (
        <Paper sx={{ p: 2 }}>
          <EmbedPDF
            mode="inline"
            style={{ width: "100%", height: "600px" }}
            companyIdentifier="react-viewer"
            documentURL={`${calendar.fileUrl}`}
          />
        </Paper>
      ) : (
        <Alert severity="info">Календарь событий еще не загружен</Alert>
      )}
    </Box>
  );
}
