"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { Worker, Viewer } from '@react-pdf-viewer/core';
import { defaultLayoutPlugin } from '@react-pdf-viewer/default-layout';
import '@react-pdf-viewer/core/lib/styles/index.css';
import '@react-pdf-viewer/default-layout/lib/styles/index.css';
import {
  Box,
  Button,
  Typography,
  Container,
  CircularProgress,
  Alert,
  Paper,
} from "@mui/material";
import { Upload as UploadIcon } from "@mui/icons-material";

// Моковые данные
const MOCK_MENU = {
  id: '1',
  fileUrl: '/sample-menu.pdf',
  updatedAt: new Date().toISOString(),
};

const EditMenuPage = () => {
  const router = useRouter();
  const [file, setFile] = useState(null);
  const [currentMenu, setCurrentMenu] = useState(null);
  const [loading, setLoading] = useState(false);
  const [errorMessage, setErrorMessage] = useState(null);
  const [preview, setPreview] = useState(null);

  const defaultLayoutPluginInstance = defaultLayoutPlugin();

  useEffect(() => {
    // Имитация загрузки текущего меню
    setTimeout(() => {
      setCurrentMenu(MOCK_MENU);
    }, 1000);
  }, []);

  const handleFileChange = (event) => {
    const selectedFile = event.target.files[0];
    if (selectedFile && selectedFile.type === "application/pdf") {
      setFile(selectedFile);
      setPreview(URL.createObjectURL(selectedFile));
      setErrorMessage(null);
    } else {
      setErrorMessage("Пожалуйста, выберите PDF файл");
      setFile(null);
      setPreview(null);
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!file) {
      setErrorMessage("Пожалуйста, выберите файл");
      return;
    }

    setLoading(true);
    setErrorMessage(null);

    // Имитация обновления
    setTimeout(() => {
      setLoading(false);
      router.push("/menu");
    }, 2000);
  };

  return (
    <Container maxWidth="md">
      <Box display="flex" flexDirection="column" alignItems="center" mt={4}>
        <Typography variant="h4" gutterBottom>
          Обновление меню
        </Typography>

        {errorMessage && (
          <Alert severity="error" sx={{ width: "100%", mb: 2 }}>
            {errorMessage}
          </Alert>
        )}

        <Paper sx={{ p: 3, width: "100%" }}>
          {currentMenu && (
            <Box mb={4}>
              <Typography variant="h6" gutterBottom>
                Текущее меню:
              </Typography>
              <Box height="400px">
                <Worker workerUrl="https://unpkg.com/pdfjs-dist@3.4.120/build/pdf.worker.min.js">
                  <Viewer
                    fileUrl={currentMenu.fileUrl}
                    plugins={[defaultLayoutPluginInstance]}
                  />
                </Worker>
              </Box>
            </Box>
          )}

          <Box
            component="form"
            onSubmit={handleSubmit}
            display="flex"
            flexDirection="column"
            alignItems="center"
          >
            <Button
              variant="outlined"
              component="label"
              startIcon={<UploadIcon />}
              sx={{ mb: 2 }}
            >
              Выбрать новый PDF файл
              <input
                type="file"
                hidden
                accept="application/pdf"
                onChange={handleFileChange}
              />
            </Button>

            {preview && (
              <Box mb={2} width="100%" height="400px">
                <Typography variant="h6" gutterBottom>
                  Предпросмотр нового меню:
                </Typography>
                <Worker workerUrl="https://unpkg.com/pdfjs-dist@3.4.120/build/pdf.worker.min.js">
                  <Viewer
                    fileUrl={preview}
                    plugins={[defaultLayoutPluginInstance]}
                  />
                </Worker>
              </Box>
            )}

            <Button
              type="submit"
              variant="contained"
              color="primary"
              disabled={loading || !file}
              sx={{ mt: 2 }}
            >
              {loading ? <CircularProgress size={24} /> : "Обновить меню"}
            </Button>
          </Box>
        </Paper>
      </Box>
    </Container>
  );
};

export default EditMenuPage; 