"use client";

import { useState, useEffect } from "react";
import {
  Box,
  Button,
  Typography,
  Paper,
  CircularProgress,
  Alert,
} from "@mui/material";
import { Add as AddIcon } from "@mui/icons-material";
import { useRouter } from "next/navigation";
import useAuthStore from "@/store/authStore";
import axios from "@/axiosInstance";
import { EmbedPDF } from "@simplepdf/react-embed-pdf";
import ListPageHeader from '@/components/ListPageHeader';

const MenuPage = () => {
  const router = useRouter();
  const { user } = useAuthStore();
  const [menu, setMenu] = useState(null);
  const [loading, setLoading] = useState(false);
  const [errorMessage, setErrorMessage] = useState(null);

  useEffect(() => {
    if (user?.schoolId) {
      fetchMenu();
    }
  }, [user?.schoolId]);

  const fetchMenu = async () => {
    setLoading(true);
    try {
      const response = await axios.get(`/menu/${user?.schoolId}`);
      setMenu(response.data);
      setErrorMessage(null);
    } catch (error) {
      setErrorMessage("Ошибка загрузки меню.");
      console.error("Error fetching menu:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleAddMenu = () => {
    router.push("/menu/create");
  };

  const handleEditMenu = () => {
    router.push("/menu/edit");
  };

  if (loading) {
    return (
      <Box p={3} display="flex" justifyContent="center">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box>
      <ListPageHeader
        title="Меню"
        buttonText="Загрузить"
        onButtonClick={handleAddMenu}
      />

      {errorMessage && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {errorMessage}
        </Alert>
      )}

      {menu?.fileUrl ? (
        <Paper sx={{ p: 2 }}>
          <EmbedPDF
            mode="inline"
            style={{ width: "1000px", height: "500px" }}
            companyIdentifier="react-viewer"
            documentURL={`${menu.fileUrl}`}
          />
        </Paper>
      ) : (
        <Alert severity="info">Меню еще не загружено</Alert>
      )}
    </Box>
  );
};

export default MenuPage;
