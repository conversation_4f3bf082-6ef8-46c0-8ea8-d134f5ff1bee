"use client";

import { useState, useEffect } from "react";
import axios from "@/axiosInstance";
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Button,
  Typography,
  Box,
} from "@mui/material";
import { Add as AddIcon } from "@mui/icons-material";
import { useRouter } from "next/navigation";
import ListPageHeader from '@/components/ListPageHeader';

const UsersPage = () => {
  const router = useRouter();
  const [users, setUsers] = useState([]);
  const [loading, setLoading] = useState(false);
  const [errorMessage, setErrorMessage] = useState(null);

  useEffect(() => {
    fetchUsers();
  }, []);

  const fetchUsers = async () => {
    setLoading(true);
    try {
      const response = await axios.get("/users");
      setUsers(response.data);
      setErrorMessage(null);
    } catch (error) {
      setErrorMessage("Ошибка загрузки списка пользователей.");
      console.error("Error fetching users:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleAddUser = () => {
    router.push("/users/create");
  };

  const getRoleName = (role) => {
    const roles = {
      SUPER_ADMIN: "Супер админ",
      ADMIN: "Администратор",
      TEACHER: "Учитель",
      PARENT: "Родитель",
    };
    return roles[role] || role;
  };

  if (loading) {
    return <Box p={3}>Загрузка...</Box>;
  }

  if (errorMessage) {
    return (
      <Box p={3} color="error.main">
        {errorMessage}
      </Box>
    );
  }

  return (
    <Box>
      <ListPageHeader
        title="Пользователи"
        buttonText="Добавить пользователя"
        onButtonClick={handleAddUser}
      />

      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>Email</TableCell>
              <TableCell>Имя</TableCell>
              <TableCell>Фамилия</TableCell>
              <TableCell>Роль</TableCell>
              <TableCell>Телефон</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {users.map((user) => (
              <TableRow key={user.id}>
                <TableCell>{user.email}</TableCell>
                <TableCell>{user.firstName}</TableCell>
                <TableCell>{user.lastName}</TableCell>
                <TableCell>{getRoleName(user.role)}</TableCell>
                <TableCell>{user.phone}</TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>
    </Box>
  );
};

export default UsersPage;
