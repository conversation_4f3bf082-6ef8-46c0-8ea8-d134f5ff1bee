"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import {
  Box,
  Button,
  TextField,
  MenuItem,
  Typography,
  Paper,
  Grid,
  Container,
  CircularProgress,
  Alert,
} from "@mui/material";
import { useForm, registerler } from "react-hook-form";
import axios from "@/axiosInstance";
import useAuthStore from "@/store/authStore";
import { ROLES } from '@/constants/roles';

const CreateUserPage = () => {
  const router = useRouter();
  const { user } = useAuthStore();
  const [schools, setSchools] = useState([]);
  const [loading, setLoading] = useState(false);
  const [errorMessage, setErrorMessage] = useState(null);

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm();

  useEffect(() => {
    if (user?.role === ROLES.SUPERADMIN) {
      fetchSchools();
    }
  }, [user]);

  const fetchSchools = async () => {
    setLoading(true);
    try {
      const response = await axios.get("/schools");
      setSchools(response.data);
      setErrorMessage(null);
    } catch (error) {
      setErrorMessage("Ошибка загрузки списка школ.");
      console.error("Error fetching schools:", error);
    } finally {
      setLoading(false);
    }
  };

  const onSubmit = async (data) => {
    setLoading(true);
    setErrorMessage(null);

    try {
      if (user?.role === "admin") {
        data.schoolId = user.schoolId;
      }

      data.profilePicture = "";

      console.log("Отправляемые данные:", data);

      await axios.post("/users", data);
      router.push("/users");
    } catch (error) {
      console.error("Ошибка:", error.response?.data);
      setErrorMessage(
        error.response?.data?.message || "Ошибка при создании пользователя"
      );
    } finally {
      setLoading(false);
    }
  };

  const availableRoles =
    user?.role === ROLES.SUPERADMIN
      ? [ROLES.ADMIN, ROLES.TEACHER, ROLES.PARENT]
      : [ROLES.TEACHER, ROLES.PARENT];

  if (loading) {
    return <Box p={3}>Загрузка...</Box>;
  }

  return (
    <Container maxWidth="sm">
      <Box display="flex" flexDirection="column" alignItems="center" mt={4}>
        <Typography variant="h4" gutterBottom>
          Создание пользователя
        </Typography>

        {errorMessage && (
          <Alert severity="error" sx={{ width: "100%", mb: 2 }}>
            {errorMessage}
          </Alert>
        )}

        <Box
          component="form"
          onSubmit={handleSubmit(onSubmit)}
          width="100%"
          mt={2}
        >
          <TextField
            fullWidth
            label="Email"
            variant="outlined"
            margin="normal"
            {...register("email", {
              required: "Email обязателен",
              pattern: {
                value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                message: "Некорректный email",
              },
            })}
            error={!!errors.email}
            helperText={errors.email?.message}
          />

          <TextField
            fullWidth
            label="Пароль"
            type="password"
            variant="outlined"
            margin="normal"
            {...register("password", {
              required: "Пароль обязателен",
              minLength: {
                value: 6,
                message: "Минимальная длина пароля 6 символов",
              },
            })}
            error={!!errors.password}
            helperText={errors.password?.message}
          />

          <TextField
            fullWidth
            select
            label="Роль"
            variant="outlined"
            margin="normal"
            {...register("role", { required: "Роль обязательна" })}
            error={!!errors.role}
            helperText={errors.role?.message}
          >
            {availableRoles.map((role) => (
              <MenuItem key={role} value={role}>
                {role === ROLES.TEACHER
                  ? "Учитель"
                  : role === ROLES.PARENT
                  ? "Родитель"
                  : "Администратор"}
              </MenuItem>
            ))}
          </TextField>

          <TextField
            fullWidth
            label="Имя"
            variant="outlined"
            margin="normal"
            {...register("firstName", { required: "Имя обязательно" })}
            error={!!errors.firstName}
            helperText={errors.firstName?.message}
          />

          <TextField
            fullWidth
            label="Фамилия"
            variant="outlined"
            margin="normal"
            {...register("lastName", { required: "Фамилия обязательна" })}
            error={!!errors.lastName}
            helperText={errors.lastName?.message}
          />

          <TextField
            fullWidth
            label="Телефон"
            variant="outlined"
            margin="normal"
            {...register("phone", { required: "Телефон обязателен" })}
            error={!!errors.phone}
            helperText={errors.phone?.message}
          />

          {user?.role === "superadmin" && (
            <TextField
              fullWidth
              select
              label="Школа"
              variant="outlined"
              margin="normal"
              {...register("schoolId", { required: "Школа обязательна" })}
              error={!!errors.schoolId}
              helperText={errors.schoolId?.message}
            >
              {schools.map((school) => (
                <MenuItem key={school.id} value={school.id}>
                  {school.name}
                </MenuItem>
              ))}
            </TextField>
          )}

          <Button
            type="submit"
            fullWidth
            variant="contained"
            color="primary"
            disabled={loading}
            sx={{ mt: 2 }}
          >
            {loading ? <CircularProgress size={24} /> : "Создать пользователя"}
          </Button>
        </Box>
      </Box>
    </Container>
  );
};

export default CreateUserPage;
