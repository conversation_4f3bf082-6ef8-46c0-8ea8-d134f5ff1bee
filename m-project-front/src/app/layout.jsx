'use client'

import { AppRouterCacheProvider } from "@mui/material-nextjs/v15-appRouter";
import { <PERSON><PERSON> } from "next/font/google";
import { ThemeProvider } from "@mui/material/styles";
import theme from "../theme";
import { Sidebar } from "@/components/Sidebar";
import Box from "@mui/material/Box";
import CssBaseline from "@mui/material/CssBaseline";
import "./globals.css";
import { Bar } from "@/components/Bar";
import { useEffect } from "react";

const roboto = Roboto({
  weight: ["300", "400", "500", "700"],
  subsets: ["latin"],
  display: "swap",
  variable: "--font-roboto",
});

export default function RootLayout(props) {
  const { children } = props;

  // Синхронизируем токен между localStorage и cookie при загрузке страницы
  useEffect(() => {
    // Проверяем, есть ли токен в localStorage
    if (typeof window !== 'undefined') {
      const accessToken = localStorage.getItem("accessToken");

      if (accessToken) {
        // Проверяем, есть ли токен в cookie
        const cookies = document.cookie.split(';');
        const hasCookie = cookies.some(cookie => cookie.trim().startsWith('accessToken='));

        // Если токена нет в cookie, но он есть в localStorage, устанавливаем его в cookie
        if (!hasCookie) {
          console.log('Синхронизируем токен из localStorage в cookie');
          document.cookie = `accessToken=${accessToken}; path=/; max-age=86400; samesite=strict`;
        }
      }
    }
  }, []);

  return (
    <html lang="en">
      <head>
        <title>Montessori School</title>
        <meta name="description" content="Система управления Montessori School" />
        <link rel="icon" href="/favicon/favicon.ico" sizes="any" />
        <link rel="icon" href="/favicon/favicon.svg" type="image/svg+xml" />
        <link rel="apple-touch-icon" href="/favicon/apple-touch-icon.png" />
        <link rel="icon" type="image/png" sizes="96x96" href="/favicon/favicon-96x96.png" />
        <link rel="icon" type="image/png" sizes="192x192" href="/favicon/web-app-manifest-192x192.png" />
        <link rel="icon" type="image/png" sizes="512x512" href="/favicon/web-app-manifest-512x512.png" />
        <link rel="manifest" href="/favicon/site.webmanifest" />
        <meta name="theme-color" content="#ffffff" />
      </head>
      <body className={roboto.variable}>
        <AppRouterCacheProvider>
          <ThemeProvider theme={theme}>
            <Box sx={{ display: "flex" }}>
              <CssBaseline />
              <Bar />
              <Sidebar />
              <Box
                component="main"
                sx={{
                  flexGrow: 1,
                  bgcolor: "background.default",
                  p: 3,
                  pl: { xs: 10, lg: 32,},
                  pt: 10,
                  pr: '10px'
                }}
                style={{
                  overflowX: "auto",
                }}
              >
                {children}
              </Box>
            </Box>
          </ThemeProvider>
        </AppRouterCacheProvider>
      </body>
    </html>
  );
}
