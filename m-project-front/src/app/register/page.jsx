'use client'

import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { useRouter } from "next/navigation";
import useAuthStore from "@/store/authStore";
import { TextField, Button, Container, Box, Typography, Alert, CircularProgress, Select, MenuItem, FormControl, InputLabel, FormHelperText } from "@mui/material";

export default function RegisterPage() {
    const router = useRouter();
    const { registerUser, isAuthenticated, user } = useAuthStore();
    const {
        register,
        handleSubmit,
        formState: { errors },
    } = useForm();

    const [errorMessage, setErrorMessage] = useState(null);
    const [loading, setLoading] = useState(false);

    useEffect(() => {
        if (isAuthenticated) {
            router.push("/");
        }
    }, [isAuthenticated, router]);

    const onSubmit = async (data) => {
        setErrorMessage(null);
        setLoading(true);

        try {
            await registerUser(data.role, data.email, data.password);
            router.push("/login"); // Redirect after successful registration
        } catch (error) {
            setErrorMessage(error.response?.data?.message || "Ошибка регистрации");
        } finally {
            setLoading(false);
        }
    };

    return (
        <Container maxWidth="xs">
            <Box display="flex" flexDirection="column" alignItems="center" mt={8}>
                <Typography variant="h4" component="h1" gutterBottom>
                    Регистрация
                </Typography>

                {errorMessage && <Alert severity="error" sx={{ width: "100%", mb: 2 }}>{errorMessage}</Alert>}

                <Box component="form" onSubmit={handleSubmit(onSubmit)} width="100%" mt={2}>
                    <FormControl fullWidth margin="normal" error={!!errors.role}>
                        <InputLabel id="role-label">Role</InputLabel>
                        <Select
                            labelId="role-label"
                            label="Role"
                            {...register("role", { required: "Выберите роль" })}
                        >
                            <MenuItem value="admin">Admin</MenuItem>
                            <MenuItem value="teacher">Teacher</MenuItem>
                            <MenuItem value="parent">Parent</MenuItem>
                        </Select>
                        <FormHelperText>{errors.role?.message}</FormHelperText>
                    </FormControl>
                    <TextField
                        fullWidth
                        label="Email"
                        variant="outlined"
                        margin="normal"
                        {...register("email", { required: "Введите email" })}
                        error={!!errors.email}
                        helperText={errors.email?.message}
                        type="email"
                    />
                    <TextField
                        fullWidth
                        label="Пароль"
                        variant="outlined"
                        type="password"
                        margin="normal"
                        {...register("password", { required: "Введите пароль" })}
                        error={!!errors.password}
                        helperText={errors.password?.message}
                    />

                    <Button
                        type="submit"
                        fullWidth
                        variant="contained"
                        color="primary"
                        disabled={loading}
                        sx={{ mt: 2 }}
                    >
                        {loading ? <CircularProgress size={24} /> : "Зарегистрироваться"}
                    </Button>
                </Box>
            </Box>
        </Container>
    );
}