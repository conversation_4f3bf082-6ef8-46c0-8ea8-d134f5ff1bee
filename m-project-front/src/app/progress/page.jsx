import Table from "@/components/Table";
import { faker } from '@faker-js/faker'; // Правильный импорт faker
import './styles.scss'

const generateRandomPeople = (num) => {
  return Array.from({ length: num }, (_, i) => ({
    id: `person${i + 1}`,
    name: faker.person.fullName(), // Генерация случайного имени
    skills: {
      '1': 'Показано',
      '2': 'Запланировано',
      '3': 'Практиковался',
      '4': 'Освоено',
      '5': 'Надо повторить',
    },
  }));
};

// Сгенерированные 10 людей
const randomPeoples = generateRandomPeople(10);

export default function Home() {
  return (
    <div className="page-container">
      <Table people={randomPeoples}/>
    </div>
  );
}
