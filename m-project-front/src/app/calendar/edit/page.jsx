"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { EmbedPDF } from "@simplepdf/react-embed-pdf";
import {
  Box,
  Button,
  Typography,
  Container,
  CircularProgress,
  Alert,
  Paper,
} from "@mui/material";
import { Upload as UploadIcon } from "@mui/icons-material";
import useAuthStore from "@/store/authStore";
import axios from "@/axiosInstance";

const EditCalendarPage = () => {
  const [currentCalendar, setCurrentCalendar] = useState(null);
  const [file, setFile] = useState(null);
  const [preview, setPreview] = useState(null);
  const [loading, setLoading] = useState(true);
  const [errorMessage, setErrorMessage] = useState(null);
  const router = useRouter();
  const user = useAuthStore((state) => state.user);

  useEffect(() => {
    const fetchCalendar = async () => {
      if (!user?.schoolId) return;

      try {
        const response = await axios.get(`calendar/${user.schoolId}`);
        setCurrentCalendar(response.data);
      } catch (error) {
        console.error("Ошибка при загрузке календаря:", error);
        if (error.response?.status !== 404) {
          setErrorMessage(
            error.response?.data?.message || "Ошибка при загрузке календаря"
          );
        }
      } finally {
        setLoading(false);
      }
    };

    fetchCalendar();
  }, [user]);

  const handleFileChange = (e) => {
    const selectedFile = e.target.files[0];
    if (selectedFile && selectedFile.type === "application/pdf") {
      setFile(selectedFile);
      setPreview(URL.createObjectURL(selectedFile));
    } else {
      setFile(null);
      setPreview(null);
      setErrorMessage("Пожалуйста, выберите PDF файл");
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!file) {
      setErrorMessage("Пожалуйста, выберите файл");
      return;
    }

    setLoading(true);
    setErrorMessage(null);

    try {
      const formData = new FormData();
      formData.append("file", file);

      await axios.post(`calendar/upload/${user.schoolId}`, formData, {
        headers: {
          "Content-Type": "multipart/form-data",
        },
      });

      router.push("/");
    } catch (error) {
      console.error("Ошибка:", error.response?.data);
      setErrorMessage(
        error.response?.data?.message || "Ошибка при обновлении календаря"
      );
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <Box p={3} display="flex" justifyContent="center">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Container maxWidth="md">
      <Box display="flex" flexDirection="column" alignItems="center" mt={4}>
        <Typography variant="h4" gutterBottom>
          Обновление календаря событий
        </Typography>

        {errorMessage && (
          <Alert severity="error" sx={{ width: "100%", mb: 2 }}>
            {errorMessage}
          </Alert>
        )}

        <Paper sx={{ p: 3, width: "100%" }}>
          {currentCalendar && (
            <Box mb={4}>
              <Typography variant="h6" gutterBottom>
                Текущий календарь:
              </Typography>
              <Box height="400px">
                <EmbedPDF
                  mode="inline"
                  style={{ width: "100%", height: "100%" }}
                  companyIdentifier="react-viewer"
                  documentURL={currentCalendar.fileUrl}
                />
              </Box>
            </Box>
          )}

          <Box
            component="form"
            onSubmit={handleSubmit}
            display="flex"
            flexDirection="column"
            alignItems="center"
          >
            <Button
              variant="outlined"
              component="label"
              startIcon={<UploadIcon />}
              sx={{ mb: 2 }}
            >
              Выбрать новый PDF файл
              <input
                type="file"
                hidden
                accept="application/pdf"
                onChange={handleFileChange}
              />
            </Button>

            {preview && (
              <Box mb={2} width="100%" height="400px">
                <Typography variant="h6" gutterBottom>
                  Предпросмотр нового календаря:
                </Typography>
                <EmbedPDF
                  mode="inline"
                  style={{ width: "100%", height: "100%" }}
                  companyIdentifier="react-viewer"
                  documentURL={preview}
                />
              </Box>
            )}

            <Button
              type="submit"
              variant="contained"
              color="primary"
              disabled={loading || !file}
              sx={{ mt: 2 }}
            >
              {loading ? <CircularProgress size={24} /> : "Обновить календарь"}
            </Button>
          </Box>
        </Paper>
      </Box>
    </Container>
  );
};

export default EditCalendarPage;
