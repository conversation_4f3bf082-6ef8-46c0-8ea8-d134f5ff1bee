"use client";

import { useState, useEffect, Suspense } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import {
  Box,
  Button,
  TextField,
  Typography,
  Container,
  CircularProgress,
  Alert,
  Paper,
  MenuItem,
  FormControl,
  InputLabel,
  Select,
} from "@mui/material";
import { Upload as UploadIcon } from "@mui/icons-material";
import axios from "@/axiosInstance";
import useAuthStore from "@/store/authStore";
import { useForm, Controller } from "react-hook-form";

const CreateReportPageContent = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const childIdFromUrl = searchParams.get('childId');
  const [children, setChildren] = useState([]);
  const [loading, setLoading] = useState(false);
  const [errorMessage, setErrorMessage] = useState(null);
  const [file, setFile] = useState(null);
  const [preview, setPreview] = useState(null);
  const { user } = useAuthStore();

  const {
    register,
    handleSubmit,
    control,
    formState: { errors },
    setValue,
    watch,
  } = useForm({
    defaultValues: {
      childId: childIdFromUrl || '',
      type: 'custom',
      name: '',
    }
  });

  const selectedChildId = watch('childId');

  useEffect(() => {
    fetchChildren();
  }, []);

  const fetchChildren = async () => {
    try {
      const response = await axios.get("/children");
      setChildren(response.data);
    } catch (error) {
      console.error("Error fetching children:", error);
      setErrorMessage("Ошибка загрузки списка детей.");
    }
  };

  const handleFileChange = (event) => {
    const selectedFile = event.target.files[0];
    if (selectedFile && selectedFile.type === "application/pdf") {
      setFile(selectedFile);
      setPreview(URL.createObjectURL(selectedFile));
      setErrorMessage(null);
    } else {
      setErrorMessage("Пожалуйста, выберите PDF файл");
      setFile(null);
      setPreview(null);
    }
  };

  const onSubmit = async (data) => {
    if (!file) {
      setErrorMessage("Пожалуйста, выберите файл");
      return;
    }

    setLoading(true);
    setErrorMessage(null);

    try {
      const formData = new FormData();
      formData.append("file", file);
      formData.append("childId", data.childId);
      formData.append("type", data.type);
      formData.append("name", data.name || "");
      formData.append("schoolId", user.schoolId);
      formData.append("authorId", user.id); // Добавляем ID автора (текущего пользователя)

      // Используем правильный эндпоинт для загрузки отчета
      await axios.post("/reports", formData, {
        headers: {
          "Content-Type": "multipart/form-data",
        },
      });

      router.push("/reports");
    } catch (error) {
      console.error("Ошибка:", error.response?.data);
      setErrorMessage(
        error.response?.data?.message || "Ошибка при загрузке отчета"
      );
    } finally {
      setLoading(false);
    }
  };

  return (
    <Container maxWidth="md">
      <Box display="flex" flexDirection="column" alignItems="center" mt={4}>
        <Typography variant="h4" gutterBottom>
          Добавление отчета
        </Typography>

        {errorMessage && (
          <Alert severity="error" sx={{ width: "100%", mb: 2 }}>
            {errorMessage}
          </Alert>
        )}

        <Paper sx={{ p: 3, width: "100%" }}>
          <Box
            component="form"
            onSubmit={handleSubmit(onSubmit)}
            display="flex"
            flexDirection="column"
          >
            <Controller
              name="childId"
              control={control}
              rules={{ required: "Выберите ребенка" }}
              render={({ field }) => (
                <TextField
                  {...field}
                  fullWidth
                  select
                  label="Ребенок"
                  variant="outlined"
                  margin="normal"
                  error={!!errors.childId}
                  helperText={errors.childId?.message}
                >
                  {children.map((child) => (
                    <MenuItem key={child.id} value={child.id}>
                      {child.name}
                    </MenuItem>
                  ))}
                </TextField>
              )}
            />

            <Controller
              name="type"
              control={control}
              rules={{ required: "Выберите тип отчета" }}
              render={({ field }) => (
                <TextField
                  {...field}
                  fullWidth
                  select
                  label="Тип отчета"
                  variant="outlined"
                  margin="normal"
                  error={!!errors.type}
                  helperText={errors.type?.message}
                >
                  <MenuItem value="progress">Прогресс</MenuItem>
                  <MenuItem value="attendance">Посещаемость</MenuItem>
                  <MenuItem value="custom">Индивидуальный отчет</MenuItem>
                </TextField>
              )}
            />

            <TextField
              fullWidth
              label="Название отчета"
              variant="outlined"
              margin="normal"
              {...register("name", { required: "Название отчета обязательно" })}
              error={!!errors.name}
              helperText={errors.name?.message}
            />

            <Box mt={2} display="flex" flexDirection="column" alignItems="center">
              <Button
                variant="outlined"
                component="label"
                startIcon={<UploadIcon />}
                sx={{ mb: 2 }}
              >
                Выбрать PDF файл
                <input
                  type="file"
                  hidden
                  accept="application/pdf"
                  onChange={handleFileChange}
                />
              </Button>

              {file && (
                <Typography variant="body2" sx={{ mb: 1 }}>
                  Выбран файл: {file.name}
                </Typography>
              )}

              {preview && (
                <Box mb={2} width="100%" height="500px">
                  <iframe
                    src={preview}
                    width="100%"
                    height="100%"
                    style={{ border: "none" }}
                  />
                </Box>
              )}
            </Box>

            <Button
              type="submit"
              variant="contained"
              color="primary"
              disabled={loading || !file}
              sx={{ mt: 2 }}
            >
              {loading ? <CircularProgress size={24} /> : "Загрузить отчет"}
            </Button>
          </Box>
        </Paper>
      </Box>
    </Container>
  );
};

const CreateReportPage = () => {
  return (
    <Suspense fallback={<CircularProgress />}>
      <CreateReportPageContent />
    </Suspense>
  );
};

export default CreateReportPage;
