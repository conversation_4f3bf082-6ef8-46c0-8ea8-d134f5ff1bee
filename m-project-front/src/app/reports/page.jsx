"use client";

import { useState, useEffect } from "react";
import axios from "@/axiosInstance";
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Button,
  Typography,
  Box,
  TextField,
  MenuItem,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  IconButton,
  Tooltip,
} from "@mui/material";
import {
  Delete as DeleteIcon,
  Edit as EditIcon,
  Visibility as VisibilityIcon,
  Download as DownloadIcon,
} from "@mui/icons-material";
import { useRouter } from "next/navigation";
import useAuthStore from "@/store/authStore";
import ListPageHeader from "@/components/ListPageHeader";

const ReportsPage = () => {
  const router = useRouter();
  const { user } = useAuthStore();
  const [reports, setReports] = useState([]);
  const [children, setChildren] = useState([]);
  const [selectedChild, setSelectedChild] = useState("");
  const [loading, setLoading] = useState(false);
  const [errorMessage, setErrorMessage] = useState(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [selectedReport, setSelectedReport] = useState(null);
  const [previewDialogOpen, setPreviewDialogOpen] = useState(false);

  useEffect(() => {
    fetchChildren();
  }, []);

  useEffect(() => {
    if (selectedChild) {
      fetchReports(selectedChild);
    }
  }, [selectedChild]);

  const fetchChildren = async () => {
    try {
      const response = await axios.get("/children");
      setChildren(response.data);
    } catch (error) {
      console.error("Error fetching children:", error);
    }
  };

  const fetchReports = async (childId) => {
    setLoading(true);
    try {
      const response = await axios.get(`/reports/child/${childId}`);
      setReports(response.data);
      setErrorMessage(null);
    } catch (error) {
      setErrorMessage("Ошибка загрузки отчетов.");
      console.error("Error fetching reports:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleAddReport = () => {
    const path = selectedChild
      ? `/reports/create?childId=${selectedChild}`
      : "/reports/create";
    router.push(path);
  };

  const handleEditReport = (id) => {
    router.push(`/reports/${id}/edit`);
  };

  const handleDeleteClick = (report) => {
    setSelectedReport(report);
    setDeleteDialogOpen(true);
  };

  const handleDeleteConfirm = async () => {
    if (!selectedReport) return;

    setLoading(true);
    try {
      await axios.delete(`/reports/${selectedReport.id}`);
      setDeleteDialogOpen(false);
      setSelectedReport(null);
      fetchReports(selectedChild);
    } catch (error) {
      setErrorMessage("Ошибка при удалении отчета.");
      console.error("Error deleting report:", error);
    } finally {
      setLoading(false);
    }
  };

  const handlePreviewClick = (report) => {
    setSelectedReport(report);
    setPreviewDialogOpen(true);
  };

  const handleDownload = (report) => {
    // Создаем ссылку для скачивания файла
    const link = document.createElement("a");
    // Формируем полный URL для файла
    link.href = `${report.fileUrl}`;
    // Формируем имя файла для скачивания
    const reportName = report.name || getReportTypeName(report.type);
    link.download = `Отчет_${reportName}_${new Date(
      report.createdAt
    ).toLocaleDateString()}.pdf`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString("ru-RU");
  };

  const getReportTypeName = (type) => {
    const types = {
      progress: "Прогресс",
      attendance: "Посещаемость",
      custom: "Индивидуальный отчет",
    };
    return types[type] || type;
  };

  if (loading) {
    return <Box p={3}>Загрузка...</Box>;
  }

  return (
    <Box>
      <ListPageHeader
        title="Отчеты"
        buttonText="Добавить отчет"
        onButtonClick={handleAddReport}
      />

      <TextField
        select
        fullWidth
        label="Выберите ребенка"
        value={selectedChild}
        onChange={(e) => setSelectedChild(e.target.value)}
        margin="normal"
        sx={{ mb: 3 }}
      >
        {children.map((child) => (
          <MenuItem key={child.id} value={child.id}>
            {child.name}
          </MenuItem>
        ))}
      </TextField>

      {errorMessage && (
        <Box color="error.main" mb={2}>
          {errorMessage}
        </Box>
      )}

      {selectedChild && (
        <TableContainer component={Paper}>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Тип отчета</TableCell>
                <TableCell>Название</TableCell>
                <TableCell>Дата создания</TableCell>
                <TableCell>Действия</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {reports.length > 0 ? (
                reports.map((report) => (
                  <TableRow key={report.id}>
                    <TableCell>{getReportTypeName(report.type)}</TableCell>
                    <TableCell>{report.name || "Без названия"}</TableCell>
                    <TableCell>{formatDate(report.createdAt)}</TableCell>
                    <TableCell>
                      <Tooltip title="Просмотр">
                        <IconButton
                          size="small"
                          onClick={() => handlePreviewClick(report)}
                          sx={{ mr: 1 }}
                        >
                          <VisibilityIcon />
                        </IconButton>
                      </Tooltip>
                      <Tooltip title="Скачать">
                        <IconButton
                          size="small"
                          onClick={() => handleDownload(report)}
                          sx={{ mr: 1 }}
                        >
                          <DownloadIcon />
                        </IconButton>
                      </Tooltip>
                      <Tooltip title="Редактировать">
                        <IconButton
                          size="small"
                          onClick={() => handleEditReport(report.id)}
                          sx={{ mr: 1 }}
                        >
                          <EditIcon />
                        </IconButton>
                      </Tooltip>
                      <Tooltip title="Удалить">
                        <IconButton
                          size="small"
                          color="error"
                          onClick={() => handleDeleteClick(report)}
                        >
                          <DeleteIcon />
                        </IconButton>
                      </Tooltip>
                    </TableCell>
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={4} align="center">
                    Отчеты не найдены
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </TableContainer>
      )}

      {/* Диалог подтверждения удаления */}
      <Dialog
        open={deleteDialogOpen}
        onClose={() => setDeleteDialogOpen(false)}
      >
        <DialogTitle>Подтверждение удаления</DialogTitle>
        <DialogContent>
          Вы уверены, что хотите удалить отчет{" "}
          {selectedReport ? `"${selectedReport.name || "Без названия"}"` : ""}?
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialogOpen(false)}>Отмена</Button>
          <Button onClick={handleDeleteConfirm} color="error" autoFocus>
            Удалить
          </Button>
        </DialogActions>
      </Dialog>

      {/* Диалог предпросмотра PDF */}
      <Dialog
        open={previewDialogOpen}
        onClose={() => setPreviewDialogOpen(false)}
        maxWidth="lg"
        fullWidth
      >
        <DialogTitle>
          {selectedReport
            ? `${getReportTypeName(selectedReport.type)} - ${
                selectedReport.name || "Без названия"
              }`
            : "Просмотр отчета"}
        </DialogTitle>
        <DialogContent sx={{ height: "80vh", p: 0 }}>
          {selectedReport && (
            <iframe
              src={`${selectedReport.fileUrl}`}
              width="100%"
              height="100%"
              style={{ border: "none" }}
            />
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setPreviewDialogOpen(false)}>Закрыть</Button>
          {selectedReport && (
            <Button
              onClick={() => handleDownload(selectedReport)}
              color="primary"
            >
              Скачать
            </Button>
          )}
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default ReportsPage;
