"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import {
  Box,
  Button,
  TextField,
  Typography,
  Container,
  CircularProgress,
  Alert,
  MenuItem,
} from "@mui/material";
import { useForm, Controller } from "react-hook-form";
import axios from "@/axiosInstance";
import { use } from "react";

const EditAttendancePage = ({ params }) => {
  const router = useRouter();
  const unwrappedParams = use(params);
  const { id } = unwrappedParams;
  const [loading, setLoading] = useState(false);
  const [errorMessage, setErrorMessage] = useState(null);

  const {
    register,
    handleSubmit,
    control,
    formState: { errors },
    setValue,
  } = useForm({
    defaultValues: {
      status: "",
      checkInTime: "",
      checkOutTime: ""
    }
  });

  useEffect(() => {
    fetchAttendance();
  }, [id]);

  const fetchAttendance = async () => {
    try {
      const response = await axios.get(`/attendance/${id}`);
      const data = response.data;

      // Форматируем время для полей ввода
      const checkInTime = data.checkInTime
        ? new Date(data.checkInTime).toLocaleTimeString("ru-RU", {
            hour: "2-digit",
            minute: "2-digit",
          })
        : "";
      const checkOutTime = data.checkOutTime
        ? new Date(data.checkOutTime).toLocaleTimeString("ru-RU", {
            hour: "2-digit",
            minute: "2-digit",
          })
        : "";

      setValue("status", data.status || "");
      setValue("checkInTime", checkInTime);
      setValue("checkOutTime", checkOutTime);
    } catch (error) {
      console.error("Error fetching attendance:", error);
      setErrorMessage("Ошибка загрузки данных.");
    }
  };

  const onSubmit = async (data) => {
    setLoading(true);
    setErrorMessage(null);

    try {
      // Получаем текущую запись для использования её даты
      const currentAttendance = await axios.get(`/attendance/${id}`);
      const currentDate = new Date(currentAttendance.data.date);

      // Создаем объекты Date для времени прихода и ухода
      let checkInTime = null;
      let checkOutTime = null;

      if (data.checkInTime) {
        const [hours, minutes] = data.checkInTime.split(':');
        const checkIn = new Date(currentDate);
        checkIn.setHours(parseInt(hours, 10), parseInt(minutes, 10), 0);
        checkInTime = checkIn.toISOString();
      }

      if (data.checkOutTime) {
        const [hours, minutes] = data.checkOutTime.split(':');
        const checkOut = new Date(currentDate);
        checkOut.setHours(parseInt(hours, 10), parseInt(minutes, 10), 0);
        checkOutTime = checkOut.toISOString();
      }

      const formData = {
        status: data.status,
        checkInTime,
        checkOutTime,
      };

      await axios.patch(`/attendance/${id}`, formData);
      router.push("/attendance");
    } catch (error) {
      console.error("Ошибка:", error.response?.data);
      setErrorMessage(
        error.response?.data?.message || "Ошибка при обновлении записи"
      );
    } finally {
      setLoading(false);
    }
  };

  return (
    <Container maxWidth="sm">
      <Box display="flex" flexDirection="column" alignItems="center" mt={4}>
        <Typography variant="h4" gutterBottom>
          Редактирование посещаемости
        </Typography>

        {errorMessage && (
          <Alert severity="error" sx={{ width: "100%", mb: 2 }}>
            {errorMessage}
          </Alert>
        )}

        <Box
          component="form"
          onSubmit={handleSubmit(onSubmit)}
          width="100%"
          mt={2}
        >
          <Controller
            name="status"
            control={control}
            rules={{ required: "Статус обязателен" }}
            render={({ field }) => (
              <TextField
                {...field}
                fullWidth
                select
                label="Статус"
                variant="outlined"
                margin="normal"
                error={!!errors.status}
                helperText={errors.status?.message}
              >
                <MenuItem value="present">Присутствует</MenuItem>
                <MenuItem value="absent">Отсутствует</MenuItem>
                <MenuItem value="late">Опоздание</MenuItem>
              </TextField>
            )}
          />

          <TextField
            fullWidth
            label="Время прихода"
            type="time"
            variant="outlined"
            margin="normal"
            InputLabelProps={{
              shrink: true,
            }}
            {...register("checkInTime")}
          />

          <TextField
            fullWidth
            label="Время ухода"
            type="time"
            variant="outlined"
            margin="normal"
            InputLabelProps={{
              shrink: true,
            }}
            {...register("checkOutTime")}
          />

          <Button
            type="submit"
            fullWidth
            variant="contained"
            color="primary"
            disabled={loading}
            sx={{ mt: 2 }}
          >
            {loading ? <CircularProgress size={24} /> : "Сохранить изменения"}
          </Button>
        </Box>
      </Box>
    </Container>
  );
};

export default EditAttendancePage;