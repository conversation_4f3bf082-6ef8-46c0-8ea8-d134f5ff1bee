"use client";

import { useState, useEffect, Suspense } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import {
  Box,
  Button,
  TextField,
  Typography,
  Container,
  CircularProgress,
  Alert,
  MenuItem,
} from "@mui/material";
import { useForm } from "react-hook-form";
import axios from "@/axiosInstance";
import useAuthStore from "@/store/authStore";
import { Controller } from "react-hook-form";

const CreateAttendancePageContent = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const childIdFromUrl = searchParams.get('childId');
  const [children, setChildren] = useState([]);
  const [loading, setLoading] = useState(false);
  const [errorMessage, setErrorMessage] = useState(null);
  const [selectedStatus, setSelectedStatus] = useState('');
  const { user } = useAuthStore(); 

  const {
    register,
    handleSubmit,
    control,
    formState: { errors },
  } = useForm({
    defaultValues: {
      date: new Date().toISOString().split('T')[0],
      status: 'present',
      childId: childIdFromUrl || '',
    }
  });

  useEffect(() => {
    fetchChildren();
  }, []);

  const fetchChildren = async () => {
    try {
      const response = await axios.get("/children");
      setChildren(response.data);
    } catch (error) {
      console.error("Error fetching children:", error);
      setErrorMessage("Ошибка загрузки списка детей.");
    }
  };

  const onSubmit = async (data) => {
    setLoading(true);
    setErrorMessage(null);

    try {
      // Преобразуем дату в формат ISO
      const date = new Date(data.date);
      date.setHours(12);

      // Создаем объекты Date для времени прихода и ухода
      let checkInTime = null;
      let checkOutTime = null;

      if (data.checkInTime) {
        const [hours, minutes] = data.checkInTime.split(':');
        const checkIn = new Date(data.date);
        checkIn.setHours(parseInt(hours, 10), parseInt(minutes, 10), 0);
        checkInTime = checkIn.toISOString();
      }

      if (data.checkOutTime) {
        const [hours, minutes] = data.checkOutTime.split(':');
        const checkOut = new Date(data.date);
        checkOut.setHours(parseInt(hours, 10), parseInt(minutes, 10), 0);
        checkOutTime = checkOut.toISOString();
      }

      const formData = {
        childId: data.childId,
        date: date.toISOString(),
        status: data.status,
        schoolId: user.schoolId,
        checkInTime,
        checkOutTime,
      };

      console.log("Отправляемые данные:", formData);

      await axios.post("/attendance", formData);
      router.push("/attendance");
    } catch (error) {
      console.error("Ошибка:", error.response?.data);
      setErrorMessage(
        error.response?.data?.message || "Ошибка при создании записи"
      );
    } finally {
      setLoading(false);
    }
  };

  return (
    <Container maxWidth="sm">
      <Box display="flex" flexDirection="column" alignItems="center" mt={4}>
        <Typography variant="h4" gutterBottom>
          Добавление посещаемости
        </Typography>

        {errorMessage && (
          <Alert severity="error" sx={{ width: "100%", mb: 2 }}>
            {errorMessage}
          </Alert>
        )}

        <Box
          component="form"
          onSubmit={handleSubmit(onSubmit)}
          width="100%"
          mt={2}
        >
          <Controller
            name="childId"
            control={control}
            rules={{ required: "Выберите ребенка" }}
            defaultValue=""
            render={({ field }) => (
              <TextField
                {...field}
                fullWidth
                select
                label="Ребенок"
                variant="outlined"
                margin="normal"
                error={!!errors.childId}
                helperText={errors.childId?.message}
              >
                {children.map((child) => (
                  <MenuItem key={child.id} value={child.id}>
                    {child.name}
                  </MenuItem>
                ))}
              </TextField>
            )}
          />

          <TextField
            fullWidth
            label="Дата"
            type="date"
            variant="outlined"
            margin="normal"
            InputLabelProps={{
              shrink: true,
            }}
            {...register("date", { required: "Дата обязательна" })}
            error={!!errors.date}
            helperText={errors.date?.message}
          />

          <Controller
            name="status"
            control={control}
            rules={{ required: "Статус обязателен" }}
            defaultValue=""
            render={({ field }) => (
              <TextField
                {...field}
                fullWidth
                select
                label="Статус"
                variant="outlined"
                margin="normal"
                error={!!errors.status}
                helperText={errors.status?.message}
              >
                <MenuItem value="present">Присутствует</MenuItem>
                <MenuItem value="absent">Отсутствует</MenuItem>
                <MenuItem value="late">Опоздание</MenuItem>
              </TextField>
            )}
          />

          <TextField
            fullWidth
            label="Время прихода"
            type="time"
            variant="outlined"
            margin="normal"
            InputLabelProps={{
              shrink: true,
            }}
            {...register("checkInTime")}
          />

          <TextField
            fullWidth
            label="Время ухода"
            type="time"
            variant="outlined"
            margin="normal"
            InputLabelProps={{
              shrink: true,
            }}
            {...register("checkOutTime")}
          />

          <Button
            type="submit"
            fullWidth
            variant="contained"
            color="primary"
            disabled={loading}
            sx={{ mt: 2 }}
          >
            {loading ? <CircularProgress size={24} /> : "Добавить запись"}
          </Button>
        </Box>
      </Box>
    </Container>
  );
};

const CreateAttendancePage = () => {
  return (
    <Suspense fallback={<CircularProgress />}>
      <CreateAttendancePageContent />
    </Suspense>
  );
};

export default CreateAttendancePage; 