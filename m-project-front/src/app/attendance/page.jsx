"use client";

import { useState, useEffect } from "react";
import axios from "@/axiosInstance";
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Button,
  Typography,
  Box,
  TextField,
  MenuItem,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
} from "@mui/material";
import { Add as AddIcon } from "@mui/icons-material";
import { useRouter } from "next/navigation";
import useAuthStore from "@/store/authStore";
import ListPageHeader from '@/components/ListPageHeader';

const AttendancePage = () => {
  const router = useRouter();
  const { user } = useAuthStore();
  const [attendance, setAttendance] = useState([]);
  const [children, setChildren] = useState([]);
  const [selectedChild, setSelectedChild] = useState("");
  const [loading, setLoading] = useState(false);
  const [errorMessage, setErrorMessage] = useState(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [selectedRecord, setSelectedRecord] = useState(null);

  useEffect(() => {
    fetchChildren();
  }, []);

  useEffect(() => {
    if (selectedChild) {
      fetchAttendance(selectedChild);
    }
  }, [selectedChild]);

  const fetchChildren = async () => {
    try {
      const response = await axios.get("/children");
      setChildren(response.data);
    } catch (error) {
      console.error("Error fetching children:", error);
    }
  };

  const fetchAttendance = async (childId) => {
    setLoading(true);
    try {
      const response = await axios.get(`/attendance?childId=${childId}`);
      setAttendance(response.data);
      setErrorMessage(null);
    } catch (error) {
      setErrorMessage("Ошибка загрузки посещаемости.");
      console.error("Error fetching attendance:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleAddAttendance = () => {
    const path = selectedChild 
      ? `/attendance/create?childId=${selectedChild}`
      : '/attendance/create';
    router.push(path);
  };

  const handleEditAttendance = (id) => {
    router.push(`/attendance/${id}/edit`);
  };

  const handleDeleteClick = (record) => {
    setSelectedRecord(record);
    setDeleteDialogOpen(true);
  };

  const handleDeleteConfirm = async () => {
    if (!selectedRecord) return;

    setLoading(true);
    try {
      await axios.delete(`/attendance/${selectedRecord.id}`);
      setDeleteDialogOpen(false);
      setSelectedRecord(null);
      fetchAttendance(selectedChild);
    } catch (error) {
      setErrorMessage("Ошибка при удалении записи.");
      console.error("Error deleting attendance:", error);
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString("ru-RU");
  };

  const formatTime = (timeString) => {
    if (!timeString) return "-";
    return new Date(timeString).toLocaleTimeString("ru-RU", {
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  const getStatusName = (status) => {
    const statuses = {
      present: "Присутствует",
      absent: "Отсутствует",
      late: "Опоздание",
    };
    return statuses[status] || status;
  };

  if (loading) {
    return <Box p={3}>Загрузка...</Box>;
  }

  return (
    <Box>
      <ListPageHeader
        title="Посещаемость"
        buttonText="Добавить запись"
        onButtonClick={handleAddAttendance}
      />

      <TextField
        select
        fullWidth
        label="Выберите ребенка"
        value={selectedChild}
        onChange={(e) => setSelectedChild(e.target.value)}
        margin="normal"
        sx={{ mb: 3 }}
      >
        {children.map((child) => (
          <MenuItem key={child.id} value={child.id}>
            {child.name}
          </MenuItem>
        ))}
      </TextField>

      {errorMessage && (
        <Box color="error.main" mb={2}>
          {errorMessage}
        </Box>
      )}

      {selectedChild && (
        <TableContainer component={Paper}>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Дата</TableCell>
                <TableCell>Статус</TableCell>
                <TableCell>Время прихода</TableCell>
                <TableCell>Время ухода</TableCell>
                <TableCell>Действия</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {attendance.map((record) => (
                <TableRow key={record.id}>
                  <TableCell>{formatDate(record.date)}</TableCell>
                  <TableCell>{getStatusName(record.status)}</TableCell>
                  <TableCell>{formatTime(record.checkInTime)}</TableCell>
                  <TableCell>{formatTime(record.checkOutTime)}</TableCell>
                  <TableCell>
                    <Button
                      size="small"
                      onClick={() => handleEditAttendance(record.id)}
                      sx={{ mr: 1 }}
                    >
                      Редактировать
                    </Button>
                    <Button
                      size="small"
                      color="error"
                      onClick={() => handleDeleteClick(record)}
                    >
                      Удалить
                    </Button>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      )}

      <Dialog
        open={deleteDialogOpen}
        onClose={() => setDeleteDialogOpen(false)}
      >
        <DialogTitle>Подтверждение удаления</DialogTitle>
        <DialogContent>
          Вы уверены, что хотите удалить запись посещаемости от {selectedRecord ? formatDate(selectedRecord.date) : ''}?
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialogOpen(false)}>Отмена</Button>
          <Button onClick={handleDeleteConfirm} color="error" autoFocus>
            Удалить
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default AttendancePage; 