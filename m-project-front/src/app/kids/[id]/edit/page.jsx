"use client";

import { useState, useEffect, use } from "react";
import { useRouter } from "next/navigation";
import {
  Box,
  Button,
  TextField,
  Typography,
  Container,
  CircularProgress,
  Alert,
  MenuItem,
  Checkbox,
  ListItemText,
  Divider,
  Paper,
  Chip,
} from "@mui/material";
import { Add as AddIcon, Delete as DeleteIcon, Refresh as RefreshIcon } from "@mui/icons-material";
import { useForm } from "react-hook-form";
import axios from "@/axiosInstance";
import useAuthStore from "@/store/authStore";
import { Controller } from "react-hook-form";

const EditKidPage = ({ params }) => {
  const router = useRouter();
  const resolvedParams = use(params);
  const { id } = resolvedParams;
  const [groups, setGroups] = useState([]);
  const [parents, setParents] = useState([]);
  const [loading, setLoading] = useState(false);
  const [errorMessage, setErrorMessage] = useState(null);
  const [selectedParents, setSelectedParents] = useState([]);
  const [accessCode, setAccessCode] = useState('');
  const [allowedTelegramIds, setAllowedTelegramIds] = useState([]);
  const [telegramUsers, setTelegramUsers] = useState({});
  const [codeGenerating, setCodeGenerating] = useState(false);

  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    control,
  } = useForm({
    defaultValues: {
      groupId: '',
    }
  });

  async function getData() {
    try {
      setLoading(true);
      // Сначала загружаем группы и родителей
      await Promise.all([fetchGroups(), fetchParents()]);
      // Затем загружаем данные ребенка
      await fetchKid();
    } catch (error) {
      console.error("Error loading data:", error);
      setErrorMessage("Ошибка загрузки данных");
    } finally {
      setLoading(false);
    }
  }

  useEffect(() => {
    getData();
  }, [id]);

  const fetchGroups = async () => {
    try {
      const response = await axios.get("/groups");
      setGroups(response.data);
    } catch (error) {
      console.error("Error fetching groups:", error);
      setErrorMessage("Ошибка загрузки списка групп.");
    }
  };

  const fetchParents = async () => {
    try {
      const response = await axios.get("/users?role=PARENT");
      setParents(response.data);
    } catch (error) {
      console.error("Error fetching parents:", error);
      setErrorMessage("Ошибка загрузки списка родителей.");
    }
  };

  const fetchKid = async () => {
    try {
      const response = await axios.get(`/children/${id}`);
      const kid = response.data;

      setValue("name", kid.name);
      setValue("groupId", kid.groupId); // Теперь можно установить значение напрямую
      setValue(
        "dateOfBirth",
        new Date(kid.dateOfBirth).toISOString().split("T")[0]
      );
      setSelectedParents(kid.parentIds || []);
      setAccessCode(kid.accessCode || '');
      setAllowedTelegramIds(kid.allowedTelegramIds || []);
    } catch (error) {
      console.error("Error fetching kid:", error);
      setErrorMessage("Ошибка загрузки данных ребенка.");
    }
  };

  const fetchAllowedTelegramIds = async () => {
    try {
      const response = await axios.get(`/children/${id}/allowed-telegram-ids`);
      const telegramIds = response.data.telegramIds || [];
      setAllowedTelegramIds(telegramIds);

      // Получаем информацию о пользователях Telegram
      if (telegramIds.length > 0) {
        fetchTelegramUsers(telegramIds);
      }
    } catch (error) {
      console.error("Error fetching allowed Telegram IDs:", error);
    }
  };

  // Функция для получения информации о пользователях Telegram
  const fetchTelegramUsers = async (telegramIds) => {
    try {
      // В реальном проекте здесь должен быть запрос к API
      // Пример: const response = await axios.get(`/telegram-users?ids=${telegramIds.join(',')}`);

      // Имитация получения данных о пользователях
      const mockUsers = {};
      telegramIds.forEach(id => {
        // Генерируем имя пользователя на основе ID
        // В реальном проекте здесь должны быть реальные данные
        mockUsers[id] = {
          firstName: `Пользователь ${id.substring(0, 4)}`,
          lastName: `Telegram`
        };
      });

      setTelegramUsers(mockUsers);
    } catch (error) {
      console.error("Error fetching Telegram users:", error);
    }
  };

  const generateAccessCode = async () => {
    setCodeGenerating(true);
    try {
      const response = await axios.post(`/children/${id}/generate-access-code`);
      setAccessCode(response.data.accessCode);
    } catch (error) {
      console.error("Error generating access code:", error);
      setErrorMessage("Ошибка при генерации кода доступа.");
    } finally {
      setCodeGenerating(false);
    }
  };

  const resetAccessCode = async () => {
    setCodeGenerating(true);
    try {
      const response = await axios.post(`/children/${id}/reset-access-code`);
      setAccessCode(response.data.accessCode);
    } catch (error) {
      console.error("Error resetting access code:", error);
      setErrorMessage("Ошибка при сбросе кода доступа.");
    } finally {
      setCodeGenerating(false);
    }
  };

  const removeAccessCode = async () => {
    setCodeGenerating(true);
    try {
      await axios.delete(`/children/${id}/access-code`);
      setAccessCode('');
    } catch (error) {
      console.error("Error removing access code:", error);
      setErrorMessage("Ошибка при удалении кода доступа.");
    } finally {
      setCodeGenerating(false);
    }
  };

  const addAllowedTelegramId = async (telegramId) => {
    if (!telegramId) return;
    try {
      await axios.post(`/children/${id}/allowed-telegram-ids`, { telegramId });
      fetchAllowedTelegramIds();
    } catch (error) {
      console.error("Error adding Telegram ID:", error);
      setErrorMessage("Ошибка при добавлении Telegram ID.");
    }
  };

  const removeAllowedTelegramId = async (telegramId) => {
    try {
      await axios.delete(`/children/${id}/allowed-telegram-ids/${telegramId}`);
      fetchAllowedTelegramIds();
    } catch (error) {
      console.error("Error removing Telegram ID:", error);
      setErrorMessage("Ошибка при удалении Telegram ID.");
    }
  };

  const handleParentChange = (event) => {
    setSelectedParents(event.target.value);
  };

  const onSubmit = async (data) => {
    setLoading(true);
    setErrorMessage(null);

    try {
      // Преобразуем дату в формат ISO с временем
      const formattedDate = new Date(data.dateOfBirth);
      formattedDate.setHours(12);

      const updateData = {
        ...data,
        dateOfBirth: formattedDate.toISOString(),
        parentIds: selectedParents,
      };

      await axios.patch(`/children/${id}`, updateData);
      router.push("/kids");
    } catch (error) {
      console.error("Ошибка:", error.response?.data);
      setErrorMessage(
        error.response?.data?.message || "Ошибка при обновлении данных"
      );
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return <Box p={3}>Загрузка...</Box>;
  }

  return (
    <Container maxWidth="sm">
      <Box display="flex" flexDirection="column" alignItems="center" mt={4}>
        <Typography variant="h4" gutterBottom>
          Редактирование данных ребенка
        </Typography>

        {errorMessage && (
          <Alert severity="error" sx={{ width: "100%", mb: 2 }}>
            {errorMessage}
          </Alert>
        )}

        <Box
          component="form"
          onSubmit={handleSubmit(onSubmit)}
          width="100%"
          mt={2}
        >
          <TextField
            fullWidth
            label="Имя"
            variant="outlined"
            margin="normal"
            {...register("name", { required: "Имя обязательно" })}
            error={!!errors.name}
            helperText={errors.name?.message}
          />

          <TextField
            fullWidth
            label="Дата рождения"
            type="date"
            variant="outlined"
            margin="normal"
            InputLabelProps={{
              shrink: true,
            }}
            {...register("dateOfBirth", {
              required: "Дата рождения обязательна",
            })}
            error={!!errors.dateOfBirth}
            helperText={errors.dateOfBirth?.message}
          />

          <Controller
            name="groupId"
            control={control}
            rules={{ required: "Группа обязательна" }}
            render={({ field }) => (
              <TextField
                {...field}
                fullWidth
                select
                label="Группа"
                variant="outlined"
                margin="normal"
                error={!!errors.groupId}
                helperText={errors.groupId?.message}
              >
                {groups.map((group) => (
                  <MenuItem key={group.id} value={group.id}>
                    {group.name}
                  </MenuItem>
                ))}
              </TextField>
            )}
          />

          <TextField
            fullWidth
            select
            label="Родители"
            variant="outlined"
            margin="normal"
            value={selectedParents}
            onChange={handleParentChange}
            SelectProps={{
              multiple: true,
              renderValue: (selected) => {
                const selectedParentNames = parents
                  .filter((parent) => selected.includes(parent.id))
                  .map((parent) => `${parent.firstName} ${parent.lastName}`);
                return selectedParentNames.join(", ");
              },
            }}
          >
            {parents.map((parent) => (
              <MenuItem key={parent.id} value={parent.id}>
                <Checkbox checked={selectedParents.includes(parent.id)} />
                <ListItemText
                  primary={`${parent.firstName} ${parent.lastName}`}
                />
              </MenuItem>
            ))}
          </TextField>

          <Divider sx={{ my: 3 }} />

          <Typography variant="h6" gutterBottom>
            Telegram авторизация
          </Typography>

          <Paper sx={{ p: 2, mb: 3 }}>
            <Typography variant="subtitle1" gutterBottom>
              Код доступа
            </Typography>

            <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
              <TextField
                fullWidth
                label="Код доступа"
                variant="outlined"
                value={accessCode}
                onChange={(e) => setAccessCode(e.target.value)}
                sx={{ mr: 1 }}
              />
              <Button
                variant="contained"
                color="primary"
                onClick={generateAccessCode}
                disabled={codeGenerating}
                sx={{ mr: 1, minWidth: '100px' }}
              >
                Создать
              </Button>
              <Button
                variant="outlined"
                color="primary"
                onClick={resetAccessCode}
                disabled={codeGenerating || !accessCode}
                sx={{ mr: 1, minWidth: '100px' }}
              >
                Сбросить
              </Button>
              <Button
                variant="outlined"
                color="error"
                onClick={removeAccessCode}
                disabled={codeGenerating || !accessCode}
                sx={{ minWidth: '100px' }}
              >
                Удалить
              </Button>
            </Box>

            <Typography variant="subtitle1" gutterBottom sx={{ mt: 3 }}>
              Разрешенные Telegram ID
            </Typography>

            <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mb: 2 }}>
              {allowedTelegramIds.map((telegramId) => {
                const user = telegramUsers[telegramId];
                const userName = user ? `${user.firstName} ${user.lastName}` : '';
                return (
                  <Chip
                    key={telegramId}
                    label={userName ? `${telegramId} (${userName})` : telegramId}
                    onDelete={() => removeAllowedTelegramId(telegramId)}
                    color="primary"
                    variant="outlined"
                  />
                );
              })}
              {allowedTelegramIds.length === 0 && (
                <Typography variant="body2" color="text.secondary">
                  Нет разрешенных Telegram ID. Любой пользователь сможет использовать код доступа.
                </Typography>
              )}
            </Box>

            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <TextField
                fullWidth
                label="Добавить Telegram ID"
                variant="outlined"
                placeholder="Введите Telegram ID"
                id="new-telegram-id"
                sx={{ mr: 1 }}
              />
              <Button
                variant="contained"
                color="primary"
                onClick={() => {
                  const input = document.getElementById('new-telegram-id');
                  addAllowedTelegramId(input.value);
                  input.value = '';
                }}
                startIcon={<AddIcon />}
              >
                Добавить
              </Button>
            </Box>
          </Paper>

          <Button
            type="submit"
            fullWidth
            variant="contained"
            color="primary"
            disabled={loading}
            sx={{ mt: 2 }}
          >
            {loading ? <CircularProgress size={24} /> : "Сохранить изменения"}
          </Button>
        </Box>
      </Box>
    </Container>
  );
};

export default EditKidPage;
