"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import {
  Box,
  Button,
  TextField,
  Typography,
  Container,
  CircularProgress,
  Alert,
  MenuItem,
  Checkbox,
  ListItemText,
} from "@mui/material";
import { useForm, Controller } from "react-hook-form";
import axios from "@/axiosInstance";
import useAuthStore from "@/store/authStore";

const CreateKidPage = () => {
  const router = useRouter();
  const { user } = useAuthStore();
  const [groups, setGroups] = useState([]);
  const [parents, setParents] = useState([]);
  const [loading, setLoading] = useState(false);
  const [errorMessage, setErrorMessage] = useState(null);
  const [selectedParents, setSelectedParents] = useState([]);

  const {
    register,
    handleSubmit,
    control,
    getValues,
    setValue,
    formState: { errors },
  } = useForm({
    defaultValues: {
      groupId: '',
    },
  });

  useEffect(() => {
    fetchGroups();
    fetchParents();
  }, []);


  const fetchGroups = async () => {
    try {
      const response = await axios.get("/groups");
      setGroups(response.data);

      const selectedGroup = getValues('groupId');

      if (response.data.length > 0) {
        setValue('groupId', response.data[0].id);
      }
    } catch (error) {
      console.error("Error fetching groups:", error);
      setErrorMessage("Ошибка загрузки списка групп.");
    }
  };

  const fetchParents = async () => {
    try {
      const response = await axios.get("/users?role=PARENT");
      setParents(response.data);
    } catch (error) {
      console.error("Error fetching parents:", error);
      setErrorMessage("Ошибка загрузки списка родителей.");
    }
  };

  const onSubmit = async (data) => {
    setLoading(true);
    setErrorMessage(null);

    try {
      const formattedDate = new Date(data.dateOfBirth);
      formattedDate.setHours(12);

      const formData = {
        ...data,
        dateOfBirth: formattedDate.toISOString(),
        parentIds: selectedParents,
        schoolId: user.schoolId,
      };

      await axios.post("/children", formData);
      router.push("/kids");
    } catch (error) {
      setErrorMessage(
        error.response?.data?.message || "Ошибка при создании записи"
      );
    } finally {
      setLoading(false);
    }
  };

  const handleParentChange = (event) => {
    setSelectedParents(event.target.value);
  };

  if (loading) {
    return <Box p={3}>Загрузка...</Box>;
  }

  return (
    <Container maxWidth="sm">
      <Box display="flex" flexDirection="column" alignItems="center" mt={4}>
        <Typography variant="h4" gutterBottom>
          Добавление ребенка
        </Typography>

        {errorMessage && (
          <Alert severity="error" sx={{ width: "100%", mb: 2 }}>
            {errorMessage}
          </Alert>
        )}

        <Box
          component="form"
          onSubmit={handleSubmit(onSubmit)}
          width="100%"
          mt={2}
        >
          <TextField
            fullWidth
            label="Имя"
            variant="outlined"
            margin="normal"
            {...register("name", { required: "Имя обязательно" })}
            error={!!errors.name}
            helperText={errors.name?.message}
          />

          <TextField
            fullWidth
            label="Дата рождения"
            type="date"
            variant="outlined"
            margin="normal"
            InputLabelProps={{
              shrink: true,
            }}
            {...register("dateOfBirth", {
              required: "Дата рождения обязательна",
            })}
            error={!!errors.dateOfBirth}
            helperText={errors.dateOfBirth?.message}
          />

          <Controller
            name="groupId"
            control={control}
            rules={{ required: "Группа обязательна" }}
            render={({ field }) => (
              <TextField
                {...field}
                fullWidth
                select
                label="Группа"
                variant="outlined"
                margin="normal"
                error={!!errors.groupId}
                helperText={errors.groupId?.message}
              >
                {groups.map((group) => (
                  <MenuItem key={group.id} value={group.id}>
                    {group.name}
                  </MenuItem>
                ))}
              </TextField>
            )}
          />

          <TextField
            fullWidth
            select
            label="Родители"
            variant="outlined"
            margin="normal"
            value={selectedParents}
            onChange={handleParentChange}
            SelectProps={{
              multiple: true,
              renderValue: (selected) => {
                const selectedParentNames = parents
                  .filter((parent) => selected.includes(parent.id))
                  .map((parent) => `${parent.firstName} ${parent.lastName}`);
                return selectedParentNames.join(", ");
              },
            }}
          >
            {parents.map((parent) => (
              <MenuItem key={parent.id} value={parent.id}>
                <Checkbox checked={selectedParents.includes(parent.id)} />
                <ListItemText
                  primary={`${parent.firstName} ${parent.lastName}`}
                />
              </MenuItem>
            ))}
          </TextField>

          <Button
            type="submit"
            fullWidth
            variant="contained"
            color="primary"
            disabled={loading}
            sx={{ mt: 2 }}
          >
            {loading ? <CircularProgress size={24} /> : "Добавить ребенка"}
          </Button>
        </Box>
      </Box>
    </Container>
  );
};

export default CreateKidPage;
