"use client";

import { useState, useEffect } from "react";
import axios from "@/axiosInstance";
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Button,
  Typography,
  Box,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
} from "@mui/material";
import { Add as AddIcon, Edit as EditIcon, Delete as DeleteIcon } from "@mui/icons-material";
import { useRouter } from "next/navigation";
import useAuthStore from "@/store/authStore";
import ListPageHeader from '@/components/ListPageHeader';

const KidsPage = () => {
  const router = useRouter();
  const { user } = useAuthStore();
  const [kids, setKids] = useState([]);
  const [groups, setGroups] = useState({});
  const [parents, setParents] = useState({});
  const [loading, setLoading] = useState(false);
  const [errorMessage, setErrorMessage] = useState(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [selectedKid, setSelectedKid] = useState(null);
  const [selectedGroup, setSelectedGroup] = useState("");
  console.log(user);
  useEffect(() => {
    if (user?.schoolId) {
      fetchKids();
      fetchGroups();
      fetchParents();
    }
  }, [user?.schoolId]);

  const fetchKids = async () => {
    setLoading(true);
    try {
      const response = await axios.get(`/children?schoolId=${user.schoolId}`);
      setKids(response.data);
      setErrorMessage(null);
    } catch (error) {
      setErrorMessage("Ошибка загрузки списка детей.");
      console.error("Error fetching kids:", error);
    } finally {
      setLoading(false);
    }
  };

  const fetchGroups = async () => {
    try {
      const response = await axios.get(`/groups?schoolId=${user.schoolId}`);
      const groupsMap = {};
      response.data.forEach((group) => {
        groupsMap[group.id] = group.name;
      });
      setGroups(groupsMap);
    } catch (error) {
      console.error("Error fetching groups:", error);
      setErrorMessage("Ошибка загрузки списка групп.");
    }
  };

  const fetchParents = async () => {
    try {
      const response = await axios.get(`/users/parents?schoolId=${user.schoolId}`);
      const parentsMap = {};
      response.data.forEach((parent) => {
        parentsMap[parent.id] = `${parent.firstName} ${parent.lastName}`;
      });
      setParents(parentsMap);
    } catch (error) {
      console.error("Error fetching parents:", error);
      setErrorMessage("Ошибка загрузки списка родителей.");
    }
  };

  const handleAddKid = () => {
    router.push("/kids/create");
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString("ru-RU");
  };

  const getParentNames = (parentIds) => {
    if (!parentIds || !parentIds.length) return "Не назначены";
    return parentIds
      .map((id) => parents[id])
      .filter(Boolean)
      .join(", ");
  };

  const handleEditKid = (kidId) => {
    router.push(`/kids/${kidId}/edit`);
  };

  const handleDeleteClick = (kid) => {
    setSelectedKid(kid);
    setDeleteDialogOpen(true);
  };

  const handleDeleteConfirm = async () => {
    if (!selectedKid) return;

    setLoading(true);
    try {
      await axios.delete(`/children/${selectedKid.id}`);
      setDeleteDialogOpen(false);
      setSelectedKid(null);
      fetchKids(); // Обновляем список после удаления
    } catch (error) {
      setErrorMessage("Ошибка при удалении ребенка.");
      console.error("Error deleting kid:", error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return <Box>Загрузка...</Box>;
  }

  if (errorMessage) {
    return (
      <Box color="error.main">
        {errorMessage}
      </Box>
    );
  }

  // Компонент карточки для мобильной версии
  const KidCard = ({ kid, onEdit, onDelete }) => (
    <Paper sx={{ p: 2, mb: 2 }}>
      <Box sx={{ mb: 2 }}>
        <Typography variant="h6">{kid.name}</Typography>
        <Typography variant="body2" color="text.secondary">
          Дата рождения: {formatDate(kid.dateOfBirth)}
        </Typography>
        <Typography variant="body2" color="text.secondary">
          Группа: {groups[kid.groupId] || "Не назначена"}
        </Typography>
        <Typography variant="body2" color="text.secondary">
          Родители: {getParentNames(kid.parentIds)}
        </Typography>
        <Typography variant="body2" sx={{ mt: 1 }}>
          Код доступа: {kid.accessCode ? (
            <Box component="span" sx={{ display: 'inline-flex', alignItems: 'center' }}>
              {kid.accessCode}
              {kid.allowedTelegramIds && kid.allowedTelegramIds.length > 0 && (
                <Typography variant="caption" color="primary" sx={{ ml: 1 }}>
                  (Ограниченный доступ)
                </Typography>
              )}
            </Box>
          ) : (
            <Typography component="span" variant="body2" color="text.secondary">
              Не создан
            </Typography>
          )}
        </Typography>
      </Box>
      <Box sx={{ display: 'flex', gap: 1, justifyContent: 'flex-end' }}>
        <Button
          size="small"
          startIcon={<EditIcon />}
          onClick={() => onEdit(kid.id)}
        >
          Редактировать
        </Button>
        <Button
          size="small"
          color="error"
          startIcon={<DeleteIcon />}
          onClick={() => onDelete(kid)}
        >
          Удалить
        </Button>
      </Box>
    </Paper>
  );

  return (
    <Box>
      <ListPageHeader
        title="Дети"
        buttonText="Добавить ребенка"
        onButtonClick={handleAddKid}
      />

      <FormControl
        fullWidth
        margin="normal"
        sx={{ mb: 3, maxWidth: { xs: '100%', sm: '300px' } }}
      >
        <InputLabel id="group-filter-label">Фильтр по группе</InputLabel>
        <Select
          labelId="group-filter-label"
          value={selectedGroup}
          onChange={(e) => setSelectedGroup(e.target.value)}
          label="Фильтр по группе"
        >
          <MenuItem value="">Все группы</MenuItem>
          {Object.entries(groups).map(([id, name]) => (
            <MenuItem key={id} value={id}>{name}</MenuItem>
          ))}
        </Select>
      </FormControl>

      {/* Таблица для десктопа */}
      <Box sx={{ display: { xs: 'none', sm: 'block' } }}>
        <TableContainer component={Paper}>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Имя Фамилия</TableCell>
                <TableCell>Дата рождения</TableCell>
                <TableCell>Группа</TableCell>
                <TableCell>Родители</TableCell>
                <TableCell>Код доступа</TableCell>
                <TableCell>Действия</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {kids
                .filter(kid => !selectedGroup || kid.groupId === selectedGroup)
                .map((kid) => (
                <TableRow key={kid.id}>
                  <TableCell>{kid.name}</TableCell>
                  <TableCell>{formatDate(kid.dateOfBirth)}</TableCell>
                  <TableCell>{groups[kid.groupId] || "Не назначена"}</TableCell>
                  <TableCell>{getParentNames(kid.parentIds)}</TableCell>
                  <TableCell>
                    {kid.accessCode ? (
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <Typography variant="body2" sx={{ mr: 1 }}>
                          {kid.accessCode}
                        </Typography>
                        {kid.allowedTelegramIds && kid.allowedTelegramIds.length > 0 && (
                          <Typography variant="caption" color="primary">
                            (Ограниченный доступ)
                          </Typography>
                        )}
                      </Box>
                    ) : (
                      <Typography variant="body2" color="text.secondary">
                        Не создан
                      </Typography>
                    )}
                  </TableCell>
                  <TableCell>
                    <Button
                      size="small"
                      startIcon={<EditIcon />}
                      onClick={() => handleEditKid(kid.id)}
                      sx={{ mr: 1 }}
                    >
                      Редактировать
                    </Button>
                    <Button
                      size="small"
                      color="error"
                      startIcon={<DeleteIcon />}
                      onClick={() => handleDeleteClick(kid)}
                    >
                      Удалить
                    </Button>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      </Box>

      {/* Карточки для мобильных */}
      <Box sx={{ display: { xs: 'block', sm: 'none' } }}>
        {kids
          .filter(kid => !selectedGroup || kid.groupId === selectedGroup)
          .map((kid) => (
          <KidCard
            key={kid.id}
            kid={kid}
            onEdit={handleEditKid}
            onDelete={handleDeleteClick}
          />
        ))}
      </Box>

      <Dialog
        open={deleteDialogOpen}
        onClose={() => setDeleteDialogOpen(false)}
      >
        <DialogTitle>Подтверждение удаления</DialogTitle>
        <DialogContent>
          Вы уверены, что хотите удалить запись о ребенке "{selectedKid?.name}"?
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialogOpen(false)}>Отмена</Button>
          <Button onClick={handleDeleteConfirm} color="error" autoFocus>
            Удалить
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default KidsPage;
