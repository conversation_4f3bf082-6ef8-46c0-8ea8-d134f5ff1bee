"use client";

import React, { useState, useEffect, Suspense, useCallback } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import {
  Box,
  Button,
  Typography,
  Container,
  CircularProgress,
  Alert,
  Paper,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
} from "@mui/material";
import { Upload as UploadIcon } from "@mui/icons-material";
import axios from "@/axiosInstance";
import useAuthStore from "@/store/authStore";

// Компонент для работы с параметрами URL
function ScheduleParamsHandler({ onParamsReady }) {
  const searchParams = useSearchParams();
  const ageGroupIdFromUrl = searchParams.get('ageGroupId') || 'children_3_6';

  useEffect(() => {
    onParamsReady(ageGroupIdFromUrl);
  }, [ageGroupIdFromUrl, onParamsReady]);

  return null;
}

const EditSchedulePage = () => {
  const router = useRouter();
  const { user } = useAuthStore();
  const [ageGroupIdFromUrl, setAgeGroupIdFromUrl] = useState('children_3_6');

  const [file, setFile] = useState(null);
  const [currentSchedule, setCurrentSchedule] = useState(null);
  const [loading, setLoading] = useState(false);
  const [errorMessage, setErrorMessage] = useState(null);
  const [preview, setPreview] = useState(null);
  const [selectedAgeGroup, setSelectedAgeGroup] = useState(ageGroupIdFromUrl);
  const [ageGroups, setAgeGroups] = useState([]);
  const [loadingAgeGroups, setLoadingAgeGroups] = useState(false);

  // Обработчик получения параметров URL
  const handleParamsReady = useCallback((ageGroupId) => {
    setAgeGroupIdFromUrl(ageGroupId);
    setSelectedAgeGroup(ageGroupId);
  }, []);

  useEffect(() => {
    fetchAgeGroups();
  }, []);

  useEffect(() => {
    if (user?.schoolId && selectedAgeGroup) {
      fetchCurrentSchedule();
    }
  }, [user?.schoolId, selectedAgeGroup]);

  const fetchAgeGroups = async () => {
    try {
      setLoadingAgeGroups(true);
      const response = await axios.get('/skills-category/age-groups/all');
      setAgeGroups(response.data);
    } catch (error) {
      console.error('Error fetching age groups:', error);
    } finally {
      setLoadingAgeGroups(false);
    }
  };

  const fetchCurrentSchedule = async () => {
    try {
      setLoading(true);
      const response = await axios.get(`/schedule/${user?.schoolId}?ageGroupId=${selectedAgeGroup}`);
      setCurrentSchedule(response.data);
      setErrorMessage(null);
    } catch (error) {
      console.error("Error fetching current schedule:", error);
      setErrorMessage(`Расписание для выбранной возрастной группы не найдено`);
      setCurrentSchedule(null);
    } finally {
      setLoading(false);
    }
  };

  const handleAgeGroupChange = (event) => {
    setSelectedAgeGroup(event.target.value);
  };

  const handleFileChange = (event) => {
    const selectedFile = event.target.files[0];
    if (selectedFile && selectedFile.type === "application/pdf") {
      setFile(selectedFile);
      setPreview(URL.createObjectURL(selectedFile));
      setErrorMessage(null);
    } else {
      setErrorMessage("Пожалуйста, выберите PDF файл");
      setFile(null);
      setPreview(null);
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!file) {
      setErrorMessage("Пожалуйста, выберите файл");
      return;
    }

    setLoading(true);
    setErrorMessage(null);

    try {
      const formData = new FormData();
      formData.append("file", file);
      formData.append("ageGroupId", selectedAgeGroup);

      await axios.post(`/schedule/upload/${user.schoolId}`, formData, {
        headers: {
          "Content-Type": "multipart/form-data",
        },
      });

      router.push("/schedule");
    } catch (error) {
      console.error("Ошибка:", error.response?.data);
      setErrorMessage(
        error.response?.data?.message || "Ошибка при обновлении расписания"
      );
    } finally {
      setLoading(false);
    }
  };

  return (
    <Container maxWidth="md">
      {/* Обертка Suspense для компонента, использующего useSearchParams */}
      <Suspense fallback={<Box display="flex" justifyContent="center" mt={4}><CircularProgress /></Box>}>
        <ScheduleParamsHandler onParamsReady={handleParamsReady} />
      </Suspense>

      <Box display="flex" flexDirection="column" alignItems="center" mt={4}>
        <Typography variant="h4" gutterBottom>
          Обновление расписания
        </Typography>

        {errorMessage && (
          <Alert severity="error" sx={{ width: "100%", mb: 2 }}>
            {errorMessage}
          </Alert>
        )}

        <Paper sx={{ p: 3, width: "100%" }}>
          <FormControl fullWidth sx={{ mb: 3 }}>
            <InputLabel id="age-group-select-label">Возрастная группа</InputLabel>
            <Select
              labelId="age-group-select-label"
              value={selectedAgeGroup}
              label="Возрастная группа"
              onChange={handleAgeGroupChange}
              disabled={loadingAgeGroups}
            >
              {ageGroups.map((group) => (
                <MenuItem key={group.id} value={group.id}>
                  {group.name}
                </MenuItem>
              ))}
            </Select>
          </FormControl>

          {currentSchedule && (
            <Box mb={4}>
              <Typography variant="h6" gutterBottom>
                Текущее расписание для группы {ageGroups.find(g => g.id === selectedAgeGroup)?.name || selectedAgeGroup}:
              </Typography>
              <Box height="400px">
                <iframe
                  src={currentSchedule.fileUrl}
                  width="100%"
                  height="100%"
                  style={{ border: "none" }}
                />
              </Box>
            </Box>
          )}

          <Box
            component="form"
            onSubmit={handleSubmit}
            display="flex"
            flexDirection="column"
            alignItems="center"
          >
            <Button
              variant="outlined"
              component="label"
              startIcon={<UploadIcon />}
              sx={{ mb: 2 }}
            >
              Выбрать новый PDF файл
              <input
                type="file"
                hidden
                accept="application/pdf"
                onChange={handleFileChange}
              />
            </Button>

            {preview && (
              <Box mb={2} width="100%" height="400px">
                <Typography variant="h6" gutterBottom>
                  Предпросмотр нового расписания:
                </Typography>
                <iframe
                  src={preview}
                  width="100%"
                  height="100%"
                  style={{ border: "none" }}
                />
              </Box>
            )}

            <Button
              type="submit"
              variant="contained"
              color="primary"
              disabled={loading || !file}
              sx={{ mt: 2 }}
            >
              {loading ? <CircularProgress size={24} /> : "Обновить расписание"}
            </Button>
          </Box>
        </Paper>
      </Box>
    </Container>
  );
};

export default EditSchedulePage;