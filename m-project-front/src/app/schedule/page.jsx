"use client";

import { useState, useEffect } from "react";
import axios from "@/axiosInstance";
import {
  Box,
  Button,
  Typography,
  Paper,
  CircularProgress,
  Alert,
  Modal,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
} from "@mui/material";
import { Add as AddIcon } from "@mui/icons-material";
import { useRouter } from "next/navigation";
import useAuthStore from "@/store/authStore";
import { EmbedPDF } from "@simplepdf/react-embed-pdf";
import './styles.scss'
import ListPageHeader from '@/components/ListPageHeader';

const SchedulePage = () => {
  const router = useRouter();
  const { user } = useAuthStore();
  const [schedule, setSchedule] = useState(null);
  const [schedules, setSchedules] = useState([]);
  const [loading, setLoading] = useState(false);
  const [errorMessage, setErrorMessage] = useState(null);
  const [open, setOpen] = useState(false);
  const [selectedAgeGroup, setSelectedAgeGroup] = useState("children_3_6");
  const [ageGroups, setAgeGroups] = useState([]);
  const [loadingAgeGroups, setLoadingAgeGroups] = useState(false);

  async function fetchAgeGroups() {
    try {
      setLoadingAgeGroups(true);
      const response = await axios.get('/skills-category/age-groups/all');
      setAgeGroups(response.data);
    } catch (error) {
      console.error("Error fetching age groups:", error);
    } finally {
      setLoadingAgeGroups(false);
    }
  }

  async function fetchAllSchedules() {
    try {
      setLoading(true);
      const response = await axios.get(`/schedule/all/${user?.schoolId}`);
      setSchedules(response.data);

      // Если есть расписания, загружаем выбранное
      if (response.data.length > 0) {
        fetchScheduleByAgeGroup(selectedAgeGroup);
      } else {
        setSchedule(null);
        setLoading(false);
      }
    } catch (error) {
      console.error("Error fetching all schedules:", error);
      setErrorMessage("Ошибка загрузки расписаний");
      setLoading(false);
    }
  }

  async function fetchScheduleByAgeGroup(ageGroupId) {
    try {
      setLoading(true);
      const response = await axios.get(`/schedule/${user?.schoolId}?ageGroupId=${ageGroupId}`);
      setSchedule(response.data);
      setErrorMessage(null);
    } catch (error) {
      console.error(`Error fetching schedule for age group ${ageGroupId}:`, error);
      setErrorMessage(`Расписание для выбранной возрастной группы не найдено`);
      setSchedule(null);
    } finally {
      setLoading(false);
    }
  }

  useEffect(() => {
    fetchAgeGroups();
  }, []);

  useEffect(() => {
    if (user?.schoolId) {
      fetchAllSchedules();
    }
  }, [user?.schoolId]);

  useEffect(() => {
    if (user?.schoolId && selectedAgeGroup) {
      fetchScheduleByAgeGroup(selectedAgeGroup);
    }
  }, [selectedAgeGroup, user?.schoolId]);

  const handleAddSchedule = () => {
    router.push("/schedule/create");
  };

  const handleEditSchedule = () => {
    router.push(`/schedule/edit?ageGroupId=${selectedAgeGroup}`);
  };

  const handleOpen = () => setOpen(true);
  const handleClose = () => setOpen(false);

  const handleAgeGroupChange = (event) => {
    setSelectedAgeGroup(event.target.value);
  };

  const getAgeGroupName = (id) => {
    const group = ageGroups.find(group => group.id === id);
    return group ? group.name : id;
  };

  if (loading) {
    return <Box p={3}>Загрузка...</Box>;
  }

  return (
    <Box>
      <ListPageHeader
        title="Расписание"
        buttonText="Загрузить расписание"
        onButtonClick={handleAddSchedule}
      />

      {errorMessage && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {errorMessage}
        </Alert>
      )}

      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <FormControl sx={{ minWidth: 250 }}>
          <InputLabel id="age-group-select-label">Возрастная группа</InputLabel>
          <Select
            labelId="age-group-select-label"
            value={selectedAgeGroup}
            label="Возрастная группа"
            onChange={handleAgeGroupChange}
            disabled={loadingAgeGroups || schedules.length === 0}
          >
            {ageGroups.map((group) => (
              <MenuItem key={group.id} value={group.id}>
                {group.name}
              </MenuItem>
            ))}
          </Select>
        </FormControl>

        {schedule && (
          <Button
            variant="outlined"
            onClick={handleEditSchedule}
            sx={{ ml: 2 }}
          >
            Редактировать
          </Button>
        )}
      </Box>

      {schedule?.fileUrl ? (
        <>
          <Typography variant="subtitle1" gutterBottom>
            Расписание для группы: {getAgeGroupName(selectedAgeGroup)}
          </Typography>
          <EmbedPDF
            className="embed-pdf"
            mode="inline"
            style={{ width: "1000px", height: "500px" }}
            companyIdentifier="react-viewer"
            documentURL={`${schedule.fileUrl}`}
          ></EmbedPDF>
        </>
      ) : (
        <Alert severity="info">
          {schedules.length > 0
            ? `Расписание для возрастной группы "${getAgeGroupName(selectedAgeGroup)}" еще не загружено`
            : 'Расписание еще не загружено'}
        </Alert>
      )}
    </Box>
  );
};

export default SchedulePage;
