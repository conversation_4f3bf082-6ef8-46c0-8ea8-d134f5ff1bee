"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import {
  Box,
  Button,
  Typography,
  Container,
  CircularProgress,
  Alert,
  Paper,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
} from "@mui/material";
import { Upload as UploadIcon } from "@mui/icons-material";
import axios from "@/axiosInstance";
import useAuthStore from "@/store/authStore";

const CreateSchedulePage = () => {
  const router = useRouter();
  const [file, setFile] = useState(null);
  const [loading, setLoading] = useState(false);
  const [errorMessage, setErrorMessage] = useState(null);
  const [preview, setPreview] = useState(null);
  const { user } = useAuthStore();
  const [selectedAgeGroup, setSelectedAgeGroup] = useState("children_3_6");
  const [ageGroups, setAgeGroups] = useState([]);
  const [loadingAgeGroups, setLoadingAgeGroups] = useState(false);

  useEffect(() => {
    // Загрузка возрастных групп
    const fetchAgeGroups = async () => {
      try {
        setLoadingAgeGroups(true);
        const response = await axios.get('/skills-category/age-groups/all');
        setAgeGroups(response.data);
      } catch (error) {
        console.error('Error fetching age groups:', error);
      } finally {
        setLoadingAgeGroups(false);
      }
    };

    fetchAgeGroups();
  }, []);

  const handleFileChange = (event) => {
    const selectedFile = event.target.files[0];
    if (selectedFile && selectedFile.type === "application/pdf") {
      setFile(selectedFile);
      setPreview(URL.createObjectURL(selectedFile));
      setErrorMessage(null);
    } else {
      setErrorMessage("Пожалуйста, выберите PDF файл");
      setFile(null);
      setPreview(null);
    }
  };

  const handleAgeGroupChange = (event) => {
    setSelectedAgeGroup(event.target.value);
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!file) {
      setErrorMessage("Пожалуйста, выберите файл");
      return;
    }

    setLoading(true);
    setErrorMessage(null);

    try {
      const formData = new FormData();
      formData.append("file", file);
      formData.append("ageGroupId", selectedAgeGroup);

      await axios.post(`/schedule/upload/${user.schoolId}`, formData, {
        headers: {
          "Content-Type": "multipart/form-data",
        },
      });

      router.push(`/schedule`);
    } catch (error) {
      console.error("Ошибка:", error.response?.data);
      setErrorMessage(
        error.response?.data?.message || "Ошибка при загрузке расписания"
      );
    } finally {
      setLoading(false);
    }
  };

  return (
    <Container maxWidth="md">
      <Box display="flex" flexDirection="column" alignItems="center" mt={4}>
        <Typography variant="h4" gutterBottom>
          Загрузка расписания
        </Typography>

        {errorMessage && (
          <Alert severity="error" sx={{ width: "100%", mb: 2 }}>
            {errorMessage}
          </Alert>
        )}

        <Paper sx={{ p: 3, width: "100%" }}>
          <Box
            component="form"
            onSubmit={handleSubmit}
            display="flex"
            flexDirection="column"
            alignItems="center"
          >
            <FormControl fullWidth sx={{ mb: 3 }}>
              <InputLabel id="age-group-select-label">Возрастная группа</InputLabel>
              <Select
                labelId="age-group-select-label"
                value={selectedAgeGroup}
                label="Возрастная группа"
                onChange={handleAgeGroupChange}
                disabled={loadingAgeGroups}
              >
                {ageGroups.map((group) => (
                  <MenuItem key={group.id} value={group.id}>
                    {group.name}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
            <Button
              variant="outlined"
              component="label"
              startIcon={<UploadIcon />}
              sx={{ mb: 2 }}
            >
              Выбрать PDF файл
              <input
                type="file"
                hidden
                accept="application/pdf"
                onChange={handleFileChange}
              />
            </Button>

            {preview && (
              <Box mb={2} width="100%" height="500px">
                <iframe
                  src={preview}
                  width="100%"
                  height="100%"
                  style={{ border: "none" }}
                />
              </Box>
            )}

            <Button
              type="submit"
              variant="contained"
              color="primary"
              disabled={loading || !file}
              sx={{ mt: 2 }}
            >
              {loading ? <CircularProgress size={24} /> : "Загрузить расписание"}
            </Button>
          </Box>
        </Paper>
      </Box>
    </Container>
  );
};

export default CreateSchedulePage;