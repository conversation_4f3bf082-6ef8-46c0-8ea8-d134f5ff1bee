import axios from "axios";

const api = axios.create({
  // Используем относительный путь для API
  baseURL: process.env.NODE_ENV === 'production' ?  '/api' : 'http://localhost:3002/api',
  headers: {
    'Content-Type': 'application/json',
  },
  withCredentials: true
});

api.interceptors.request.use(
  (config) => {
    console.log('Request:', config.url, config.method);
    const accessToken = localStorage.getItem("accessToken");

    // Если токена нет и мы не на странице логина/регистрации, выполняем logout
    if (!accessToken &&
        typeof window !== 'undefined' &&
        !window.location.pathname.includes('/login') &&
        !window.location.pathname.includes('/register')) {
      console.warn("⚠️ Отсутствует accessToken, выполняем logout");
      // Импортируем динамически, чтобы избежать циклических зависимостей
      import('@/store/authStore').then(module => {
        const useAuthStore = module.default;
        useAuthStore.getState().logout();
      });
    }

    if (accessToken) {
      config.headers.Authorization = `Bearer ${accessToken}`;
    }
    return config;
  },
  (error) => {
    console.error('Request Error:', error);
    return Promise.reject(error);
  }
);

// 🔹 Перехватываем ответы и разлогиниваем при `401 Unauthorized`
api.interceptors.response.use(
  (response) => response,
  (error) => {
    console.error('Response Error:', error.message);
    console.error('Full Error:', error);

    if (error.response && error.response.status === 401) {
      console.error("⛔️ 401 Unauthorized — разлогиниваем пользователя", error);
      // Импортируем динамически, чтобы избежать циклических зависимостей
      import('@/store/authStore').then(module => {
        const useAuthStore = module.default;
        useAuthStore.getState().logout();
      });
    }
    return Promise.reject(error);
  }
);

export default api;