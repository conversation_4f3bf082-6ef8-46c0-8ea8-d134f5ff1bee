import { ROLES } from './roles';
// Импорт иконок
import HomeIcon from '@mui/icons-material/Home';
import GroupIcon from '@mui/icons-material/Group';
import ChildCareIcon from '@mui/icons-material/ChildCare';
import TrendingUpIcon from '@mui/icons-material/TrendingUp';
import SchoolIcon from '@mui/icons-material/School';
import PeopleIcon from '@mui/icons-material/People';
import EventAvailableIcon from '@mui/icons-material/EventAvailable';
import CalendarMonthIcon from '@mui/icons-material/CalendarMonth';
import RestaurantMenuIcon from '@mui/icons-material/RestaurantMenu';
import DescriptionIcon from '@mui/icons-material/Description';

export const pages = [
  {
    id: 11,
    name: "Главная",
    path: "/",
    roles: [ROLES.ADMIN, ROLES.SUPERADMIN],
    icon: HomeIcon
  },
  {
    id: 1,
    name: "Группы",
    path: "/groups",
    roles: [ROLES.ADMIN, ROLES.SUPERADMIN],
    icon: GroupIcon
  },
  {
    id: 2,
    name: "<PERSON>е<PERSON><PERSON>",
    path: "/kids",
    roles: [ROLES.ADMIN, ROLES.SUPERADMIN],
    icon: ChildCareIcon
  },
  {
    id: 3,
    name: "Прогресс",
    path: "/progress",
    roles: [ROLES.ADMIN, ROLES.SUPERADMIN],
    icon: TrendingUpIcon
  },
  {
    id: 4,
    name: "Школы",
    path: "/schools",
    roles: [ROLES.SUPERADMIN],
    icon: SchoolIcon
  },
  {
    id: 6,
    name: "Пользователи",
    path: "/users",
    roles: [ROLES.SUPERADMIN],
    icon: PeopleIcon
  },
  {
    id: 7,
    name: "Посещаемость",
    path: "/attendance",
    roles: [ROLES.ADMIN, ROLES.SUPERADMIN],
    icon: EventAvailableIcon
  },
  {
    id: 8,
    name: "Расписание",
    path: "/schedule",
    roles: [ROLES.SUPERADMIN],
    icon: CalendarMonthIcon
  },
  {
    id: 9,
    name: "Меню",
    path: "/menu",
    roles: [ROLES.SUPERADMIN],
    icon: RestaurantMenuIcon
  },
  // Временно скрыта вкладка Отчеты
  // {
  //   id: 10,
  //   name: "Отчеты",
  //   path: "/reports",
  //   roles: [ROLES.ADMIN, ROLES.SUPERADMIN],
  //   icon: DescriptionIcon
  // },
];
