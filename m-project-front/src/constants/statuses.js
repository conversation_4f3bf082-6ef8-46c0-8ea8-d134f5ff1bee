// API статусы (используются при отправке на сервер)
export const API_STATUSES = {
  SHOWN: "shown",
  PRACTICED: "practiced",
  MASTERED: "mastered",
  PLANNED: "planned",
  NEED_REVIEW: "needReview",
};

// Отображаемые статусы (используются в интерфейсе)
export const DISPLAY_STATUSES = {
  SHOWN: "Показано",
  PRACTICED: "Практиковался",
  MASTERED: "Освоено",
  PLANNED: "Запланировано",
  NEED_REVIEW: "Надо повторить",
};

// Преобразование API статуса в отображаемый
export const apiToDisplay = {
  [API_STATUSES.SHOWN]: DISPLAY_STATUSES.SHOWN,
  [API_STATUSES.PRACTICED]: DISPLAY_STATUSES.PRACTICED,
  [API_STATUSES.MASTERED]: DISPLAY_STATUSES.MASTERED,
  [API_STATUSES.PLANNED]: DISPLAY_STATUSES.PLANNED,
  [API_STATUSES.NEED_REVIEW]: DISPLAY_STATUSES.NEED_REVIEW,
};

// Преобразование отображаемого статуса в API
export const displayToApi = {
  [DISPLAY_STATUSES.SHOWN]: API_STATUSES.SHOWN,
  [DISPLAY_STATUSES.PRACTICED]: API_STATUSES.PRACTICED,
  [DISPLAY_STATUSES.MASTERED]: API_STATUSES.MASTERED,
  [DISPLAY_STATUSES.PLANNED]: API_STATUSES.PLANNED,
  [DISPLAY_STATUSES.NEED_REVIEW]: API_STATUSES.NEED_REVIEW,
};

// Порядок смены статусов
export const nextStatusMap = {
  [API_STATUSES.SHOWN]: API_STATUSES.PRACTICED,
  [API_STATUSES.PRACTICED]: API_STATUSES.MASTERED,
  [API_STATUSES.MASTERED]: API_STATUSES.PLANNED,
  [API_STATUSES.PLANNED]: API_STATUSES.NEED_REVIEW,
  [API_STATUSES.NEED_REVIEW]: API_STATUSES.SHOWN,
};

// Получение следующего статуса
export const getNextStatus = (currentStatus) => {
  // Если статус не определен, возвращаем SHOWN
  if (!currentStatus) return API_STATUSES.SHOWN;
  
  // Если это API статус, используем nextStatusMap напрямую
  if (Object.values(API_STATUSES).includes(currentStatus)) {
    return nextStatusMap[currentStatus] || API_STATUSES.SHOWN;
  }
  
  // Если это отображаемый статус, сначала преобразуем в API статус
  const apiStatus = displayToApi[currentStatus];
  return apiStatus ? nextStatusMap[apiStatus] : API_STATUSES.SHOWN;
};
