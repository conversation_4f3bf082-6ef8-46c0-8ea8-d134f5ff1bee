<svg xmlns="http://www.w3.org/2000/svg" version="1.1" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:svgjs="http://svgjs.dev/svgjs" width="200" height="200"><svg width="200" height="200" viewBox="0 0 200 200" xmlns="http://www.w3.org/2000/svg">
  <!-- Background -->
  <rect width="200" height="200" fill="#F5F1E9"></rect>

  <!-- House base -->
  <rect x="60" y="100" width="80" height="60" fill="#5C6B3D"></rect>

  <!-- Roof -->
  <polygon points="50,100 100,60 150,100" fill="#4A5630"></polygon>

  <!-- Windows -->
  <rect x="70" y="115" width="15" height="15" fill="#F5F1E9"></rect>
  <rect x="115" y="115" width="15" height="15" fill="#F5F1E9"></rect>

  <!-- Sun -->
  <circle cx="100" cy="40" r="15" fill="#FFD700"></circle>

  <!-- Sun rays -->
  <g stroke="#FFD700" stroke-width="3">
    <line x1="100" y1="15" x2="100" y2="5"></line>
    <line x1="115" y1="20" x2="125" y2="10"></line>
    <line x1="130" y1="40" x2="145" y2="40"></line>
    <line x1="115" y1="60" x2="125" y2="70"></line>
    <line x1="100" y1="65" x2="100" y2="75"></line>
    <line x1="85" y1="60" x2="75" y2="70"></line>
    <line x1="70" y1="40" x2="55" y2="40"></line>
    <line x1="85" y1="20" x2="75" y2="10"></line>
  </g>
</svg><style>@media (prefers-color-scheme: light) { :root { filter: none; } }
@media (prefers-color-scheme: dark) { :root { filter: none; } }
</style></svg>