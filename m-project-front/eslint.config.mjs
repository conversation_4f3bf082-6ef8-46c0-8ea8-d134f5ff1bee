import { dirname } from "path";
import { fileURLToPath } from "url";
import { FlatCompat } from "@eslint/eslintrc";

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const compat = new FlatCompat({
  baseDirectory: __dirname,
});

const eslintConfig = [
  {
    files: ["**/*.js", "**/*.jsx", "**/*.mjs"],
    languageOptions: {
      parser: "espree",
      ecmaVersion: "latest",
      sourceType: "module",
    },
    extends: [
      ...compat.extends("next/core-web-vitals").filter(config => 
        !config.files?.some(pattern => 
          pattern.includes('.ts') || pattern.includes('.tsx')
        )
      )
    ]
  }
];

export default eslintConfig;
