# 📌 Процесс в админ-панели

## **Страница списка школ** (доступ: `superadmin`)
Отображает список всех школ.

**Поля в таблице:**
- `name` – Имя школы
- `address` – Адрес
- `description` – Описание
- `contact` – Телефон
- `location` – Город

---

## **Страница добавления школы** (доступ: `superadmin`)
Создание новой школы.

**Форма:**
- `name` – Имя школы
- `address` – Адрес
- `description` – Описание
- `contact` – Телефон
- `location` – Город

---

## **Страница списка пользователей** (доступ: `superadmin`)
Отобр<PERSON>ж<PERSON><PERSON>т всех пользователей.

**Поля в таблице:**
- `email` – Логин
- `role` – Роль
- `firstName` – Имя
- `lastName` – Фамилия
- `phone` – Телефон

---

## **Страница добавления пользователя** (доступ: `superadmin`, `admin`)
Создание пользователя (админ может добавлять только учителей и родителей).

**Форма:**
- `email` – Логин
- `password` – Пароль (хешируется на сервере)
- `role` – Роль
- `firstName` – Имя
- `lastName` – Фамилия
- `phone` – Телефон
- `schoolId` – Школа (автоматически подставляется у `admin`, у `superadmin` – выбирается из списка)

---

## **Страница списка групп** (доступ: `admin`)
Отображает группы, созданные в школе.

**Поля в таблице:**
- `name` – Название группы
- `description` – Описание
- `teacherIds` – Учителя
- `schoolId` – Текущая школа

---

## **Страница добавления группы** (доступ: `admin`)
Создание группы.

**Форма:**
- `name` – Название группы
- `description` – Описание
- `teacherIds` – Список учителей (может быть пустым)
- `schoolId` – Текущая школа

---

## **Страница списка детей** (доступ: `admin`)
Отображает список детей в школе.

**Поля в таблице:**
- `name` – Имя Фамилия
- `dateOfBirth` – Дата рождения
- `groupId` – Группа
- `parentIds` – Родители

---

## **Страница добавления детей** (доступ: `superadmin`, `admin`)
Добавление нового ребенка.

**Форма:**
- `name` – Имя
- `dateOfBirth` – Дата рождения
- `groupId` – Группа
- `parentIds` – Родители (выбор из списка)

---

## **📌 Посещаемость (Attendance)**

### **Страница списка посещаемости** (доступ: `admin`)
Отображает посещаемость детей. Выбираем из селекта ребенка и отображаем его посещаемость.

**Поля в таблице:**
- `childName` – Имя ребенка
- `date` – Дата
- `status` – Статус (`present`, `absent`, `late`)
- `checkInTime` – Время прихода
- `checkOutTime` – Время ухода

### **Страница добавления посещаемости** (доступ: `admin`)
Добавление новой записи о посещаемости.

**Форма:**
- `childId` – Выбор ребенка
- `date` – Дата
- `status` – `present` / `absent`
- `checkInTime` – Время прихода
- `checkOutTime` – Время ухода

### **Страница редактирования посещаемости** (доступ: `admin`)
Редактирование существующей записи.

**Редактируемые поля:**
- `status` – `present` / `absent`
- `checkInTime`
- `checkOutTime`

---

## **📌 Расписание (Schedule)**

### **Страница списка расписания** (доступ: `admin`)
Отображает занятия и мероприятия.

- Отображаем файл pdf формата с сервера в виде preview блока на фронте
<!-- 
**Поля в таблице:**
- `date` – Дата
- `time` – Время начала
- `groupId` – Группа
- `activity` – Название активности
- `teacherId` – Преподаватель -->

### **Страница добавления расписания** (доступ: `admin`)
Создание записи в расписании.

- Загружаем pdf файл и сохраняем на сервере 

<!-- **Форма:**
- `date` – Дата
- `time` – Время начала
- `groupId` – Группа
- `activity` – Название занятия
- `teacherId` – Преподаватель -->

### **Страница редактирования расписания** (доступ: `admin`)
Редактирование записи в расписании.

- Загружаем pdf файл и сохраняем на сервере, таким образом работает обновление

<!-- **Редактируемые поля:**
- `date`
- `time`
- `activity`
- `teacherId` -->

---

## **📌 Меню питания (Menu)**

### **Страница списка меню** (доступ: `admin`)
Отображает меню на день.

- Отображаем файл pdf формата с сервера в виде preview блока на фронте

<!-- **Поля в таблице:**
- `date` – Дата
- `mealType` – `Завтрак` / `Обед` / `Ужин`
- `description` – Описание еды -->

### **Страница добавления меню** (доступ: `admin`)
Добавление нового блюда.

- Загружаем pdf файл и сохраняем на сервере

<!-- **Форма:**
- `date` – Дата
- `mealType` – `Завтрак` / `Обед` / `Ужин`
- `description` – Описание -->

### **Страница редактирования меню** (доступ: `admin`)
Редактирование меню.

- Загружаем pdf файл и сохраняем на сервере, таким образом работает обновление

<!-- **Редактируемые поля:**
- `mealType`
- `description` -->