/** @type {import('next').NextConfig} */
const nextConfig = {

  // Отключаем ESLint во время сборки для ускорения деплоя
  eslint: {
    ignoreDuringBuilds: true,
  },
  // Отключаем проверку типов во время сборки для ускорения деплоя
  typescript: {
    ignoreBuildErrors: true,
  },
  // Настройка заголовков безопасности
  async headers() {
    return [
      {
        // Специальные заголовки для PDF-файлов
        source: '/files/:path*.pdf',
        headers: [
          {
            key: 'Content-Type',
            value: 'application/pdf',
          },
          {
            key: 'Content-Disposition',
            value: 'inline',
          },
          {
            key: 'Access-Control-Allow-Origin',
            value: '*',
          },
        ],
      },
      {
        // Применяем ко всем маршрутам
        source: '/:path*',
        headers: [
          // Полностью отключаем CSP
          {
            key: 'Content-Security-Policy',
            value: "",
          },
          {
            key: 'Access-Control-Allow-Origin',
            value: '*',
          },
          {
            key: 'Access-Control-Allow-Methods',
            value: 'GET, POST, PUT, DELETE, OPTIONS',
          },
          {
            key: 'Access-Control-Allow-Headers',
            value: 'X-Requested-With, Content-Type, Accept',
          },
        ],
      },
    ];
  },
};

module.exports = nextConfig;