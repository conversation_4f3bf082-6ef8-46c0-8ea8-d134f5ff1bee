# Настройка Яндекс Метрики в Telegram Mini App

## Общая информация

В проекте реализована интеграция с Яндекс Метрикой для сбора статистики использования Telegram Mini App. Это позволяет отслеживать:

- Просмотры страниц
- Переходы между разделами
- Действия пользователей (авторизация, выбор ребенка, просмотр документов)
- Ошибки и проблемы в работе приложения

## Настройка счетчика

1. Счетчик уже создан в [кабинете Яндекс Метрики](https://metrika.yandex.ru/)
2. Используйте ID счетчика `101522325`
3. ID счетчика уже добавлен в файлы:
   - `.env.local` - для локальной разработки
   - `vercel.json` - для продакшн-окружения

```
# В .env.local
NEXT_PUBLIC_YANDEX_METRIKA_ID=101522325
```

```json
// В vercel.json
"env": {
  "NEXT_PUBLIC_API_URL": "https://mymontessory.ru/api",
  "NEXT_PUBLIC_CLIENT_API_URL": "https://mymontessory.ru/api",
  "NEXT_PUBLIC_YANDEX_METRIKA_ID": "101522325"
}
```

## Отслеживаемые события

В приложении настроено отслеживание следующих событий:

### Авторизация
- `auth_start` - начало процесса авторизации
- `auth_success` - успешная авторизация
- `auth_error` - ошибка авторизации

### Навигация
- `page_view` - просмотр основной страницы (Главная, Прогресс, Информация)
- `tab_change` - переключение между вкладками внутри страницы (например, Меню/Расписание)

### Действия с детьми
- `child_select` - выбор ребенка
- `child_connect` - подключение ребенка

### Просмотр документов
- `document_view` - просмотр документа (меню, расписание, календарь)
- `document_download` - открытие документа в браузере

### Просмотр прогресса
- `progress_view` - просмотр прогресса
- `skill_details` - просмотр деталей навыка

## Добавление новых событий

Для добавления отслеживания новых событий используйте функцию `sendMetrikaEvent` из модуля `@/utils/metrikaEvents`:

```javascript
import { sendMetrikaEvent, MetrikaEvents } from '@/utils/metrikaEvents';

// Отправка события с дополнительными параметрами
sendMetrikaEvent(MetrikaEvents.DOCUMENT_VIEW, {
  document_url: documentUrl,
  document_type: 'menu'
});

// Отправка пользовательского события
sendMetrikaEvent('custom_event_name', {
  param1: 'value1',
  param2: 'value2'
});
```

## Особенности работы в Telegram Mini App

Яндекс Метрика в Telegram Mini App имеет некоторые особенности:

1. Webview в Telegram воспринимается Яндекс Метрикой как новый браузер
2. Сессии могут быть короче, чем в обычном веб-приложении
3. Некоторые события могут не отслеживаться, если пользователь быстро закрывает приложение

Для улучшения качества сбора данных:
- Используйте параметр `defer: true` при инициализации счетчика
- Отправляйте события вручную через `hit` и `reachGoal`
- Добавляйте информацию о Telegram в параметры событий

## Просмотр статистики

Статистика доступна в [кабинете Яндекс Метрики](https://metrika.yandex.ru/) после авторизации.

## Отслеживание пользователей и детей

В аналитику автоматически добавляется информация о пользователе (родителе) и ребенке:

- **ID пользователя** (родителя) - извлекается из JWT токена
- **Роль пользователя** - извлекается из JWT токена
- **Telegram ID пользователя** - извлекается из JWT токена
- **ID ребенка** - берется из localStorage

Это позволяет анализировать поведение конкретных пользователей и отслеживать, как родители взаимодействуют с информацией о своих детях.

## Исключение тестовых пользователей

В проекте настроено исключение тестовых пользователей из аналитики. Пользователь с ID `d7f0a801-a746-4738-9434-e6a00a158644` не отправляет данные в Яндекс Метрику.

Если вам нужно добавить дополнительные тестовые ID, отредактируйте константу `EXCLUDED_USER_ID` в следующих файлах:
- `src/utils/metrikaEvents.js`
- `src/components/YandexMetrika/YandexMetrika.jsx`

Для исключения нескольких пользователей можно изменить логику функции `shouldExcludeUser()` в этих файлах, например:

```javascript
// Массив ID пользователей, которых нужно исключить из аналитики
const EXCLUDED_USER_IDS = [
  'd7f0a801-a746-4738-9434-e6a00a158644',
  // Добавьте другие ID здесь
];

const shouldExcludeUser = () => {
  if (typeof window === 'undefined') return false;

  try {
    // Проверяем ID пользователя в localStorage
    const childId = localStorage.getItem('childId');
    if (EXCLUDED_USER_IDS.includes(childId)) return true;

    // Проверяем, есть ли ID пользователя в URL
    const url = window.location.href;
    if (EXCLUDED_USER_IDS.some(id => url.includes(id))) return true;

    return false;
  } catch (error) {
    console.error('Ошибка при проверке ID пользователя:', error);
    return false;
  }
};
