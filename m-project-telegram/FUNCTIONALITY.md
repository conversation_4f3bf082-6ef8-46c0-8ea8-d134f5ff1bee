# M-Project Telegram Mini App

## Общее описание

M-Project Telegram Mini App - это интегрированное в Telegram приложение для родителей, которое позволяет отслеживать прогресс детей в образовательном учреждении, просматривать расписание, меню, информацию о преподавателях и получать доступ к отчетам. Приложение обеспечивает удобный и быстрый доступ к важной информации прямо из интерфейса Telegram.

## Архитектура приложения

### Фронтенд (Telegram Mini App)
- **Фреймворк**: Next.js
- **UI библиотека**: Material UI
- **PDF просмотрщик**: Встроенный просмотрщик PDF
- **Интеграция**: Telegram Web App API

### Бэкенд
- **Фреймворк**: NestJS
- **База данных**: PostgreSQL (через Prisma ORM)
- **Аутентификация**: Интеграция с Telegram Login Widget (планируется)
- **Хранение файлов**: Локальная файловая система

### Взаимодействие компонентов
- Telegram Mini App взаимодействует с бэкендом через REST API
- Бэкенд предоставляет данные из базы данных PostgreSQL
- Файлы (PDF документы, отчеты) хранятся на сервере и доступны через API

## Основные функциональные возможности

### Авторизация
- Авторизация пользователя через Telegram аккаунт
- Безопасный доступ к персональным данным
- Система ролей с фокусом на роль "parent" (родитель)
- Автоматическая аутентификация через Telegram

### Управление профилями детей
- Выбор ребенка из списка (для родителей с несколькими детьми)
- Просмотр основной информации о ребенке (имя, дата рождения, группа)
- Переключение между профилями детей

### Информационный раздел
- **Меню**: Просмотр меню питания в формате PDF
- **Расписание**: Просмотр расписания занятий в формате PDF
- **Преподаватели**: Список преподавателей с информацией о них

### Прогресс и отчеты
- Таблица прогресса ребенка с визуализацией достижений по различным навыкам
- Вкладка с отчетами для скачивания документов
- История посещаемости и успеваемости
- Персонализированные отчеты по каждому ребенку

### Навигация
- Интуитивно понятная навигация между разделами через нижнее меню
- Адаптация под размер окна Telegram
- Оптимизированный интерфейс для мобильных устройств

## Технические детали

### Фронтенд
- Использование React Hooks для управления состоянием
- Динамическая загрузка компонентов для оптимизации производительности
- Адаптивный дизайн для различных устройств
- Интеграция с Telegram Web App API для доступа к функциям Telegram

### Бэкенд
- RESTful API для взаимодействия с клиентом
- Модульная архитектура с разделением на сервисы
- Использование Prisma ORM для работы с базой данных
- Интеграция с Telegram для аутентификации пользователей
- Загрузка и хранение файлов (отчеты, меню, расписание)

### API эндпоинты для Telegram Mini App

#### Дети и профили
- `GET /children` - получение списка детей для родителя
- `GET /children/:id/details` - получение детальной информации о ребенке
- `GET /children/with-progress` - получение списка детей с информацией о прогрессе

#### Информационные материалы
- `GET /menu/:schoolId` - получение информации о меню
- `GET /menu/download/:schoolId` - скачивание PDF файла с меню
- `GET /schedule/:schoolId` - получение информации о расписании
- `GET /schedule/download/:schoolId` - скачивание PDF файла с расписанием

#### Прогресс и отчеты
- `GET /progress` - получение информации о прогрессе
- `GET /reports/:schoolId` - получение списка доступных отчетов
- `GET /reports/child/:childId` - получение списка отчетов для конкретного ребенка

### База данных
- Реляционная структура с связями между сущностями
- Основные сущности:
  - User (пользователи системы)
  - School (образовательные учреждения)
  - Child (дети)
  - Group (группы детей)
  - Progress (прогресс по навыкам)
  - Attendance (посещаемость)
  - Report (отчеты)
  - Menu (меню питания)
  - Schedule (расписание)

### Структура ключевых моделей данных

#### Child (Ребенок)
```prisma
model Child {
  id          String   @id @default(uuid())
  name        String
  dateOfBirth DateTime
  groupId     String
  schoolId    String
  group       Group    @relation(fields: [groupId], references: [id])
  school      School   @relation(fields: [schoolId], references: [id])
  parentIds   String[]
  photoUrl    String?
  isDeleted   Boolean  @default(false)

  notes       Note[]
  progress    Progress[]
  attendance  Attendance[]
  feeds       Feed[]
  reports     Report[]
  photos      Photo[]  @relation("ChildPhotos")
}
```

#### Progress (Прогресс)
```prisma
model Progress {
  id        String   @id @default(uuid())
  childId   String
  skillId   String
  status    String // Например: "in_progress", "completed"
  updatedAt DateTime @updatedAt

  child Child @relation(fields: [childId], references: [id])
}
```

#### Report (Отчет)
```prisma
model Report {
  id        String   @id @default(uuid())
  childId   String
  schoolId  String
  authorId  String
  fileUrl   String   // URL файла отчёта
  type      String   // Тип отчёта: "progress", "attendance", "custom"
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  child  Child  @relation(fields: [childId], references: [id])
  school School @relation(fields: [schoolId], references: [id])
  author User   @relation(fields: [authorId], references: [id])
}
```

#### Menu и Schedule (Меню и Расписание)
```prisma
model Menu {
  id        String   @id @default(uuid())
  schoolId  String   @unique
  fileUrl   String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  school School @relation(fields: [schoolId], references: [id])
}

model Schedule {
  id        String   @id @default(uuid())
  schoolId  String   @unique
  fileUrl   String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  school School @relation(fields: [schoolId], references: [id])
}
```

## Инструкции по установке и запуску

### Требования
- Node.js 16+
- PostgreSQL
- Docker (опционально)

### Установка и запуск бэкенда
1. Клонировать репозиторий
2. Установить зависимости: `npm install`
3. Создать файл `.env` с необходимыми переменными окружения
4. Запустить миграции Prisma: `npx prisma migrate dev`
5. Запустить сервер: `npm run start:dev`

### Установка и запуск фронтенда
1. Клонировать репозиторий
2. Установить зависимости: `npm install`
3. Создать файл `.env.local` с необходимыми переменными окружения
4. Запустить сервер разработки: `npm run dev`

### Запуск с использованием Docker
1. Собрать и запустить контейнеры: `docker-compose up -d`

## Дальнейшее развитие

### Планируемые функции
- Интеграция с системой уведомлений Telegram
- Расширенная аналитика прогресса ребенка
- Система обратной связи для родителей
- Календарь событий и мероприятий
- Чат с преподавателями

### Возможные улучшения
- Оптимизация производительности при загрузке PDF-документов
- Улучшение UX/UI для более интуитивного взаимодействия
- Расширение функциональности для преподавателей и администраторов
- Интеграция с другими образовательными сервисами

## Интеграция с существующим бэкендом

Для интеграции Telegram Mini App с существующим бэкендом необходимо:

1. **Реализовать авторизацию через Telegram**:
   - Добавить в модель User поле telegramId
   - Создать механизм связывания Telegram аккаунта с существующим аккаунтом родителя
   - Реализовать проверку данных, полученных от Telegram Web App

2. **Адаптировать существующие API для Telegram Mini App**:
   - Создать специальные эндпоинты для Telegram Mini App или адаптировать существующие
   - Обеспечить оптимизацию запросов для мобильных устройств
   - Реализовать кэширование данных для ускорения работы приложения

3. **Обеспечить безопасность**:
   - Проверка подлинности запросов от Telegram
   - Ограничение доступа к данным только для авторизованных пользователей
   - Защита от CSRF и других типов атак
