'use client'

import { useEffect } from 'react'
import { usePathname, useSearchParams } from 'next/navigation'
import { getUserIdFromToken, getUserRoleFromToken, getTelegramIdFromToken } from '@/utils/jwtHelper'

// ID пользователя, которого нужно исключить из аналитики (тестовый пользователь)
const EXCLUDED_USER_ID = 'd7f0a801-a746-4738-9434-e6a00a158644';

/**
 * Проверяет, нужно ли исключить текущего пользователя из аналитики
 * @returns {boolean} true, если пользователя нужно исключить
 */
const shouldExcludeUser = () => {
  if (typeof window === 'undefined') return false;

  try {
    // Проверяем ID пользователя в localStorage
    const childId = localStorage.getItem('childId');
    if (childId === EXCLUDED_USER_ID) return true;

    // Проверяем, есть ли ID пользователя в URL
    const url = window.location.href;
    if (url.includes(EXCLUDED_USER_ID)) return true;

    return false;
  } catch (error) {
    console.error('Ошибка при проверке ID пользователя:', error);
    return false;
  }
};

export default function YandexMetrika({ metrikaId }) {
  const pathname = usePathname()
  const searchParams = useSearchParams()

  useEffect(() => {
    // Проверяем, нужно ли исключить пользователя из аналитики
    if (shouldExcludeUser()) {
      if (process.env.NODE_ENV !== 'production') {
        console.log('[Метрика] Просмотр страницы не отправлен (тестовый пользователь)');
      }
      return;
    }

    // Проверяем, что мы на клиенте и что ym определен
    if (typeof window !== 'undefined' && window.ym) {
      const url = `${pathname}${searchParams.toString() ? `?${searchParams.toString()}` : ''}`

      // Получаем информацию о пользователе
      const userId = getUserIdFromToken();
      const userRole = getUserRoleFromToken();
      const telegramId = getTelegramIdFromToken();

      // Получаем ID ребенка из localStorage
      let childId = null;
      try {
        childId = localStorage.getItem('childId');
      } catch (error) {
        console.error('Ошибка при получении ID ребенка:', error);
      }

      // Отправляем информацию о просмотре страницы
      window.ym(metrikaId, 'hit', url);

      // Отправляем дополнительные параметры
      const params = {};

      // Добавляем информацию о пользователе
      if (userId) {
        params.user = {
          id: userId,
          role: userRole || 'unknown',
          telegramId: telegramId || 'unknown'
        };
      }

      // Добавляем информацию о ребенке
      if (childId) {
        params.child = {
          id: childId
        };
      }

      // Добавляем информацию о Telegram, если доступно
      if (window.Telegram?.WebApp) {
        params.telegram = {
          initDataUnsafe: window.Telegram.WebApp.initDataUnsafe,
          platform: window.Telegram.WebApp.platform,
          viewportHeight: window.Telegram.WebApp.viewportHeight,
          viewportStableHeight: window.Telegram.WebApp.viewportStableHeight,
        };
      }

      // Отправляем параметры в Яндекс Метрику
      window.ym(metrikaId, 'params', params);
    }
  }, [pathname, searchParams, metrikaId])

  return null
}
