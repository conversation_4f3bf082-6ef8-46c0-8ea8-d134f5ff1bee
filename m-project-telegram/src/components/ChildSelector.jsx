'use client';

import { useState, useEffect } from 'react';
import { Box, FormControl, Select, MenuItem, Typography } from '@mui/material';
import api from '@/utils/axiosInstance';
import { sendMetrikaEvent, MetrikaEvents } from '@/utils/metrikaEvents';

const ChildSelector = ({ onChildSelect }) => {
  const [children, setChildren] = useState([]);
  const [selectedChildId, setSelectedChildId] = useState('');
  const [loading, setLoading] = useState(true);

  // Загрузка списка детей
  // Проверка токена в localStorage
  useEffect(() => {
    // Используем отложенное выполнение для предотвращения ошибок гидратации
    const checkToken = () => {
      const token = localStorage.getItem('accessToken');
      console.log('Токен в localStorage:', token ? 'найден' : 'не найден');

      // Декодируем JWT токен для проверки содержимого
      if (token) {
        try {
          const base64Url = token.split('.')[1];
          const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
          const jsonPayload = decodeURIComponent(atob(base64).split('').map(function(c) {
            return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
          }).join(''));

          console.log('Декодированный JWT токен:', JSON.parse(jsonPayload));
        } catch (e) {
          console.error('Ошибка при декодировании JWT токена:', e);
        }
      }
    };

    // Используем setTimeout для отложенного выполнения после гидратации
    const timer = setTimeout(checkToken, 0);
    return () => clearTimeout(timer);
  }, []);

  // Загрузка списка детей
  useEffect(() => {
    const fetchChildren = async () => {
      try {
        setLoading(true);
        console.log('Запрос к API для получения списка детей...');
        // Запрашиваем список детей с информацией о группах
        const response = await api.get('/children/my-children');
        console.log('Получен ответ от API:', response.data);

        // Дополнительно запрашиваем информацию о группах, если в ответе нет ageGroupId
        if (response.data && response.data.length > 0 && response.data[0].group && !response.data[0].group.ageGroupId) {
          // Если в данных о группе нет ageGroupId, пытаемся получить дополнительную информацию
          try {
            const groupIds = response.data.map(child => child.group?.id).filter(Boolean);
            if (groupIds.length > 0) {
              // Запрашиваем информацию о группах
              const groupsPromises = groupIds.map(groupId => api.get(`/groups/${groupId}`));
              const groupsResponses = await Promise.all(groupsPromises);

              // Дополняем данные о детях информацией о группах
              const groupsData = groupsResponses.map(resp => resp.data);
              const groupsMap = {};
              groupsData.forEach(group => {
                if (group && group.id) {
                  groupsMap[group.id] = group;
                }
              });

              // Обновляем данные о детях
              response.data = response.data.map(child => {
                if (child.group && child.group.id && groupsMap[child.group.id]) {
                  return {
                    ...child,
                    group: {
                      ...child.group,
                      ageGroupId: groupsMap[child.group.id].ageGroupId || 'children_3_6'
                    }
                  };
                }
                return child;
              });
            }
          } catch (groupError) {
            console.error('Ошибка при получении данных о группах:', groupError);
          }
        }

        if (response.data && response.data.length > 0) {
          setChildren(response.data);

          // Получаем сохраненный ID ребенка из localStorage или используем первого ребенка из списка
          let savedChildId;
          try {
            savedChildId = localStorage.getItem('childId');
          } catch (e) {
            console.error('Ошибка при получении childId из localStorage:', e);
            savedChildId = null;
          }
          const initialChildId = savedChildId && response.data.some(child => child.id === savedChildId)
            ? savedChildId
            : response.data[0].id;

          // Если ID ребенка изменился, обновляем состояние
          if (initialChildId !== selectedChildId) {
            setSelectedChildId(initialChildId);

            // Сохраняем schoolId и информацию о группе выбранного ребенка
            const selectedChild = response.data.find(child => child.id === initialChildId);
            if (selectedChild) {
              if (selectedChild.schoolId) {
                console.log('Сохраняем schoolId:', selectedChild.schoolId);
                localStorage.setItem('schoolId', selectedChild.schoolId);
              }

              // Сохраняем информацию о группе ребенка
              if (selectedChild.group) {
                console.log('Сохраняем информацию о группе:', selectedChild.group);
                localStorage.setItem('childGroupId', selectedChild.group.id);
                // Если в API возвращается ageGroupId группы, сохраняем его
                if (selectedChild.group.ageGroupId) {
                  localStorage.setItem('childAgeGroupId', selectedChild.group.ageGroupId);
                }
              }
            }

            // Вызываем колбэк только если ID изменился
            if (onChildSelect && initialChildId) {
              console.log('Вызываем onChildSelect с ID:', initialChildId);
              onChildSelect(initialChildId);
            }
          } else {
            console.log('Идентификатор ребенка не изменился, пропускаем обновление');
          }
        }
      } catch (error) {
        console.error('Ошибка при загрузке списка детей:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchChildren();
  }, [onChildSelect]);

  // Обработчик изменения выбранного ребенка
  const handleChildChange = (event) => {
    const childId = event.target.value;
    setSelectedChildId(childId);
    localStorage.setItem('childId', childId);

    // Обновляем информацию о группе при смене ребенка
    const selectedChild = children.find(child => child.id === childId);
    if (selectedChild) {
      if (selectedChild.schoolId) {
        localStorage.setItem('schoolId', selectedChild.schoolId);
      }

      if (selectedChild.group) {
        localStorage.setItem('childGroupId', selectedChild.group.id);
        if (selectedChild.group.ageGroupId) {
          localStorage.setItem('childAgeGroupId', selectedChild.group.ageGroupId);
        }
      }

      // Отправляем событие о выборе ребенка
      sendMetrikaEvent(MetrikaEvents.CHILD_SELECT, {
        child_id: childId,
        child_name: selectedChild.name,
        group_id: selectedChild.group?.id,
        group_name: selectedChild.group?.name
      });
    }

    if (onChildSelect) {
      onChildSelect(childId);
    }
  };

  // Если у родителя только один ребенок или данные загружаются, не показываем селектор
  if (loading || children.length <= 1) {
    return null;
  }

  return (
    <Box sx={{
      display: 'flex',
      alignItems: 'center',
      p: 1,
      bgcolor: '#F0F5E1',
      borderRadius: '4px',
      mb: 2
    }}>
      <Typography variant="body2" sx={{ mr: 1, color: '#556B2F' }}>
        Ребенок:
      </Typography>
      <FormControl size="small" sx={{ minWidth: 120 }}>
        <Select
          value={selectedChildId}
          onChange={handleChildChange}
          displayEmpty
          sx={{
            '& .MuiSelect-select': {
              py: 0.5,
              color: '#556B2F',
              fontWeight: 'medium',
            },
            '& .MuiOutlinedInput-notchedOutline': {
              borderColor: '#8FBC8F',
            },
            '&:hover .MuiOutlinedInput-notchedOutline': {
              borderColor: '#6B8E23',
            },
            '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
              borderColor: '#556B2F',
            },
          }}
        >
          {children.map((child) => (
            <MenuItem key={child.id} value={child.id}>
              {child.name}
            </MenuItem>
          ))}
        </Select>
      </FormControl>
    </Box>
  );
};

export default ChildSelector;
