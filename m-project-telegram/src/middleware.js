import { NextResponse } from 'next/server';

export function middleware(request) {
  // Получаем путь из URL
  const path = request.nextUrl.pathname;

  // Если путь содержит /attendance, перенаправляем на главную страницу
  if (path.includes('/attendance')) {
    return NextResponse.redirect(new URL('/home', request.url));
  }

  // В остальных случаях продолжаем обработку запроса
  return NextResponse.next();
}

// Указываем, к каким путям применять middleware
export const config = {
  matcher: [
    // Применяем middleware только к пути /attendance
    '/attendance/:path*',
  ],
};
