/**
 * Функция для выхода из аккаунта
 * Удаляет токен из localStorage и перезагружает страницу
 */
export const logout = () => {
  if (typeof window !== 'undefined') {
    localStorage.removeItem('accessToken');
    window.location.reload();
  }
};

/**
 * Проверяет, авторизован ли пользователь
 * @returns {boolean} true, если пользователь авторизован
 */
export const isAuthenticated = () => {
  if (typeof window !== 'undefined') {
    return !!localStorage.getItem('accessToken');
  }
  return false;
};
