/**
 * Утилиты для отправки событий в Яндекс Метрику
 */

import { getUserIdFromToken, getUserRoleFromToken, getTelegramIdFromToken } from './jwtHelper';

// ID пользователя, которого нужно исключить из аналитики (тестовый пользователь)
const EXCLUDED_USER_ID = 'd7f0a801-a746-4738-9434-e6a00a158644';

/**
 * Проверяет, нужно ли исключить текущего пользователя из аналитики
 * @returns {boolean} true, если пользователя нужно исключить
 */
const shouldExcludeUser = () => {
  if (typeof window === 'undefined') return false;

  try {
    // Проверяем ID пользователя в localStorage
    const childId = localStorage.getItem('childId');
    if (childId === EXCLUDED_USER_ID) return true;

    // Проверяем, есть ли ID пользователя в URL
    const url = window.location.href;
    if (url.includes(EXCLUDED_USER_ID)) return true;

    return false;
  } catch (error) {
    console.error('Ошибка при проверке ID пользователя:', error);
    return false;
  }
};

/**
 * Отправляет событие в Яндекс Метрику
 * @param {string} eventName - Название события
 * @param {Object} [params] - Дополнительные параметры события
 */
export const sendMetrikaEvent = (eventName, params = {}) => {
  // Проверяем, нужно ли исключить пользователя из аналитики
  if (shouldExcludeUser()) {
    if (process.env.NODE_ENV !== 'production') {
      console.log(`[Метрика] Событие не отправлено (тестовый пользователь): ${eventName}`, params);
    }
    return;
  }

  if (typeof window !== 'undefined' && window.ym) {
    const metrikaId = process.env.NEXT_PUBLIC_YANDEX_METRIKA_ID || "101522325";

    // Добавляем информацию о пользователе
    const userId = getUserIdFromToken();
    const userRole = getUserRoleFromToken();
    const telegramId = getTelegramIdFromToken();

    // Добавляем ID пользователя и другую информацию в параметры
    if (userId) {
      params.user = {
        id: userId,
        role: userRole || 'unknown',
        telegramId: telegramId || 'unknown'
      };
    }

    // Добавляем ID ребенка, если он есть в localStorage
    try {
      const childId = localStorage.getItem('childId');
      if (childId) {
        params.child = {
          id: childId
        };
      }
    } catch (error) {
      console.error('Ошибка при получении ID ребенка:', error);
    }

    // Добавляем информацию о Telegram, если доступно
    if (window.Telegram?.WebApp) {
      params.telegram = {
        platform: window.Telegram.WebApp.platform,
        version: window.Telegram.WebApp.version,
      };
    }

    // Отправляем событие в Яндекс Метрику
    window.ym(metrikaId, 'reachGoal', eventName, params);

    if (process.env.NODE_ENV !== 'production') {
      console.log(`[Метрика] Событие: ${eventName}`, params);
    }
  }
};

/**
 * Предопределенные события для Telegram Mini App
 */
export const MetrikaEvents = {
  // Авторизация
  AUTH_START: 'auth_start',
  AUTH_SUCCESS: 'auth_success',
  AUTH_ERROR: 'auth_error',
  LOGOUT: 'logout',

  // Навигация
  PAGE_VIEW: 'page_view',
  TAB_CHANGE: 'tab_change',

  // Действия с детьми
  CHILD_SELECT: 'child_select',
  CHILD_CONNECT: 'child_connect',

  // Просмотр документов
  DOCUMENT_VIEW: 'document_view',
  DOCUMENT_DOWNLOAD: 'document_download',

  // Просмотр прогресса
  PROGRESS_VIEW: 'progress_view',
  SKILL_DETAILS: 'skill_details',

  // Просмотр меню и расписания
  MENU_VIEW: 'menu_view',
  SCHEDULE_VIEW: 'schedule_view',
  CALENDAR_VIEW: 'calendar_view',

  // Ошибки
  ERROR: 'error',
};
