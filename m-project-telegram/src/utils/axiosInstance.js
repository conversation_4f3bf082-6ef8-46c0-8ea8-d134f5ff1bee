import axios from "axios";

// Определяем базовый URL для API
const getBaseUrl = () => {
  // Для production окружения используем относительный URL
  if (process.env.NODE_ENV === 'production') {
    // Используем относительный URL для избежания проблем с Mixed Content
    return '/api';
  }

  // Для других окружений используем переменные окружения
  // Используем реальный URL сервера вместо localhost
  // Избегаем проверки window для предотвращения ошибок гидратации
  return process.env.NEXT_PUBLIC_API_URL;
};

const api = axios.create({
  baseURL: getBaseUrl(),
  headers: {
    'Content-Type': 'application/json',
  },
  withCredentials: true
});

api.interceptors.request.use(
  (config) => {
    console.log('Request:', config.url, config.method);

    // Для Telegram Mini App мы можем получать данные из Telegram.WebApp
    if (typeof window !== 'undefined' && window.Telegram?.WebApp) {
      try {
        // Явно вызываем ready() перед каждым запросом для обновления данных
        window.Telegram.WebApp.ready();

        const initData = window.Telegram.WebApp.initData;
        console.log('Using Telegram initData:', initData ? 'present' : 'missing');

        if (initData) {
          // Добавляем initData в заголовки для аутентификации через Telegram
          config.headers['X-Telegram-Init-Data'] = initData;
        } else {
          console.warn('Telegram initData is empty. Using fallback authentication.');
          // Можно добавить тестовые данные для разработки
          if (process.env.NODE_ENV !== 'production') {
            config.headers['X-Telegram-Init-Data'] = 'mock_init_data_for_development';
          }
        }
      } catch (error) {
        console.error('Error accessing Telegram WebApp:', error);
      }
    } else {
      console.log('Telegram WebApp not available, using standard authentication');
    }

    // Для демонстрационных целей в production окружении добавляем тестовый заголовок
    if (process.env.NODE_ENV === 'production' || (typeof window !== 'undefined' && !window.Telegram?.WebApp)) {
      // Добавляем статичный токен авторизации для демонстрации
      config.headers['Authorization'] = 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************.CM-CrV3HLJV2l0MAHe36tKuudErpNUtfIwxAJTyC3oE';
    }

    // Также можно использовать стандартную JWT-аутентификацию
    if (typeof window !== 'undefined') {
      try {
        const accessToken = localStorage.getItem("accessToken");
        if (accessToken) {
          config.headers.Authorization = `Bearer ${accessToken}`;
        }
      } catch (error) {
        console.error('Error accessing localStorage:', error);
      }
    }

    return config;
  },
  (error) => {
    console.error('Request Error:', error);
    return Promise.reject(error);
  }
);

// Перехватываем ответы и обрабатываем ошибки
api.interceptors.response.use(
  (response) => response,
  (error) => {
    console.error('Response Error:', error.message);

    if (error.response) {
      // Обработка ошибок от сервера
      console.error('Server Error:', error.response.status, error.response.data);

      // Можно добавить специфическую обработку для разных статусов
      switch (error.response.status) {
        case 401:
          console.error("Unauthorized - необходима авторизация");
          break;
        case 403:
          console.error("Forbidden - доступ запрещен");
          break;
        case 404:
          console.error("Not Found - ресурс не найден");
          break;
        case 500:
          console.error("Server Error - ошибка сервера");
          break;
      }
    } else if (error.request) {
      // Запрос был сделан, но ответ не получен
      console.error('No response received:', error.request);

      // Проверяем, если это ошибка подключения
      if (error.message === 'Network Error') {
        console.warn('Network error detected. Using mock data if available.');

        // Если включены мок-данные, можно вернуть заглушку
        if (process.env.NEXT_PUBLIC_USE_MOCK_DATA === 'true') {
          // Проверяем URL запроса и возвращаем соответствующие мок-данные
          const url = error.config.url;

          if (url.includes('/children/my-children')) {
            // Заглушка для списка детей
            return Promise.resolve({
              data: [
                {
                  id: '05721765-7a52-4972-b109-156a939bbdc1',
                  name: 'Иван',
                  surname: 'Иванов',
                  schoolId: '67e43b51-01dc-4dbb-abb0-142c52921dc6',
                  school: { name: 'Школа №1' }
                }
              ]
            });
          } else if (url.includes('/menu/')) {
            // Заглушка для меню
            return Promise.resolve({
              data: {
                id: '123456',
                fileUrl: '/files/menu.pdf',
                updatedAt: '2023-11-01T12:00:00Z' // Используем статичную дату вместо динамической
              }
            });
          } else if (url.includes('/schedule/child/')) {
            // Заглушка для расписания по ID ребенка
            return Promise.resolve({
              data: {
                id: '654321',
                fileUrl: '/files/schedule.pdf',
                ageGroupId: 'children_3_6', // Добавляем тип группы
                updatedAt: '2023-11-01T12:00:00Z' // Используем статичную дату вместо динамической
              }
            });
          } else if (url.includes('/schedule/')) {
            // Заглушка для расписания по schoolId
            return Promise.resolve({
              data: {
                id: '654321',
                fileUrl: '/files/schedule.pdf',
                updatedAt: '2023-11-01T12:00:00Z' // Используем статичную дату вместо динамической
              }
            });
          } else if (url.includes('/skills-category')) {
            // Заглушка для категорий навыков
            return Promise.resolve({
              data: [
                { id: '1', name: 'Математика', order: 1 },
                { id: '2', name: 'Чтение', order: 2 },
                { id: '3', name: 'Письмо', order: 3 }
              ]
            });
          } else if (url.includes('/children/') && url.includes('/details')) {
            // Заглушка для деталей ребенка
            return Promise.resolve({
              data: {
                id: '05721765-7a52-4972-b109-156a939bbdc1',
                name: 'Иван',
                surname: 'Иванов',
                birthDate: '2015-05-15',
                schoolId: '67e43b51-01dc-4dbb-abb0-142c52921dc6',
                school: { name: 'Школа №1' },
                skills: []
              }
            });
          }

          // Добавьте другие заглушки по необходимости
        }
      }
    }

    return Promise.reject(error);
  }
);

export default api;
