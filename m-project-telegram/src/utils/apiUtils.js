/**
 * Утилиты для работы с API и URL в API-маршрутах
 */

/**
 * Получает базовый URL API в зависимости от окружения
 * @returns {string} Базовый URL API
 */
export function getApiBaseUrl() {
  return process.env.NODE_ENV === 'production'
    ? (process.env.NEXT_PUBLIC_API_URL || 'https://mymontessory.ru/api')
    : 'http://localhost:3002/api';
}

/**
 * Получает базовый URL для файлов в зависимости от окружения
 * @returns {string} Базовый URL для файлов
 */
export function getFilesBaseUrl() {
  return process.env.NODE_ENV === 'production'
    ? (process.env.NEXT_PUBLIC_API_URL?.replace('/api', '') || 'https://mymontessory.ru')
    : 'http://localhost:3002';
}

/**
 * Преобразует относительный URL файла в абсолютный
 * @param {object} data - Объект с данными, содержащий fileUrl
 * @returns {object} - Объект с преобразованным fileUrl
 */
export function transformFileUrl(data) {
  if (data && data.fileUrl) {
    // Проверяем, начинается ли URL с '/uploads/'
    if (data.fileUrl.startsWith('/uploads/')) {
      // Заменяем относительный URL на абсолютный
      const baseUrl = getFilesBaseUrl();
      data.fileUrl = `${baseUrl}${data.fileUrl}`;
    }
  }
  return data;
}

/**
 * Обрабатывает ответ от API и преобразует URL файлов
 * @param {Response} response - Ответ от fetch
 * @returns {Promise<object>} - Преобразованные данные
 */
export async function processApiResponse(response) {
  const data = await response.json();
  return transformFileUrl(data);
}
