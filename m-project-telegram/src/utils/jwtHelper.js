/**
 * Утилиты для работы с JWT токеном
 */

/**
 * Декодирует JWT токен и возвращает его содержимое
 * @param {string} token - JWT токен
 * @returns {Object|null} Содержимое токена или null в случае ошибки
 */
export const decodeJwt = (token) => {
  if (!token) return null;
  
  try {
    // JWT токен состоит из трех частей, разделенных точками
    const base64Url = token.split('.')[1];
    const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
    const jsonPayload = decodeURIComponent(
      atob(base64)
        .split('')
        .map(c => '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2))
        .join('')
    );

    return JSON.parse(jsonPayload);
  } catch (error) {
    console.error('Ошибка при декодировании JWT токена:', error);
    return null;
  }
};

/**
 * Получает ID пользователя из JWT токена
 * @returns {string|null} ID пользователя или null в случае ошибки
 */
export const getUserIdFromToken = () => {
  if (typeof window === 'undefined') return null;
  
  try {
    const token = localStorage.getItem('accessToken');
    if (!token) return null;
    
    const decoded = decodeJwt(token);
    return decoded?.sub || null; // sub содержит ID пользователя
  } catch (error) {
    console.error('Ошибка при получении ID пользователя из токена:', error);
    return null;
  }
};

/**
 * Получает роль пользователя из JWT токена
 * @returns {string|null} Роль пользователя или null в случае ошибки
 */
export const getUserRoleFromToken = () => {
  if (typeof window === 'undefined') return null;
  
  try {
    const token = localStorage.getItem('accessToken');
    if (!token) return null;
    
    const decoded = decodeJwt(token);
    return decoded?.role || null;
  } catch (error) {
    console.error('Ошибка при получении роли пользователя из токена:', error);
    return null;
  }
};

/**
 * Получает Telegram ID пользователя из JWT токена
 * @returns {string|null} Telegram ID пользователя или null в случае ошибки
 */
export const getTelegramIdFromToken = () => {
  if (typeof window === 'undefined') return null;
  
  try {
    const token = localStorage.getItem('accessToken');
    if (!token) return null;
    
    const decoded = decodeJwt(token);
    return decoded?.telegramId || null;
  } catch (error) {
    console.error('Ошибка при получении Telegram ID пользователя из токена:', error);
    return null;
  }
};
