'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Box, CircularProgress, ThemeProvider, createTheme } from '@mui/material';
import { CacheProvider } from '@emotion/react';
import createCache from '@emotion/cache';
import ConnectChildScreen from './ConnectChildScreen';

function createEmotionCache() {
  return createCache({ key: 'css', prepend: true });
}

// Создаем тему для приложения
const theme = createTheme({
  palette: {
    primary: {
      main: '#6B8E23', // Оливковый
    },
    secondary: {
      main: '#8FBC8F', // Светло-зеленый
    },
    background: {
      default: '#FAFAF0', // Светлый бежевый
    },
    text: {
      primary: '#333333', // Темно-серый
    },
  },
  typography: {
    fontFamily: '<PERSON><PERSON>, <PERSON><PERSON>, sans-serif',
  },
  components: {
    MuiButton: {
      styleOverrides: {
        root: {
          textTransform: 'none',
        },
      },
    },
  },
});

export default function Home() {
  const router = useRouter();
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [loading, setLoading] = useState(true);
  const [screenHeight, setScreenHeight] = useState('100vh');
  const clientSideEmotionCache = createEmotionCache();

  // Инициализация Telegram Web App и проверка авторизации
  useEffect(() => {
    if (typeof window !== 'undefined') {
      console.log('Telegram object available:', !!window.Telegram);

      // Для режима разработки создаем мок-объект Telegram
      if (process.env.NODE_ENV !== 'production' && !window.Telegram) {
        console.log('Creating mock Telegram WebApp for development');
        window.Telegram = {
          WebApp: {
            ready: () => console.log('Mock WebApp ready called'),
            initData: 'mock_init_data_for_development',
            viewportHeight: window.innerHeight,
            onEvent: () => {},
            offEvent: () => {},
            MainButton: {
              setText: () => {},
              show: () => {},
              hide: () => {},
              onClick: () => {},
              offClick: () => {},
            },
          },
        };
      }

      // Проверяем авторизацию с отложенным выполнением
      setTimeout(() => {
        const token = localStorage.getItem('accessToken');
        if (token) {
          setIsAuthenticated(true);
          // Перенаправляем на главную страницу
          router.push('/home');
        } else {
          setLoading(false);
        }
      }, 0);

      // Обработчик изменения высоты вьюпорта
      const handleViewportChange = () => {
        if (window.Telegram?.WebApp) {
          setScreenHeight(`${window.Telegram.WebApp.viewportHeight}px`);
        }
      };

      if (window.Telegram?.WebApp) {
        window.Telegram.WebApp.ready();
        window.Telegram.WebApp.onEvent('viewportChanged', handleViewportChange);
        handleViewportChange();
      }

      // Очистка при размонтировании
      return () => {
        if (window.Telegram?.WebApp) {
          window.Telegram.WebApp.offEvent('viewportChanged');
        }
      };
    }
  }, [router]);

  // Обработчик успешной авторизации
  const handleAuthSuccess = (data) => {
    setIsAuthenticated(true);
    console.log('Authentication successful:', data);

    // Перенаправляем на главную страницу
    router.push('/home');
  };

  if (loading && isAuthenticated) {
    return (
      <CacheProvider value={clientSideEmotionCache}>
        <ThemeProvider theme={theme}>
          <Box sx={{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            height: '100vh',
            width: '100%'
          }}>
            <CircularProgress />
          </Box>
        </ThemeProvider>
      </CacheProvider>
    );
  }

  return (
    <CacheProvider value={clientSideEmotionCache}>
      <ThemeProvider theme={theme}>
        <Box sx={{
          display: 'flex',
          flexDirection: 'column',
          height: screenHeight,
          bgcolor: 'background.default',
          overflow: 'hidden',
        }}
        className="telegram-theme"
        >
          {!isAuthenticated && (
            <ConnectChildScreen onSuccess={handleAuthSuccess} />
          )}
        </Box>
      </ThemeProvider>
    </CacheProvider>
  );
}
