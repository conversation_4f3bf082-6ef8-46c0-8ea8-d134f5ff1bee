.progress-table-container {
  width: 100%;
  overflow-x: auto;
}

.progress-table {
  min-width: 100%;
}

.skill-row {
  background-color: #f0f0f0;
  cursor: pointer;
}

.exercise-row td:first-child {
  padding-left: 32px;
}

.status-icon {
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 50%;
  width: 30px;
  height: 30px;
  font-weight: bold;
  color: white;
  margin: 0 auto;
}

.legend-container {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 16px;
}

.legend-item {
  display: flex;
  align-items: center;
  margin-right: 8px;
}

.legend-icon {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 4px;
}

.legend-text {
  font-size: 0.75rem;
}

@media (max-width: 600px) {
  .exercise-row td:first-child {
    padding-left: 16px;
  }
  
  .status-icon {
    width: 24px;
    height: 24px;
    font-size: 0.7rem;
  }
  
  .legend-icon {
    width: 16px;
    height: 16px;
  }
  
  .legend-text {
    font-size: 0.7rem;
  }
}
