'use client';

import { useEffect, useRef, useState } from "react";
import {
  Box,
  CircularProgress,
  Typography,
  IconButton,
  Stack,
  Button,
} from "@mui/material";
import NavigateNextIcon from "@mui/icons-material/NavigateNext";
import NavigateBeforeIcon from "@mui/icons-material/NavigateBefore";
import OpenInNewIcon from "@mui/icons-material/OpenInNew";

// Импортируем PDF.js
import * as pdfjsLib from "pdfjs-dist";
import { sendMetrikaEvent, MetrikaEvents } from '@/utils/metrikaEvents';

/**
 * Компонент для отображения PDF с использованием PDF.js
 * Поддерживает навигацию по страницам и масштабирование
 */
const PdfJsViewer = ({
  onError,
  onLoaded,
  documentUrl = "/files/dummy.pdf",
}) => {
  const [loading, setLoading] = useState(true);
  const [loadingType, setLoadingType] = useState('document'); // Добавляем состояние для отслеживания типа загрузки
  const [error, setError] = useState(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [pdfDocument, setPdfDocument] = useState(null);

  const canvasRef = useRef(null);
  const containerRef = useRef(null);

  // Устанавливаем настройки для PDF.js
  useEffect(() => {
    if (typeof window !== "undefined") {
      try {
        // Добавляем полифилл для URL.parse перед загрузкой PDF.js
        if (typeof URL !== 'undefined' && !URL.parse) {
          URL.parse = function(url) {
            try {
              return new URL(url);
            } catch (e) {
              // Fallback для относительных URL
              try {
                return new URL(url, window.location.href);
              } catch (e2) {
                console.warn('Failed to parse URL:', url);
                return null;
              }
            }
          };
          console.log("URL.parse polyfill added");
        }

        // Используем локальный worker файл
        const workerSrc = '/pdfjs/pdf.worker.min.js';
        pdfjsLib.GlobalWorkerOptions.workerSrc = workerSrc;
        console.log("PDF.js worker path set to:", workerSrc);
      } catch (err) {
        console.error("Error setting PDF.js worker:", err);
      }
    }
  }, []);

  // Загружаем PDF документ
  useEffect(() => {
    let isMounted = true;
    let loadingTask = null;

    setLoading(true);
    setLoadingType('document'); // Устанавливаем тип загрузки - документ
    setError(null); // Сбрасываем ошибку при новой загрузке

    console.log("Loading PDF document:", documentUrl);

    // Проверяем URL файла
    if (!documentUrl) {
      console.error("Не указан URL файла");
      setError("Не указан URL файла");
      setLoading(false);
      return;
    }

    if (documentUrl === "/files/dummy.pdf") {
      console.warn("Используется URL файла по умолчанию:", documentUrl);
    }

    const loadPdf = async () => {
      try {
        // Сначала проверяем доступность файла
        const response = await fetch(documentUrl, { method: "HEAD" });

        if (!isMounted) return;

        if (!response.ok) {
          console.error(
            `Файл недоступен: ${documentUrl}, статус: ${response.status}`
          );
          throw new Error(`Файл недоступен. Статус: ${response.status}`);
        }

        // Загружаем PDF документ с минимальными настройками
        loadingTask = pdfjsLib.getDocument(documentUrl);

        const pdf = await loadingTask.promise;

        if (!isMounted) return;

        console.log("PDF loaded successfully. Pages:", pdf.numPages);
        setPdfDocument(pdf);
        setTotalPages(pdf.numPages);
        setCurrentPage(1);
        setLoading(false);

        if (onLoaded) onLoaded();
      } catch (err) {
        if (!isMounted) return;

        console.error("Error loading PDF:", err);
        setError(`Не удалось загрузить PDF документ: ${err.message || err}`);
        setLoading(false);

        if (onError)
          onError(`Не удалось загрузить PDF документ: ${err.message || err}`);
      }
    };

    loadPdf();

    return () => {
      isMounted = false;
      // Отменяем загрузку при размонтировании компонента
      if (loadingTask) {
        loadingTask.destroy();
      }
    };
  }, [documentUrl, onError, onLoaded]);

  // Рендерим текущую страницу PDF
  useEffect(() => {
    if (!pdfDocument || !canvasRef.current) return;

    console.log(`Rendering page ${currentPage} of ${totalPages}`);

    let isMounted = true;
    setLoading(true); // Показываем индикатор загрузки при смене страницы
    setLoadingType('page'); // Устанавливаем тип загрузки - страница

    const renderPage = async () => {
      try {
        // Получаем страницу
        const page = await pdfDocument.getPage(currentPage);

        // Проверяем, что компонент все еще монтирован
        if (!isMounted || !canvasRef.current) return;

        // Получаем контекст canvas
        const canvas = canvasRef.current;
        const context = canvas.getContext("2d");

        // Очищаем canvas перед рендерингом
        context.clearRect(0, 0, canvas.width, canvas.height);

        // Получаем размеры контейнера
        const container = canvas.parentElement;
        const containerWidth = container.clientWidth;
        // Высота контейнера (не используется, но может пригодиться в будущем)
        // const containerHeight = container.clientHeight;

        // Получаем размеры страницы
        const originalViewport = page.getViewport({ scale: 1.0 });

        // Вычисляем масштаб для полного заполнения контейнера
        const widthScale = containerWidth / originalViewport.width;
        // Вычисляем масштаб по высоте (не используется, но может пригодиться в будущем)
        // const heightScale = containerHeight / originalViewport.height;

        // Используем масштаб по ширине, чтобы заполнить весь экран по ширине
        const scale = widthScale; // Используем только масштаб по ширине

        // Создаем новый viewport с вычисленным масштабом
        const viewport = page.getViewport({ scale });

        // Устанавливаем размеры canvas с учетом плотности пикселей для лучшего качества
        const pixelRatio = window.devicePixelRatio || 1;
        canvas.height = viewport.height * pixelRatio;
        canvas.width = viewport.width * pixelRatio;
        canvas.style.height = `${viewport.height}px`;
        canvas.style.width = `${viewport.width}px`;

        // Рендерим страницу с учетом плотности пикселей
        context.setTransform(pixelRatio, 0, 0, pixelRatio, 0, 0);

        const renderContext = {
          canvasContext: context,
          viewport: viewport,
        };

        await page.render(renderContext).promise;
        if (!isMounted) return;

        console.log(`Page ${currentPage} rendered successfully`);
        setLoading(false);
      } catch (err) {
        if (!isMounted) return;

        console.error("Error rendering page:", err);
        setError(
          `Ошибка при отображении страницы ${currentPage}: ${
            err.message || err
          }`
        );
        setLoading(false);

        if (onError) onError(`Ошибка при отображении страницы ${currentPage}`);
      }
    };

    renderPage();

    return () => {
      isMounted = false;
    };
  }, [pdfDocument, currentPage, totalPages, onError]);

  // Функции для навигации между страницами
  const goToNextPage = () => {
    if (currentPage < totalPages) {
      setCurrentPage((prev) => prev + 1);

      // Отправляем событие о переходе на следующую страницу
      sendMetrikaEvent(MetrikaEvents.PAGE_VIEW, {
        document_url: documentUrl,
        page: currentPage + 1,
        total_pages: totalPages,
        direction: 'next'
      });
    }
  };

  const goToPrevPage = () => {
    if (currentPage > 1) {
      setCurrentPage((prev) => prev - 1);

      // Отправляем событие о переходе на предыдущую страницу
      sendMetrikaEvent(MetrikaEvents.PAGE_VIEW, {
        document_url: documentUrl,
        page: currentPage - 1,
        total_pages: totalPages,
        direction: 'prev'
      });
    }
  };

  // Функция для открытия PDF в новом окне
  const handleOpenInNewWindow = () => {
    // Отправляем событие об открытии документа в браузере
    sendMetrikaEvent(MetrikaEvents.DOCUMENT_DOWNLOAD, {
      document_url: documentUrl,
      page: currentPage,
      total_pages: totalPages
    });

    // Проверяем, если мы в Telegram Mini App
    if (window.Telegram && window.Telegram.WebApp) {
      // В Telegram Mini App используем встроенный браузер Telegram
      window.Telegram.WebApp.openLink(documentUrl);
    } else {
      // В обычном браузере открываем в новом окне
      // Проверяем доступность файла перед открытием
      fetch(documentUrl, { method: "HEAD" })
        .then((response) => {
          if (response.ok) {
            window.open(documentUrl, "_blank");
          } else {
            console.error(
              `Файл недоступен: ${documentUrl}, статус: ${response.status}`
            );
            setError(`Файл недоступен. Проверьте подключение к серверу.`);
          }
        })
        .catch((err) => {
          console.error(`Ошибка при проверке файла: ${err.message}`);
          window.open(documentUrl, "_blank"); // Все равно пытаемся открыть
        });
    }
  };

  return (
    <Box
      ref={containerRef}
      sx={{
        display: "flex",
        flexDirection: "column",
        height: "100%",
        width: "100%",
        position: "relative",
        overflow: "hidden",
        bgcolor: "#f5f5f5",
      }}
    >
      {/* Кнопка открыть в браузере */}
      <IconButton
        onClick={handleOpenInNewWindow}
        sx={{
          position: "absolute",
          top: 8,
          right: 8,
          bgcolor: "rgba(255,255,255,0.7)", // Светлый прозрачный фон
          color: "black", // Черный цвет иконки
          boxShadow: "0 2px 5px rgba(0,0,0,0.15)", // Более мягкая тень
          "&:hover": {
            bgcolor: "rgba(255,255,255,0.9)", // Более непрозрачный при наведении
            boxShadow: "0 2px 8px rgba(0,0,0,0.2)",
          },
          zIndex: 999,
        }}
        size="small"
        aria-label="Открыть в браузере"
      >
        <OpenInNewIcon fontSize="small" />
      </IconButton>

      {loading && (
        <Box
          sx={{
            position: "absolute",
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            display: "flex",
            flexDirection: "column",
            justifyContent: "center",
            alignItems: "center",
            zIndex: 5,
            bgcolor: loadingType === 'document' ? "rgba(255, 255, 255, 0.7)" : "rgba(255, 255, 255, 0.5)", // Делаем фон менее непрозрачным при загрузке страницы
          }}
        >
          <CircularProgress
            size={loadingType === 'document' ? 36 : 30}
            thickness={loadingType === 'document' ? 3 : 2.5}
          />
          <Typography sx={{ mt: 1 }}>
            {loadingType === 'document' ? 'Загрузка документа...' : 'Загрузка страницы...'}
          </Typography>
        </Box>
      )}

      {error ? (
        <Box
          sx={{
            display: "flex",
            flexDirection: "column",
            justifyContent: "center",
            alignItems: "center",
            height: "100%",
            padding: 2,
            textAlign: "center",
          }}
        >
          <Typography variant="h6" color="error" gutterBottom>
            Ошибка при загрузке документа
          </Typography>
          <Typography color="error" sx={{ mb: 2 }}>
            {error}
          </Typography>
          <Button
            variant="contained"
            color="primary"
            onClick={() => {
              setError(null);
              setLoading(true);
              // Повторная попытка загрузки документа
              setTimeout(() => {
                window.location.reload();
              }, 500);
            }}
          >
            Повторить попытку
          </Button>
        </Box>
      ) : (
        <Box
          sx={{
            flex: 1,
            display: "flex",
            flexDirection: "column",
            justifyContent: "center",
            alignItems: "center",
            overflow: "auto", // Оставляем auto для общего контейнера
            padding: 2,
            paddingBottom: "70px", // Добавляем отступ снизу, чтобы контент не перекрывался нижним меню
          }}
        >
          {/* Canvas для отображения PDF */}
          <Box
            sx={{
              flex: 1,
              display: "flex",
              flexDirection: "column",
              justifyContent: "center",
              alignItems: "center",
              overflowY: "auto", // Изменяем на auto, чтобы был вертикальный скролл при необходимости
              width: "100%",
              height: "100%",
              position: "relative",
              padding: 0, // Без отступов
            }}
          >
            <Box
              sx={{
                flex: 1,
                display: "flex",
                justifyContent: "center",
                alignItems: "center",
                overflowY: "auto", // Изменяем на auto, чтобы был вертикальный скролл при необходимости
                width: "100%",
                height: "100%",
                padding: 0,
              }}
            >
              <canvas
                ref={canvasRef}
                style={{
                  width: "100%",
                  height: "100%",
                  objectFit: "contain",
                  display: "block",
                  backgroundColor: "#fff", // Белый фон для лучшего контраста
                  imageRendering: "high-quality", // Высокое качество рендеринга
                }}
              />
            </Box>
          </Box>

          {/* Панель управления */}
          {totalPages > 1 && (
            <Box
              sx={{
                position: 'fixed',
                bottom: 70, // Увеличиваем отступ снизу, чтобы не перекрывать нижнее меню
                left: '50%',
                transform: 'translateX(-50%)',
                display: 'flex',
                justifyContent: 'center',
                width: 'auto',
                zIndex: 1000,
              }}
            >
              <Stack
                direction="row"
                spacing={1}
                sx={{
                  p: 1,
                  bgcolor: "rgba(255,255,255,0.7)", // Делаем фон светлым и прозрачным
                  borderRadius: 20, // Более закругленные углы
                  boxShadow: '0 2px 8px rgba(0,0,0,0.15)', // Более мягкая тень для светлого фона
                  height: 44,
                  alignItems: 'center',
                  minWidth: 140, // Минимальная ширина для лучшей заметности
                }}
              >
                {/* Навигация по страницам */}
                <IconButton
                  onClick={goToPrevPage}
                  disabled={currentPage <= 1}
                  size="medium"
                  sx={{
                    color: "black", // Меняем цвет иконок на черный
                    "&:hover": { bgcolor: "rgba(0,0,0,0.1)" }, // Изменяем цвет при наведении
                    "&.Mui-disabled": { opacity: 0.3, color: "rgba(0,0,0,0.3)" }, // Изменяем цвет неактивных иконок
                  }}
                >
                  <NavigateBeforeIcon fontSize="medium" />
                </IconButton>

                <Typography
                  sx={{
                    display: "flex",
                    alignItems: "center",
                    minWidth: "60px",
                    justifyContent: "center",
                    fontWeight: "bold",
                    color: "black", // Меняем цвет текста на черный
                    px: 1,
                    fontSize: "1rem", // Увеличиваем размер текста
                  }}
                >
                  {currentPage} / {totalPages}
                </Typography>

                <IconButton
                  onClick={goToNextPage}
                  disabled={currentPage >= totalPages}
                  size="medium"
                  sx={{
                    color: "black", // Меняем цвет иконок на черный
                    "&:hover": { bgcolor: "rgba(0,0,0,0.1)" }, // Изменяем цвет при наведении
                    "&.Mui-disabled": { opacity: 0.3, color: "rgba(0,0,0,0.3)" }, // Изменяем цвет неактивных иконок
                  }}
                >
                  <NavigateNextIcon fontSize="medium" />
                </IconButton>
              </Stack>
            </Box>
          )}
        </Box>
      )}
    </Box>
  );
};

export default PdfJsViewer;
