'use client';

import { useState, useEffect } from 'react';
import { Box, TextField, Button, Typography, CircularProgress } from '@mui/material';
import api from '@/utils/axiosInstance';
import UserProfileForm from './UserProfileForm';
import { sendMetrikaEvent, MetrikaEvents } from '@/utils/metrikaEvents';

const ConnectChildScreen = ({ onSuccess }) => {
  const [code, setCode] = useState('');
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);
  const [showProfileForm, setShowProfileForm] = useState(false);
  const [accessCode, setAccessCode] = useState('');
  const [telegramData, setTelegramData] = useState(null);

  // Отправляем событие о просмотре страницы авторизации
  useEffect(() => {
    sendMetrikaEvent(MetrikaEvents.AUTH_START);
  }, []);

  const handleConnect = async () => {
    if (!code || code.length < 6) {
      setError('Пожалуйста, введите корректный код доступа');
      return;
    }

    setLoading(true);
    setError('');

    // Отправляем событие о попытке авторизации
    sendMetrikaEvent(MetrikaEvents.AUTH_START, { code_length: code.length });

    try {
      // Сначала проверяем код доступа
      const response = await api.post('/auth/telegram/validate-code', {
        accessCode: code
      });

      // Если код валидный, проверяем существование пользователя
      setAccessCode(code);
      setTelegramData(response.data.telegramData);

      // Проверяем, существует ли пользователь с таким Telegram ID
      try {
        const userCheckResponse = await api.post('/auth/telegram/check-user', {
          telegramId: response.data.telegramData.id.toString(),
          accessCode: code
        });

        // Если пользователь существует и уже привязан к ребенку, пропускаем форму профиля
        if (userCheckResponse.data.exists && userCheckResponse.data.isConnectedToChild) {
          console.log('Пользователь уже существует и привязан к ребенку, пропускаем форму');

          // Автоматически подключаем пользователя
          const connectResponse = await api.post('/auth/telegram/connect-child', {
            accessCode: code,
            skipProfileUpdate: true // Флаг для пропуска обновления профиля
          });

          // Сохраняем токен и ID ребенка в localStorage
          try {
            localStorage.setItem('accessToken', connectResponse.data.accessToken);

            if (connectResponse.data.child && connectResponse.data.child.id) {
              localStorage.setItem('childId', connectResponse.data.child.id);
              console.log('Сохранен ID ребенка:', connectResponse.data.child.id);
            }
          } catch (storageErr) {
            console.error('Ошибка при сохранении данных в localStorage:', storageErr);
          }

          // Отправляем событие об успешной авторизации
          sendMetrikaEvent(MetrikaEvents.AUTH_SUCCESS, {
            method: 'auto_connect',
            has_child_id: !!connectResponse.data.child?.id
          });

          // Вызываем колбэк успешной авторизации
          if (onSuccess) {
            onSuccess(connectResponse.data);
          }
          return;
        }

        // Если пользователь не существует или не привязан к ребенку, показываем форму профиля
        setShowProfileForm(true);
      } catch (checkErr) {
        console.error('Ошибка при проверке пользователя:', checkErr);
        // В случае ошибки при проверке, показываем форму профиля
        setShowProfileForm(true);
      }
    } catch (err) {
      console.error('Ошибка при подключении:', err);
      const errorMessage = err.response?.data?.message || 'Неверный код доступа или произошла ошибка при подключении';
      setError(errorMessage);

      // Отправляем событие об ошибке авторизации
      sendMetrikaEvent(MetrikaEvents.AUTH_ERROR, {
        error_message: errorMessage,
        error_type: err.response?.status ? `http_${err.response.status}` : 'network_error'
      });
    } finally {
      setLoading(false);
    }
  };

  const handleProfileSubmit = async (profileData) => {
    try {
      // Отправляем код доступа вместе с данными профиля
      const response = await api.post('/auth/telegram/connect-child', {
        accessCode: accessCode,
        ...profileData
      });

      // Сохраняем токен и ID ребенка в localStorage
      localStorage.setItem('accessToken', response.data.accessToken);

      // Сохраняем ID ребенка, если он есть в ответе
      if (response.data.child && response.data.child.id) {
        localStorage.setItem('childId', response.data.child.id);
        console.log('Сохранен ID ребенка:', response.data.child.id);
      }

      // Отправляем событие об успешной авторизации
      sendMetrikaEvent(MetrikaEvents.AUTH_SUCCESS, {
        method: 'profile_form',
        has_child_id: !!response.data.child?.id
      });

      // Вызываем колбэк успешной авторизации
      if (onSuccess) {
        onSuccess(response.data);
      }
    } catch (err) {
      console.error('Ошибка при сохранении профиля:', err);
      const errorMessage = err.response?.data?.message || 'Произошла ошибка при сохранении данных.';
      setError(errorMessage);
      setShowProfileForm(false); // Возвращаемся к вводу кода

      // Отправляем событие об ошибке авторизации
      sendMetrikaEvent(MetrikaEvents.AUTH_ERROR, {
        error_message: errorMessage,
        error_type: 'profile_save_error',
        http_status: err.response?.status
      });
    }
  };

  if (showProfileForm) {
    return (
      <UserProfileForm
        onSubmit={handleProfileSubmit}
        initialData={{
          firstName: telegramData?.first_name || '',
          lastName: telegramData?.last_name || '',
        }}
      />
    );
  }

  return (
    <Box sx={{
      p: 3,
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center',
      height: '100%',
      justifyContent: 'center',
      maxWidth: '400px',
      margin: '0 auto'
    }}>
      <Typography
        variant="h5"
        gutterBottom
        sx={{
          fontFamily: 'var(--font-dancing-script)',
          fontSize: '2rem',
          color: '#6B8E23',
          mb: 2
        }}
      >
        MyMontessori
      </Typography>
      <Typography sx={{ mb: 3, textAlign: 'center', color: '#333' }}>
        Пожалуйста, введите код доступа, который вы получили от учителя
      </Typography>
      <TextField
        label="Код доступа"
        value={code}
        onChange={(e) => setCode(e.target.value)}
        error={!!error}
        helperText={error}
        sx={{ mb: 2, width: '100%' }}
        inputProps={{ maxLength: 6 }}
        autoFocus
      />
      <Button
        variant="contained"
        onClick={handleConnect}
        disabled={loading || code.length < 6}
        fullWidth
        sx={{
          bgcolor: '#6B8E23',
          '&:hover': {
            bgcolor: '#556B2F',
          }
        }}
      >
        {loading ? <CircularProgress size={24} /> : 'Подключить'}
      </Button>
    </Box>
  );
};

export default ConnectChildScreen;
