'use client';

import { useState, useEffect } from 'react';
import { Box, Typography } from '@mui/material';
// Удален неиспользуемый импорт OpenInNewIcon
import dynamic from 'next/dynamic';
import { sendMetrikaEvent, MetrikaEvents } from '@/utils/metrikaEvents';

// Используем dynamic import для PdfJsViewer
const PdfJsViewer = dynamic(
  () => import('./PdfJsViewer'),
  {
    ssr: false,
    // Удален лоадер, так как он уже есть в PdfJsViewer
  }
);

/**
 * Универсальный компонент для просмотра PDF-документов
 * Автоматически выбирает наиболее подходящий способ отображения PDF
 *
 * @param {Object} props - Свойства компонента
 * @param {string} props.documentUrl - URL документа для просмотра
 * @param {Function} props.onError - Функция обратного вызова при ошибке
 * @param {Function} props.onLoaded - Функция обратного вызова при загрузке документа

 */
const UniversalPdfViewer = ({ documentUrl = '/files/dummy.pdf', onError, onLoaded }) => {
  const [error, setError] = useState(null);

  // Обработчик ошибок
  const handleError = (errorMessage) => {
    console.error('PDF viewer error:', errorMessage);
    setError(errorMessage);
    if (onError) onError(errorMessage);
  };

  // Обработчик успешной загрузки
  const handleLoaded = () => {
    console.log('PDF loaded successfully');

    // Отправляем событие о просмотре документа
    sendMetrikaEvent(MetrikaEvents.DOCUMENT_VIEW, {
      document_url: documentUrl,
      document_type: getDocumentType(documentUrl)
    });

    if (onLoaded) onLoaded();
  };

  // Определяем тип документа по URL
  const getDocumentType = (url) => {
    if (!url) return 'unknown';

    if (url.includes('menus')) return 'menu';
    if (url.includes('schedules')) return 'schedule';
    if (url.includes('calendars')) return 'calendar';

    // Проверяем локальные файлы
    if (url.includes('menu.pdf')) return 'menu';
    if (url.includes('schedule.pdf')) return 'schedule';

    return 'other';
  };

  // Проверяем URL документа
  useEffect(() => {
    if (!documentUrl) {
      handleError('URL документа не указан');
      return;
    }

    // Сбрасываем состояние при изменении URL
    setError(null);

    // Проверяем доступность файла
    fetch(documentUrl, { method: 'HEAD' })
      .then(response => {
        if (!response.ok) {
          throw new Error(`Файл недоступен. Статус: ${response.status}`);
        }
        // Файл доступен, ничего не делаем, компонент сам загрузит файл
      })
      .catch(err => {
        handleError(`Не удалось проверить доступность файла: ${err.message}`);
      });
  }, [documentUrl]);



  // Отображаем ошибку, если она есть
  if (error) {
    return (
      <Box sx={{
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'center',
        alignItems: 'center',
        height: '100%',
        p: 2,
        textAlign: 'center'
      }}>

        <Typography color="error" sx={{ mt: 2 }}>
          {error}
        </Typography>
      </Box>
    );
  }

  // Отображаем PDF-просмотрщик
  return (
    <Box sx={{
      display: 'flex',
      flexDirection: 'column',
      height: '100%',
      width: '100%',
      overflow: 'auto' // Изменяем на auto, чтобы был вертикальный скролл при необходимости
    }}>

      <Box sx={{ flex: 1, position: 'relative' }}>
        <PdfJsViewer
          documentUrl={documentUrl}
          onError={handleError}
          onLoaded={handleLoaded}
        />

        {/* Удален лоадер, так как он уже есть в PdfJsViewer */}
      </Box>
    </Box>
  );
};

export default UniversalPdfViewer;
