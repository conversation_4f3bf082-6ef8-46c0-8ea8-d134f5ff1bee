import { Geist, <PERSON><PERSON><PERSON>_Mono, Dancing_Script } from "next/font/google";
import Script from "next/script";
import "./globals.css";
import { Suspense } from "react";
import YandexMetrikaScript from "@/components/YandexMetrika/YandexMetrikaScript";
import YandexMetrika from "@/components/YandexMetrika/YandexMetrika";

// ID счетчика Яндекс Метрики
const YANDEX_METRIKA_ID = process.env.NEXT_PUBLIC_YANDEX_METRIKA_ID || "101522325";

// Проверка загрузки переменных окружения в режиме разработки
if (process.env.NODE_ENV !== 'production') {
  console.log('Яндекс Метрика ID из переменных окружения:', process.env.NEXT_PUBLIC_YANDEX_METRIKA_ID);
  console.log('Используемый Яндекс Метрика ID:', YANDEX_METRIKA_ID);
}

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

const dancingScript = Dancing_Script({
  variable: "--font-dancing-script",
  subsets: ["latin"],
  weight: ["400", "700"],
  display: "swap",
});

export const metadata = {
  title: "Монтессори Телеграм",
  description: "Телеграм приложение для родителей Монтессори школы",
};

export default function RootLayout({ children }) {
  return (
    <html lang="ru">
      <head>
        <Script src="https://telegram.org/js/telegram-web-app.js" strategy="beforeInteractive" crossOrigin="anonymous" />
        <Script src="/pdfjs/url-polyfill.js" strategy="beforeInteractive" />
      </head>
      <body
        className={`${geistSans.variable} ${geistMono.variable} ${dancingScript.variable} antialiased`}
      >
        {/* Инициализация Яндекс Метрики */}
        <YandexMetrikaScript metrikaId={YANDEX_METRIKA_ID} />

        {/* Компонент для отслеживания переходов между страницами */}
        <Suspense fallback={<></>}>
          <YandexMetrika metrikaId={YANDEX_METRIKA_ID} />
        </Suspense>

        {children}
      </body>
    </html>
  );
}
