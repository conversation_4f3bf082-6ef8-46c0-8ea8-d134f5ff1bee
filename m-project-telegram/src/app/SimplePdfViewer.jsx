'use client';

import { useEffect, useRef, useState } from 'react';
import { Box, CircularProgress, Typography, IconButton, Tooltip } from '@mui/material';
import OpenInNewIcon from '@mui/icons-material/OpenInNew';

const SimplePdfViewer = ({ onError, onLoaded, documentUrl = '/files/dummy.pdf' }) => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const iframeRef = useRef(null);

  useEffect(() => {
    // Используем отложенное выполнение для предотвращения ошибок гидратации
    const initViewer = () => {
      let isMounted = true;
      console.log('SimplePdfViewer mounted, loading document:', documentUrl);

      if (onLoaded) {
        setTimeout(() => {
          if (isMounted) onLoaded();
        }, 300);
      }

      const checkIframeLoaded = () => {
        if (!isMounted) return;

        try {
          if (iframeRef.current) {
            console.log('PDF iframe loaded');
            setLoading(false);
          }
        } catch (err) {
          console.error('Error checking iframe loaded:', err);
          if (isMounted) {
            setError('Не удалось загрузить PDF документ');
            if (onError) onError('Не удалось загрузить PDF документ');
          }
        }
      };

      const setupIframeListeners = () => {
        if (iframeRef.current) {
          iframeRef.current.onload = () => {
            checkIframeLoaded();
          };

          iframeRef.current.onerror = (err) => {
            console.error('Iframe error:', err);
            if (isMounted) {
              setError('Ошибка при загрузке PDF документа');
              setLoading(false);
              if (onError) onError('Ошибка при загрузке PDF документа');
            }
          };
        }
      };

      setError(null);
      setupIframeListeners();

      return () => {
        isMounted = false;
      };
    };

    // Используем setTimeout для отложенного выполнения после гидратации
    const timer = setTimeout(initViewer, 0);
    return () => clearTimeout(timer);
  }, [documentUrl, onError, onLoaded]);

  // Функция для открытия PDF в новом окне
  const handleOpenInNewWindow = () => {
    window.open(documentUrl, '_blank');
  };

  // Добавляем кнопку для открытия PDF в новом окне
  const renderOpenButton = () => (
    <Tooltip title="Открыть в новом окне">
      <IconButton
        onClick={handleOpenInNewWindow}
        sx={{
          position: 'absolute',
          top: 8,
          right: 8,
          bgcolor: 'rgba(255,255,255,0.7)',
          '&:hover': {
            bgcolor: 'rgba(255,255,255,0.9)',
          },
          zIndex: 2
        }}
        size="small"
      >
        <OpenInNewIcon />
      </IconButton>
    </Tooltip>
  );

  console.log('Rendering PDF viewer with URL:', documentUrl);

  return (
    <Box sx={{
      display: 'flex',
      flexDirection: 'column',
      height: '100%',
      width: '100%',
      position: 'relative',
      overflow: 'hidden',
      borderRadius: 1,
      boxShadow: '0 2px 8px rgba(0,0,0,0.05)',
      bgcolor: '#f5f5f5'
    }}>
      {renderOpenButton()}

      {error ? (
        <Box sx={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          height: '100%',
          padding: 2,
          textAlign: 'center'
        }}>
          <Typography color="error">{error}</Typography>
        </Box>
      ) : (
        <iframe
          ref={iframeRef}
          src={`${documentUrl}#toolbar=1&view=FitH`}
          style={{
            width: '100%',
            height: '100%',
            border: 'none',
            overflow: 'hidden',
            backgroundColor: '#f5f5f5'
          }}
          title="PDF Viewer"
          allow="fullscreen"
          type="application/pdf"
        />
      )}
    </Box>
  );
};

export default SimplePdfViewer;
