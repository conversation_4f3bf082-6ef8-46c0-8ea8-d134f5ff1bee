'use client';

import React, { useEffect, useState } from 'react';
import './ProgressTable.css';
import api from '../utils/axiosInstance';
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Box,
  Typography,
  CircularProgress,
} from '@mui/material';
import { KeyboardArrowDown, KeyboardArrowRight } from '@mui/icons-material';
import { sendMetrikaEvent, MetrikaEvents } from '@/utils/metrikaEvents';

// Константы для статусов
const API_STATUSES = {
  SHOWN: "shown",
  PRACTICED: "practiced",
  MASTERED: "mastered",
  PLANNED: "planned",
  NEED_REVIEW: "needReview",
};

const DISPLAY_STATUSES = {
  SHOWN: "Показано",
  PRACTICED: "Практиковался",
  MASTERED: "Освоено",
  PLANNED: "Запланировано",
  NEED_REVIEW: "Надо повторить",
};

const tableColors = {
  [DISPLAY_STATUSES.SHOWN]: "#8FBC8F",      // Светлый морской зеленый
  [DISPLAY_STATUSES.PRACTICED]: "#6B8E23",  // Оливковый зеленый
  [DISPLAY_STATUSES.MASTERED]: "#556B2F",   // Темный оливковый зеленый
  [DISPLAY_STATUSES.PLANNED]: "#CD853F",    // Светлый коричневый
  [DISPLAY_STATUSES.NEED_REVIEW]: "#A0522D", // Коричневый
};

const tableLetters = {
  [DISPLAY_STATUSES.SHOWN]: "П",
  [DISPLAY_STATUSES.PRACTICED]: "П",
  [DISPLAY_STATUSES.MASTERED]: "О",
  [DISPLAY_STATUSES.PLANNED]: "З",
  [DISPLAY_STATUSES.NEED_REVIEW]: "Н",
};

// Преобразование API статуса в отображаемый
const apiToDisplay = {
  [API_STATUSES.SHOWN]: DISPLAY_STATUSES.SHOWN,
  [API_STATUSES.PRACTICED]: DISPLAY_STATUSES.PRACTICED,
  [API_STATUSES.MASTERED]: DISPLAY_STATUSES.MASTERED,
  [API_STATUSES.PLANNED]: DISPLAY_STATUSES.PLANNED,
  [API_STATUSES.NEED_REVIEW]: DISPLAY_STATUSES.NEED_REVIEW,
};

// Отображение статуса
const renderStatusIcon = (status) => {
  return (
    <Box
      sx={{
        display: "flex",
        justifyContent: "center",
        alignItems: "center",
        backgroundColor: tableColors[status] || "gray",
        color: "white",
        borderRadius: "50%",
        width: "30px",
        height: "30px",
        fontSize: "0.875rem",
        fontWeight: "bold",
      }}
    >
      {tableLetters[status] || "?"}
    </Box>
  );
};

// Компонент легенды
const Legend = () => {
  return (
    <Box mb={2}>
      <Typography variant="subtitle2" gutterBottom sx={{ color: '#333333', fontWeight: 'bold' }}>
        Обозначения:
      </Typography>
      <Box display="flex" flexWrap="wrap" gap={1}>
        {Object.entries(tableLetters).map(([status, letter]) => (
          <Box key={status} display="flex" alignItems="center" mr={1}>
            <Box
              sx={{
                width: 20,
                height: 20,
                backgroundColor: tableColors[status],
                marginRight: 1,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                borderRadius: '50%',
              }}
            >
              <Typography sx={{ color: 'white', fontSize: '0.7rem' }}>{letter}</Typography>
            </Box>
            <Typography variant="caption" sx={{ color: '#333' }}>{status}</Typography>
          </Box>
        ))}
      </Box>
    </Box>
  );
};

const ProgressTable = ({ childId: propChildId }) => {
  // Используем ID ребенка из пропсов или тестовый ID для разработки
  const [childId, setChildId] = useState(() => {
    // Если передан ID через пропсы, используем его
    if (propChildId) {
      return propChildId;
    }

    // Для разработки используем тестовый ID
    if (typeof window !== 'undefined' && process.env.NODE_ENV !== 'production') {
      return "7b19bb37-ab71-499b-aeea-194c72b9eb1a";
    }

    // Для production пытаемся получить ID из localStorage
    if (typeof window !== 'undefined') {
      const savedChildId = localStorage.getItem('childId');
      if (savedChildId) {
        console.log('Используем сохраненный ID ребенка:', savedChildId);
        return savedChildId;
      }
    }

    return null;
  });

  // Обновляем локальный childId при изменении propChildId
  useEffect(() => {
    if (propChildId && propChildId !== childId) {
      console.log('Обновляем ID ребенка в ProgressTable из пропсов:', propChildId);
      setChildId(propChildId);
    }
  }, [propChildId]);

  const [selectedCategory, setSelectedCategory] = useState('');
  const [categories, setCategories] = useState([]);
  const [openSkills, setOpenSkills] = useState({});
  const [childData, setChildData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  const toggleSkill = (skillName, subcategoryId) => {
    const isOpen = !openSkills[skillName];
    setOpenSkills((prev) => ({ ...prev, [skillName]: isOpen }));

    // Отправляем событие о раскрытии/скрытии навыка
    sendMetrikaEvent(MetrikaEvents.SKILL_DETAILS, {
      skill_name: skillName,
      skill_id: subcategoryId,
      category_id: selectedCategory,
      category_name: categories.find(cat => cat.id === selectedCategory)?.name || '',
      action: isOpen ? 'open' : 'close'
    });
  };

  // Получение ID ребенка по токену авторизации, если он не был найден в localStorage
  useEffect(() => {
    // Только для production окружения и если ID еще не получен
    if (typeof window !== 'undefined' && process.env.NODE_ENV === 'production' && !childId) {
      console.log('Не найден ID ребенка в localStorage, получаем список детей');

      // Получаем список детей по токену авторизации
      api.get('/children')
        .then(response => {
          if (response.data && response.data.length > 0) {
            // Выбираем первого ребенка из списка
            const firstChildId = response.data[0].id;
            setChildId(firstChildId);

            // Сохраняем ID в localStorage для будущих сессий
            localStorage.setItem('childId', firstChildId);
            console.log('Сохранен ID первого ребенка из списка:', firstChildId);
          } else {
            console.error('Не удалось получить данные о ребенке');
          }
        })
        .catch(err => {
          console.error('Ошибка при получении данных о ребенке:', err);
        });
    }
  }, [childId]);

  // Загрузка данных о ребенке и категориях навыков
  useEffect(() => {
    // Если ID ребенка не определен, не выполняем запрос
    if (!childId) return;

    const fetchData = async () => {
      try {
        setLoading(true);

        // Запросы к API для получения данных с использованием axios
        const [childResponse, categoriesResponse] = await Promise.all([
          api.get(`/children/${childId}/details`),
          api.get('/skills-category')
        ]);

        // Получаем данные из ответов axios
        const childData = childResponse.data;
        const categoriesData = categoriesResponse.data;

        setChildData(childData);
        setCategories(categoriesData);

        if (categoriesData.length > 0) {
          setSelectedCategory(categoriesData[0].id);
        }

        setLoading(false);
      } catch (err) {
        console.error('Ошибка при загрузке данных:', err);
        setError('Не удалось загрузить данные о прогрессе');
        setLoading(false);

        // Используем моковые данные в случае ошибки для демонстрации
        const mockChildData = {
          id: childId,
          name: 'Иван Иванов',
          progress: [
            { skillId: '101', status: 'mastered' },
            { skillId: '102', status: 'practiced' },
            { skillId: '103', status: 'shown' },
          ]
        };

        const mockCategories = [
          {
            id: 'cat1',
            name: 'Математика',
            subcategories: [
              {
                id: 'subcat1',
                name: 'Счет',
                exercises: [
                  { id: '101', name: 'Счет до 10' },
                  { id: '102', name: 'Счет до 20' },
                ]
              },
              {
                id: 'subcat2',
                name: 'Геометрия',
                exercises: [
                  { id: '103', name: 'Фигуры' },
                ]
              }
            ]
          },
          {
            id: 'cat2',
            name: 'Чтение',
            subcategories: [
              {
                id: 'subcat3',
                name: 'Алфавит',
                exercises: [
                  { id: '201', name: 'Гласные буквы' },
                  { id: '202', name: 'Согласные буквы' },
                ]
              }
            ]
          }
        ];

        setChildData(mockChildData);
        setCategories(mockCategories);
        setSelectedCategory(mockCategories[0]?.id || '');
        setLoading(false);
      }
    };

    fetchData();
  }, [childId]);

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" height="100%">
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" height="100%" flexDirection="column">
        <Typography color="error" gutterBottom>{error}</Typography>
        <Typography variant="caption">Используются демонстрационные данные</Typography>
      </Box>
    );
  }

  return (
    <Box sx={{ p: 2, height: '100%', overflow: 'auto' }}>
      <Typography variant="h5" component="h2" gutterBottom sx={{
        color: '#333',
        fontWeight: 'bold',
        fontFamily: 'var(--font-dancing-script)',
        fontSize: '2rem'
      }}>
        Прогресс {childData?.name}
      </Typography>

      {/* Выбор категории */}
      <FormControl fullWidth margin="normal" size="small" sx={{
        '& .MuiOutlinedInput-root': {
          '& fieldset': {
            borderColor: '#6B8E23',
          },
          '&:hover fieldset': {
            borderColor: '#8FBC8F',
          },
          '&.Mui-focused fieldset': {
            borderColor: '#556B2F',
          },
        },
        '& .MuiInputLabel-root': {
          color: '#5E6B4E',
          '&.Mui-focused': {
            color: '#6B8E23',
          },
        },
      }}>
        <InputLabel>Выберите категорию</InputLabel>
        <Select
          value={selectedCategory}
          onChange={(e) => {
            const newCategoryId = e.target.value;
            setSelectedCategory(newCategoryId);

            // Отправляем событие о выборе категории
            sendMetrikaEvent(MetrikaEvents.PROGRESS_VIEW, {
              category_id: newCategoryId,
              category_name: categories.find(cat => cat.id === newCategoryId)?.name || '',
              child_id: childId,
              child_name: childData?.name || ''
            });
          }}
          label="Выберите категорию"
          sx={{
            color: '#2E4514',
            '& .MuiSelect-icon': {
              color: '#6B8E23',
            },
          }}
        >
          {categories.map((category) => (
            <MenuItem key={category.id} value={category.id} sx={{
              color: '#2E4514',
              '&.Mui-selected': {
                backgroundColor: '#F0F5E1',
                '&:hover': {
                  backgroundColor: '#E6EFD1',
                },
              },
              '&:hover': {
                backgroundColor: '#F0F5E1',
              },
            }}>
              {category.name}
            </MenuItem>
          ))}
        </Select>
      </FormControl>

      <Legend />

      {/* Таблица прогресса */}
      <TableContainer component={Paper} sx={{ mt: 2, bgcolor: '#FAFAF0', borderRadius: '8px', overflow: 'hidden' }}>
        <Table size="small">
          <TableHead>
            <TableRow sx={{ bgcolor: '#6B8E23' }}>
              <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>Навыки / Упражнения</TableCell>
              <TableCell align="center" sx={{ color: 'white', fontWeight: 'bold' }}>Статус</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {selectedCategory && categories
              .find((cat) => cat.id === selectedCategory)
              ?.subcategories.map((subcategory) => (
                <React.Fragment key={subcategory.id}>
                  {/* Навык */}
                  <TableRow
                    sx={{
                      bgcolor: '#F0F5E1',
                      cursor: "pointer",
                      '&:hover': {
                        bgcolor: '#E6EFD1',
                      }
                    }}
                    onClick={() => toggleSkill(subcategory.name, subcategory.id)}
                  >
                    <TableCell>
                      <Box display="flex" justifyContent="space-between" alignItems="center">
                        {subcategory.name}
                        <IconButton size="small" onClick={(e) => {
                          e.stopPropagation(); // Предотвращаем двойное срабатывание
                          toggleSkill(subcategory.name, subcategory.id);
                        }}>
                          {openSkills[subcategory.name] ? (
                            <KeyboardArrowDown />
                          ) : (
                            <KeyboardArrowRight />
                          )}
                        </IconButton>
                      </Box>
                    </TableCell>
                    <TableCell />
                  </TableRow>

                  {/* Упражнения навыка */}
                  {openSkills[subcategory.name] &&
                    subcategory.exercises.map((exercise) => (
                      <TableRow key={exercise.id}>
                        <TableCell style={{ paddingLeft: "32px" }}>
                          {exercise.name}
                        </TableCell>
                        <TableCell align="center">
                          <Box display="flex" justifyContent="center">
                            {renderStatusIcon(
                              apiToDisplay[
                                childData?.progress.find(
                                  (p) => p.skillId === exercise.id
                                )?.status
                              ]
                            )}
                          </Box>
                        </TableCell>
                      </TableRow>
                    ))}
                </React.Fragment>
              ))}
          </TableBody>
        </Table>
      </TableContainer>
    </Box>
  );
};

export default ProgressTable;
