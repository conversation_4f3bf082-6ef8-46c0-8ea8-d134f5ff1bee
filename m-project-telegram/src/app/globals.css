@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
}

/* PDF Viewer Styles для WebViewer */
.webviewer {
  background-color: transparent;
}

/* Базовое переопределение */
.bottom-headers-wrapper {
  bottom: 40px !important;
  /* Добавляем дополнительные стили для перебивания */
  position: absolute !important;
  left: 0 !important;
  right: 0 !important;
  z-index: 10 !important;
  background-color: transparent !important;
  /* Если нужно изменить внешний вид панели */
  padding: 5px !important;
  display: flex !important;
  justify-content: center !important;
}

/* Более специфичное переопределение с использованием родительских селекторов */
.webviewer .bottom-headers-wrapper,
html body .bottom-headers-wrapper,
#app .bottom-headers-wrapper {
  bottom: 40px !important;
  /* Те же стили, но с большей специфичностью */
  position: absolute !important;
  left: 0 !important;
  right: 0 !important;
  z-index: 10 !important;
  background-color: transparent !important;
  padding: 5px !important;
  display: flex !important;
  justify-content: center !important;
}

/* Стили для дочерних элементов */
.bottom-headers-wrapper * {
  /* Переопределение стилей для всех дочерних элементов */
  font-family: Arial, Helvetica, sans-serif !important;
  color: #333 !important;
}

/* Стили для конкретных элементов внутри bottom-headers-wrapper */
.bottom-headers-wrapper button {
  background-color: #f5f5f5 !important;
  border: 1px solid #ddd !important;
  border-radius: 4px !important;
  padding: 5px 10px !important;
  margin: 0 2px !important;
}

.bottom-headers-wrapper input {
  border: 1px solid #ddd !important;
  border-radius: 4px !important;
  padding: 5px !important;
  margin: 0 2px !important;
}
