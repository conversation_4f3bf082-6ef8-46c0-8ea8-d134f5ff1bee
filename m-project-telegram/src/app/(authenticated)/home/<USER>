'use client';

import { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  CircularProgress,
  Alert
} from '@mui/material';
import { Event as EventIcon } from '@mui/icons-material';
import dynamic from 'next/dynamic';

// Динамический импорт компонента для просмотра PDF
const UniversalPdfViewer = dynamic(
  () => {
    console.log('Динамическая загрузка UniversalPdfViewer для календаря событий');
    return import('@/app/UniversalPdfViewer');
  },
  {
    ssr: false,
    loading: () => (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%' }}>
        <CircularProgress />
      </Box>
    )
  }
);

export default function HomePage() {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [calendarUrl, setCalendarUrl] = useState(null);
  const [fileExists, setFileExists] = useState(false);
  const currentMonth = new Date().toLocaleString('ru-RU', { month: 'long', year: 'numeric' });

  useEffect(() => {
    const fetchCalendar = async () => {
      try {
        setLoading(true);
        setError(null);

        // Получаем schoolId из localStorage
        const schoolId = localStorage.getItem('schoolId');
        if (!schoolId) {
          console.error('schoolId не найден в localStorage');
          setFileExists(false);
          return;
        }

        try {
          // Запрашиваем календарь с сервера
          const response = await fetch(`/api/calendar/${schoolId}`);

          if (response.ok) {
            const data = await response.json();
            if (data && data.fileUrl) {
              console.log('Получен URL календаря:', data.fileUrl);
              setCalendarUrl(data.fileUrl);
              setFileExists(true);
            } else {
              // Если нет данных, используем локальный файл
              const calendarPath = '/files/dummy.pdf';
              console.log('Используем локальный файл календаря:', calendarPath);
              setCalendarUrl(calendarPath);
              setFileExists(true);
            }
          } else {
            // Если сервер вернул ошибку, используем локальный файл
            const calendarPath = '/files/dummy.pdf';
            console.log('Используем локальный файл календаря:', calendarPath);
            setCalendarUrl(calendarPath);
            setFileExists(true);
          }
        } catch (err) {
          console.error('Ошибка при получении календаря с сервера:', err);
          // В случае ошибки используем локальный файл
          const calendarPath = '/files/dummy.pdf';
          console.log('Используем локальный файл календаря:', calendarPath);
          setCalendarUrl(calendarPath);
          setFileExists(true);
        }
      } catch (err) {
        console.error('Ошибка при загрузке календаря событий:', err);
        setError('Не удалось загрузить календарь событий. Пожалуйста, попробуйте позже.');
      } finally {
        setLoading(false);
      }
    };

    fetchCalendar();
  }, []);

  const handleError = (errorMessage) => {
    console.error('Ошибка при отображении PDF:', errorMessage);
    setError(errorMessage);
  };

  return (
    <Box sx={{
      display: 'flex',
      flexDirection: 'column',
      height: 'calc(100vh - 104px)', // Вычитаем высоту заголовка (48px) и нижней навигации (56px)
      width: '100%',
      overflow: 'hidden'
    }}>
      {/* Заголовок - делаем его компактнее */}
      <Box sx={{
        px: 2,
        py: 1, // Уменьшаем отступы сверху и снизу
        display: 'flex',
        alignItems: 'center',
        borderBottom: '1px solid #f0f0f0',
        minHeight: '48px' // Фиксированная минимальная высота
      }}>
        <EventIcon sx={{ fontSize: 24, color: '#6B8E23', mr: 1 }} />
        <Typography
          variant="h6" // Уменьшаем размер заголовка
          sx={{
            color: '#556B2F',
            fontFamily: 'var(--font-dancing-script)',
            fontWeight: 'bold',
            fontSize: '1.25rem' // Уменьшаем размер шрифта
          }}
        >
          Календарь событий
        </Typography>
      </Box>

      {/* Основное содержимое */}
      <Box sx={{
        flex: 1,
        display: 'flex',
        flexDirection: 'column',
        overflow: 'hidden',
        width: '100%',
        // Убираем отступ снизу для максимального размера
        // pb: 2
      }}>
        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', flex: 1 }}>
            <CircularProgress color="primary" />
          </Box>
        ) : error ? (
          <Box sx={{ p: 2 }}>
            <Alert severity="error">{error}</Alert>
          </Box>
        ) : !fileExists || !calendarUrl ? (
          <Box sx={{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            flex: 1,
            p: 2
          }}>
            <Paper
              elevation={2}
              sx={{
                p: 4,
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                justifyContent: 'center',
                bgcolor: '#FAFAF0',
                borderRadius: 2,
                width: '100%',
                maxWidth: 'sm',
                textAlign: 'center'
              }}
            >
              <EventIcon sx={{ fontSize: 60, color: '#8FBC8F', mb: 2 }} />

              <Typography variant="h6" sx={{ mb: 2, color: '#556B2F' }}>
                Мы обновляем календарь событий
              </Typography>

              <Typography variant="body2" sx={{ color: '#666' }}>
                Календарь событий на {currentMonth} будет доступен в ближайшее время.
              </Typography>
            </Paper>
          </Box>
        ) : (
          <Box sx={{
            flex: 1,
            width: '100%',
            height: '100%',
            overflow: 'hidden',
            // Убираем все отступы для максимального размера
            p: 0,
            m: 0
          }}>
            <UniversalPdfViewer
              documentUrl={`https://mymontessory.ru/uploads/calendars/school-67e43b51-01dc-4dbb-abb0-142c52921dc6.pdf`}
              onError={handleError}

            />
          </Box>
        )}
      </Box>
    </Box>
  );
}
