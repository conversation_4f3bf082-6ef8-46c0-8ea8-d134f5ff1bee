'use client';

import { useState, useEffect } from 'react';
import { Box, CircularProgress, Typography, Tabs, Tab, Alert, Paper } from '@mui/material';
import dynamic from 'next/dynamic';
import api from '@/utils/axiosInstance';
import { sendMetrikaEvent, MetrikaEvents } from '@/utils/metrikaEvents';

// Используем dynamic чтобы импортировать компоненты только на клиенте
const UniversalPdfViewer = dynamic(
  () => {
    console.log('Динамическая загрузка UniversalPdfViewer');
    return import('@/app/UniversalPdfViewer');
  },
  {
    ssr: false,
  }
);

export default function InfoPage() {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [activeTab, setActiveTab] = useState(0);

  // URL документов для каждой вкладки
  const [documentUrls, setDocumentUrls] = useState({
    0: null, // URL для меню
    1: null, // URL для расписания
  });

  // Состояние загрузки для каждой вкладки
  const [fileStatus, setFileStatus] = useState({
    0: 'loading', // Статус для меню: 'loading', 'error', 'not_found', 'loaded'
    1: 'loading', // Статус для расписания
  });

  // Названия вкладок
  const tabLabels = {
    0: "Меню",
    1: "Расписание"
  };

  // Загрузка данных о меню и расписании
  useEffect(() => {
    // Используем отложенное выполнение для предотвращения ошибок гидратации
    const fetchDocuments = async () => {
      console.log('Загрузка документов...');

      try {
        // Загрузка меню
        try {
          // Получаем schoolId из localStorage
          const schoolId = localStorage.getItem('schoolId');
          if (schoolId) {
            // Запрашиваем меню для школы
            const menuResponse = await api.get(`/menu/${schoolId}`);
            if (menuResponse.data && menuResponse.data.fileUrl) {
              console.log('Получен URL меню:', menuResponse.data.fileUrl);
              setDocumentUrls(prev => ({ ...prev, 0: menuResponse.data.fileUrl }));
              setFileStatus(prev => ({ ...prev, 0: 'loaded' }));
            } else {
              // Если нет данных, используем локальный файл
              const menuUrl = '/files/dummy.pdf';
              console.log('Используем локальный файл меню:', menuUrl);
              setDocumentUrls(prev => ({ ...prev, 0: menuUrl }));
              setFileStatus(prev => ({ ...prev, 0: 'loaded' }));
            }
          } else {
            // Если нет schoolId, используем локальный файл
            const menuUrl = '/files/dummy.pdf';
            console.log('Используем локальный файл меню:', menuUrl);
            setDocumentUrls(prev => ({ ...prev, 0: menuUrl }));
            setFileStatus(prev => ({ ...prev, 0: 'loaded' }));
          }
        } catch (error) {
          console.error('Ошибка при загрузке меню:', error);
          // В случае ошибки используем локальный файл
          const menuUrl = '/files/dummy.pdf';
          setDocumentUrls(prev => ({ ...prev, 0: menuUrl }));
          setFileStatus(prev => ({ ...prev, 0: 'loaded' }));
        }

        // Загрузка расписания
        try {
          // Получаем ID ребенка из localStorage
          const childId = localStorage.getItem('childId');

          if (childId) {
            // Запрашиваем расписание для ребенка с учетом его типа группы
            const scheduleResponse = await api.get(`/schedule/child/${childId}`);
            if (scheduleResponse.data && scheduleResponse.data.fileUrl) {
              console.log('Получен URL расписания:', scheduleResponse.data.fileUrl);
              setDocumentUrls(prev => ({ ...prev, 1: scheduleResponse.data.fileUrl }));
              setFileStatus(prev => ({ ...prev, 1: 'loaded' }));
            } else {
              // Если нет данных, используем локальный файл
              const scheduleUrl = '/files/dummy.pdf';
              console.log('Используем локальный файл расписания:', scheduleUrl);
              setDocumentUrls(prev => ({ ...prev, 1: scheduleUrl }));
              setFileStatus(prev => ({ ...prev, 1: 'loaded' }));
            }
          } else {
            // Если нет childId, используем локальный файл
            const scheduleUrl = '/files/dummy.pdf';
            console.log('Используем локальный файл расписания:', scheduleUrl);
            setDocumentUrls(prev => ({ ...prev, 1: scheduleUrl }));
            setFileStatus(prev => ({ ...prev, 1: 'loaded' }));
          }
        } catch (error) {
          console.error('Ошибка при загрузке расписания:', error);
          // В случае ошибки используем локальный файл
          const scheduleUrl = '/files/dummy.pdf';
          setDocumentUrls(prev => ({ ...prev, 1: scheduleUrl }));
          setFileStatus(prev => ({ ...prev, 1: 'loaded' }));
        }
      } finally {
        // Скрываем общий лоадер с небольшой задержкой
        setTimeout(() => {
          console.log('Скрываем лоадер...');
          setLoading(false);
        }, 300);
      }
    };

    // Используем setTimeout для отложенного выполнения после гидратации
    const timer = setTimeout(fetchDocuments, 0);

    // Автоматическое скрытие лоадера через короткий таймаут
    const timeoutId = setTimeout(() => {
      console.log('Forced loading complete via master timeout');
      setLoading(false);
    }, 1500); // Более короткий таймаут для улучшения UX

    return () => {
      clearTimeout(timer);
      clearTimeout(timeoutId);
    };
  }, []);

  const handleError = (errorMessage) => {
    console.error('Error in PDF viewer:', errorMessage);
    setError(errorMessage);
    setLoading(false);

    // Если ошибка связана с WebViewer, обновляем статус текущей вкладки
    if (errorMessage.includes('WebViewer') || errorMessage.includes('просмотрщик')) {
      setFileStatus(prev => ({ ...prev, [activeTab]: 'error' }));
    }
  };

  const handleLoaded = () => {
    console.log('PDF viewer loaded callback');
    // Сразу скрываем лоадер
    setLoading(false);
  };

  const handleTabChange = (_, newValue) => {
    // Просто меняем вкладку без лоадера
    setActiveTab(newValue);

    // Отправляем событие о смене таба
    sendMetrikaEvent(MetrikaEvents.TAB_CHANGE, {
      page_type: 'info',
      tab_name: tabLabels[newValue],
      tab_index: newValue
    });
  };

  return (
    <Box
      sx={{
        height: '100%',
        width: '100%',
        display: 'flex',
        flexDirection: 'column',
        overflow: 'hidden',
        position: 'relative'
      }}
    >
      <Tabs
        value={activeTab}
        onChange={handleTabChange}
        sx={{
          borderBottom: 1,
          borderColor: 'divider',
          '& .MuiTabs-indicator': {
            transition: 'none',
          },
        }}
      >
        <Tab
          label={tabLabels[0]}
          sx={{
            fontWeight: activeTab === 0 ? 'bold' : 'normal',
            fontSize: '0.95rem'
          }}
        />
        <Tab
          label={tabLabels[1]}
          sx={{
            fontWeight: activeTab === 1 ? 'bold' : 'normal',
            fontSize: '0.95rem'
          }}
        />
      </Tabs>

      {error ? (
        <Box sx={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          height: '100%',
          color: 'error.main'
        }}>
          <Typography>{error}</Typography>
        </Box>
      ) : fileStatus[activeTab] === 'not_found' ? (
        // Отображаем сообщение, если файл не найден
        <Box sx={{ p: 4, display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%' }}>
          <Paper elevation={2} sx={{ p: 4, maxWidth: '600px', bgcolor: '#FAFAF0', borderRadius: 2 }}>
            <Alert severity="info" sx={{ mb: 2 }}>
              Информация обновляется
            </Alert>
            <Typography variant="body1" sx={{ textAlign: 'center' }}>
              {activeTab === 0 ? 'Меню' : 'Расписание'} в настоящее время обновляется и скоро будет доступно.
            </Typography>
          </Paper>
        </Box>
      ) : fileStatus[activeTab] === 'error' ? (
        // Отображаем сообщение об ошибке
        <Box sx={{ p: 4, display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%' }}>
          <Paper elevation={2} sx={{ p: 4, maxWidth: '600px', bgcolor: '#FAFAF0', borderRadius: 2 }}>
            <Alert severity="error" sx={{ mb: 2 }}>
              Ошибка загрузки
            </Alert>
            <Typography variant="body1" sx={{ textAlign: 'center' }}>
              Произошла ошибка при загрузке {activeTab === 0 ? 'меню' : 'расписания'}. Пожалуйста, попробуйте позже.
            </Typography>
          </Paper>
        </Box>
      ) : documentUrls[activeTab] ? (
        // Отображаем PDF просмотрщик для вкладок "Меню" и "Расписание"
        <Box sx={{
          flex: 1,
          display: 'flex',
          flexDirection: 'column',
          p: 1,
          overflow: 'hidden'
        }}>
          <UniversalPdfViewer
            documentUrl={documentUrls[activeTab]}
            onError={handleError}
            onLoaded={handleLoaded}

          />
        </Box>
      ) : null}

      {loading && !error && (
        <Box sx={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'center',
          alignItems: 'center',
          zIndex: 10,
          bgcolor: 'rgba(255, 255, 255, 0.7)',
        }}>
          <CircularProgress size={36} thickness={3} />
          <Typography sx={{ mt: 0.5 }}>Загрузка PDF документа...</Typography>
        </Box>
      )}
    </Box>
  );
}
