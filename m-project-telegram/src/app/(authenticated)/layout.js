'use client';

import { useState, useEffect } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import Link from 'next/link';
import {
  AppBar,
  Box,
  BottomNavigation,
  BottomNavigationAction,
  Typography,
  Paper,
  IconButton
} from '@mui/material';
import {
  ShowChart as ProgressIcon,
  Info as InfoIcon,
  EventAvailable as AttendanceIcon,
  Logout as LogoutIcon,
  Home as HomeIcon
} from '@mui/icons-material';
import ChildSelector from '@/components/ChildSelector';
import { sendMetrikaEvent, MetrikaEvents } from '@/utils/metrikaEvents';

export default function AuthenticatedLayout({ children }) {
  const router = useRouter();
  const pathname = usePathname();
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [currentChildId, setCurrentChildId] = useState(null);

  // Проверка авторизации при загрузке
  useEffect(() => {
    // Используем отложенное выполнение для предотвращения ошибок гидратации
    const checkAuth = () => {
      const token = localStorage.getItem('accessToken');
      if (token) {
        setIsAuthenticated(true);

        // Получаем ID ребенка из localStorage
        const savedChildId = localStorage.getItem('childId');
        if (savedChildId) {
          setCurrentChildId(savedChildId);
        }
      } else {
        // Если нет токена, перенаправляем на страницу авторизации
        router.push('/');
      }
    };

    // Используем setTimeout для отложенного выполнения после гидратации
    const timer = setTimeout(checkAuth, 0);
    return () => clearTimeout(timer);
  }, [router]);

  // Определяем текущий активный маршрут для нижней навигации
  const getActiveRoute = () => {
    if (pathname.includes('/home')) return 0;
    if (pathname.includes('/progress')) return 1;
    if (pathname.includes('/info')) return 2;
    return 0; // По умолчанию - главная страница
  };

  // Обработчик выбора ребенка
  const handleChildSelect = (childId) => {
    // Проверяем, изменился ли ID ребенка
    if (childId === currentChildId) {
      console.log('Идентификатор ребенка не изменился, пропускаем обновление');
      return;
    }

    // Обновляем состояние
    setCurrentChildId(childId);

    // Сохраняем выбранный ID в localStorage
    localStorage.setItem('childId', childId);

    // Если мы находимся на странице прогресса, обновляем URL с новым ID ребенка
    if (pathname.includes('/progress')) {
      // Используем replace вместо push, чтобы не добавлять новые записи в историю браузера
      router.replace(`/progress?childId=${childId}`);
    }
  };

  // Обработчик выхода из аккаунта
  const handleLogout = () => {
    if (typeof window !== 'undefined') {
      // Отправляем событие о выходе из аккаунта
      sendMetrikaEvent('logout');

      localStorage.removeItem('accessToken');
      localStorage.removeItem('childId');
      router.push('/');
    }
  };

  // Отслеживаем переходы между страницами
  useEffect(() => {
    if (pathname) {
      // Определяем тип страницы по пути
      let pageType = 'unknown';
      if (pathname.includes('/home')) pageType = 'home';
      if (pathname.includes('/progress')) pageType = 'progress';
      if (pathname.includes('/info')) pageType = 'info';

      // Отправляем событие о просмотре страницы
      sendMetrikaEvent(MetrikaEvents.PAGE_VIEW, {
        page_type: pageType,
        path: pathname
      });
    }
  }, [pathname]);

  return (
    <Box sx={{
      display: 'flex',
      flexDirection: 'column',
      height: '100vh',
      bgcolor: 'background.default',
      overflow: 'hidden',
    }}>
      <AppBar position="static" sx={{ bgcolor: '#6B8E23' }}>
        <Box sx={{ display: 'flex', alignItems: 'center', p: 2 }}>
          {isAuthenticated && (
            <IconButton
              edge="start"
              color="inherit"
              aria-label="logout"
              onClick={handleLogout}
              sx={{ mr: 1 }}
            >
              <LogoutIcon />
            </IconButton>
          )}
          <Typography variant="h6" component="div" sx={{
            flex: 1,
            textAlign: 'center',
            fontWeight: 'bold',
            fontFamily: 'var(--font-dancing-script)',
            fontSize: '1.8rem'
          }}>
            MyMontessori
          </Typography>
          {/* Пустой элемент для баланса */}
          <Box sx={{ width: 40 }} />
        </Box>
      </AppBar>

      <Box sx={{
        flex: 1,
        overflow: 'hidden',
        display: 'flex',
        flexDirection: 'column',
        paddingBottom: '56px'
      }}>
        {/* Селектор выбора ребенка */}
        <Box sx={{ px: 2, pt: 1 }}>
          <ChildSelector onChildSelect={handleChildSelect} />
        </Box>

        {/* Основное содержимое страницы */}
        {children}
      </Box>

      <Paper sx={{ position: 'fixed', bottom: 0, left: 0, right: 0, bgcolor: '#FAFAF0' }} elevation={3}>
        <BottomNavigation
          showLabels
          value={getActiveRoute()}
          sx={{
            bgcolor: '#FAFAF0',
            '& .MuiBottomNavigationAction-root': {
              color: '#666666',
            },
            '& .Mui-selected': {
              color: '#6B8E23',
            },
          }}
        >
          <BottomNavigationAction
            component={Link}
            href="/home"
            label="Главная"
            icon={<HomeIcon />}
          />
          <BottomNavigationAction
            component={Link}
            href={`/progress${currentChildId ? `?childId=${currentChildId}` : ''}`}
            label="Прогресс"
            icon={<ProgressIcon />}
          />
          <BottomNavigationAction
            component={Link}
            href="/info"
            label="Информация"
            icon={<InfoIcon />}
          />
        </BottomNavigation>
      </Paper>
    </Box>
  );
}
