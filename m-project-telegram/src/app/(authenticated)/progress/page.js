'use client';

import { useEffect, useState, Suspense } from 'react';
import { useSearchParams } from 'next/navigation';
import { Box, CircularProgress } from '@mui/material';
import ProgressTable from '@/app/ProgressTable';

// Компонент, который использует useSearchParams
function ProgressContent() {
  const searchParams = useSearchParams();
  const [childId, setChildId] = useState(null);

  useEffect(() => {
    // Получаем childId из URL параметров
    const childIdFromUrl = searchParams.get('childId');

    // Проверяем, изменился ли ID ребенка
    if (childIdFromUrl && childIdFromUrl !== childId) {
      console.log('Обновляем childId из URL:', childIdFromUrl);
      setChildId(childIdFromUrl);
    } else if (!childIdFromUrl && !childId) {
      // Если нет в URL и еще не установлен childId, пытаемся получить из localStorage
      const savedChildId = localStorage.getItem('childId');
      if (savedChildId) {
        console.log('Используем childId из localStorage:', savedChildId);
        setChildId(savedChildId);
      } else if (process.env.NODE_ENV !== 'production') {
        // Для разработки используем тестовый ID
        console.log('Используем тестовый childId для разработки');
        setChildId("7b19bb37-ab71-499b-aeea-194c72b9eb1a");
      }
    }
  }, [searchParams, childId]);

  return (
    <Box sx={{ height: '100%', width: '100%', overflow: 'auto' }}>
      <ProgressTable childId={childId} />
    </Box>
  );
}

// Компонент загрузки для Suspense
function ProgressLoading() {
  return (
    <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%', width: '100%' }}>
      <CircularProgress />
    </Box>
  );
}

export default function ProgressPage() {
  return (
    <Suspense fallback={<ProgressLoading />}>
      <ProgressContent />
    </Suspense>
  );
}
