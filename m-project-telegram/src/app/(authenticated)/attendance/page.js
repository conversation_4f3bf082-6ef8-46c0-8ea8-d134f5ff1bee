'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import {
  Box,
  Typography,
  Paper,
  Container,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  List,
  ListItem,
  ListItemText,
  Divider,
  CircularProgress,
  Alert,
  Chip
} from '@mui/material';
import {
  EventAvailable as AttendanceIcon,
  ExpandMore as ExpandMoreIcon,
  CheckCircle as PresentIcon,
  Cancel as AbsentIcon,
  Schedule as LateIcon
} from '@mui/icons-material';
import api from '@/utils/axiosInstance';

// Функция для форматирования даты
const formatDate = (dateString) => {
  const date = new Date(dateString);
  return date.toLocaleDateString('ru-RU', {
    day: 'numeric',
    month: 'long',
    year: 'numeric',
    weekday: 'long'
  });
};

// Функция для группировки посещений по месяцам
const groupAttendanceByMonth = (attendanceList) => {
  const grouped = {};

  attendanceList.forEach(attendance => {
    const date = new Date(attendance.date);
    const monthYear = date.toLocaleDateString('ru-RU', { month: 'long', year: 'numeric' });

    if (!grouped[monthYear]) {
      grouped[monthYear] = [];
    }

    grouped[monthYear].push(attendance);
  });

  // Сортируем записи внутри каждого месяца по дате (от новых к старым)
  Object.keys(grouped).forEach(month => {
    grouped[month].sort((a, b) => new Date(b.date) - new Date(a.date));
  });

  return grouped;
};

// Компонент для отображения статуса посещения
const AttendanceStatus = ({ status }) => {
  switch (status.toLowerCase()) {
    case 'present':
      return (
        <Chip
          icon={<PresentIcon />}
          label="Присутствовал"
          color="success"
          size="small"
          variant="outlined"
        />
      );
    case 'absent':
      return (
        <Chip
          icon={<AbsentIcon />}
          label="Отсутствовал"
          color="error"
          size="small"
          variant="outlined"
        />
      );
    case 'late':
      return (
        <Chip
          icon={<LateIcon />}
          label="Опоздал"
          color="warning"
          size="small"
          variant="outlined"
        />
      );
    default:
      return (
        <Chip
          label={status}
          size="small"
          variant="outlined"
        />
      );
  }
};

export default function AttendancePage() {
  const [attendance, setAttendance] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [groupedAttendance, setGroupedAttendance] = useState({});
  const [expandedMonth, setExpandedMonth] = useState(null);
  const router = useRouter();

  // Перенаправляем на главную страницу, так как страница посещаемости скрыта
  useEffect(() => {
    router.replace('/home');
  }, [router]);

  useEffect(() => {
    const fetchAttendance = async () => {
      try {
        setLoading(true);
        setError(null);

        // Получаем ID ребенка из localStorage
        const childId = localStorage.getItem('childId');

        if (!childId) {
          setError('ID ребенка не найден. Пожалуйста, выберите ребенка.');
          setLoading(false);
          return;
        }

        // Запрашиваем данные о посещаемости
        const response = await api.get(`/attendance?childId=${childId}`);

        // Устанавливаем данные
        setAttendance(response.data);

        // Группируем данные по месяцам
        const grouped = groupAttendanceByMonth(response.data);
        setGroupedAttendance(grouped);

        // Устанавливаем текущий месяц как развернутый по умолчанию
        if (Object.keys(grouped).length > 0) {
          setExpandedMonth(Object.keys(grouped)[0]);
        }
      } catch (err) {
        console.error('Ошибка при загрузке данных о посещаемости:', err);
        setError('Не удалось загрузить данные о посещаемости. Пожалуйста, попробуйте позже.');
      } finally {
        setLoading(false);
      }
    };

    fetchAttendance();
  }, []);

  const handleAccordionChange = (month) => (event, isExpanded) => {
    setExpandedMonth(isExpanded ? month : null);
  };

  return (
    <Container maxWidth="sm" sx={{ py: 4, pb: 10 }}>
      <Box sx={{ mb: 3, display: 'flex', alignItems: 'center' }}>
        <AttendanceIcon sx={{ fontSize: 30, color: '#6B8E23', mr: 1 }} />
        <Typography
          variant="h5"
          sx={{
            color: '#556B2F',
            fontFamily: 'var(--font-dancing-script)',
            fontWeight: 'bold'
          }}
        >
          Посещаемость
        </Typography>
      </Box>

      {loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
          <CircularProgress color="primary" />
        </Box>
      ) : error ? (
        <Alert severity="error" sx={{ mb: 2 }}>{error}</Alert>
      ) : attendance.length === 0 ? (
        <Paper
          elevation={2}
          sx={{
            p: 4,
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            bgcolor: '#FAFAF0',
            borderRadius: 2,
            minHeight: '200px',
            textAlign: 'center'
          }}
        >
          <AttendanceIcon sx={{ fontSize: 60, color: '#8FBC8F', mb: 2 }} />

          <Typography variant="h6" sx={{ mb: 2, color: '#556B2F' }}>
            Нет данных о посещаемости
          </Typography>

          <Typography variant="body2" sx={{ color: '#666' }}>
            Информация о посещениях будет отображаться здесь, как только появятся данные.
          </Typography>
        </Paper>
      ) : (
        <Box>
          {Object.keys(groupedAttendance).map((month) => (
            <Accordion
              key={month}
              expanded={expandedMonth === month}
              onChange={handleAccordionChange(month)}
              sx={{
                mb: 2,
                bgcolor: '#FAFAF0',
                '&:before': {
                  display: 'none',
                },
              }}
            >
              <AccordionSummary
                expandIcon={<ExpandMoreIcon />}
                sx={{
                  bgcolor: '#F5F5DC',
                  borderRadius: '4px 4px 0 0',
                }}
              >
                <Typography sx={{ fontWeight: 'bold', color: '#556B2F' }}>
                  {month} ({groupedAttendance[month].length})
                </Typography>
              </AccordionSummary>
              <AccordionDetails>
                <List sx={{ width: '100%', p: 0 }}>
                  {groupedAttendance[month].map((item, index) => (
                    <Box key={item.id}>
                      <ListItem
                        alignItems="flex-start"
                        sx={{
                          flexDirection: 'column',
                          alignItems: 'flex-start',
                          py: 1.5
                        }}
                      >
                        <Box sx={{
                          display: 'flex',
                          justifyContent: 'space-between',
                          width: '100%',
                          mb: 1
                        }}>
                          <Typography variant="subtitle1" sx={{ fontWeight: 'bold' }}>
                            {formatDate(item.date)}
                          </Typography>
                          <AttendanceStatus status={item.status} />
                        </Box>

                        {(item.checkInTime || item.checkOutTime) && (
                          <Box sx={{ mt: 1, color: '#666' }}>
                            {item.checkInTime && (
                              <Typography variant="body2">
                                Время прихода: {new Date(item.checkInTime).toLocaleTimeString('ru-RU', { hour: '2-digit', minute: '2-digit' })}
                              </Typography>
                            )}
                            {item.checkOutTime && (
                              <Typography variant="body2">
                                Время ухода: {new Date(item.checkOutTime).toLocaleTimeString('ru-RU', { hour: '2-digit', minute: '2-digit' })}
                              </Typography>
                            )}
                          </Box>
                        )}
                      </ListItem>
                      {index < groupedAttendance[month].length - 1 && <Divider />}
                    </Box>
                  ))}
                </List>
              </AccordionDetails>
            </Accordion>
          ))}
        </Box>
      )}
    </Container>
  );
}
