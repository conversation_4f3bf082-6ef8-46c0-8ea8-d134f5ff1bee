// Полифилл для URL.parse для совместимости с PDF.js
// Этот файл решает проблему "URL.parse is not a function" в Telegram Mini App

(function() {
  'use strict';
  
  // Проверяем, нужен ли полифилл
  if (typeof URL !== 'undefined' && !URL.parse) {
    console.log('Adding URL.parse polyfill for PDF.js compatibility');
    
    URL.parse = function(url, base) {
      try {
        if (base) {
          return new URL(url, base);
        } else {
          return new URL(url);
        }
      } catch (e) {
        // Fallback для относительных URL
        try {
          if (typeof window !== 'undefined' && window.location) {
            return new URL(url, window.location.href);
          } else if (typeof self !== 'undefined' && self.location) {
            return new URL(url, self.location.href);
          }
        } catch (e2) {
          console.warn('Failed to parse URL with polyfill:', url, e2);
          return null;
        }
        console.warn('Failed to parse URL:', url, e);
        return null;
      }
    };
  }
  
  // Полифилл для worker контекста
  if (typeof self !== 'undefined' && typeof self.URL !== 'undefined' && !self.URL.parse) {
    console.log('Adding URL.parse polyfill for worker context');
    
    self.URL.parse = function(url, base) {
      try {
        if (base) {
          return new self.URL(url, base);
        } else {
          return new self.URL(url);
        }
      } catch (e) {
        console.warn('Failed to parse URL in worker context:', url, e);
        return null;
      }
    };
  }
  
  console.log('URL polyfill loaded successfully');
})();
