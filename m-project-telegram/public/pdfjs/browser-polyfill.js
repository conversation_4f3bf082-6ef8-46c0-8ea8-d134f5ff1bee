// Полифилл для переменной browser, которая используется в PDF.js
if (typeof browser === 'undefined') {
  var browser = {
    // Добавляем минимальные необходимые методы и свойства
    runtime: {
      getURL: function(path) {
        return path;
      }
    }
  };
}

// Полифилл для chrome, который также может использоваться в PDF.js
if (typeof chrome === 'undefined') {
  var chrome = {
    runtime: {
      getURL: function(path) {
        return path;
      }
    }
  };
}

console.log('Browser polyfill loaded');
