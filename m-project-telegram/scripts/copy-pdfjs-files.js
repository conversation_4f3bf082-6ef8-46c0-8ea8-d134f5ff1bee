const fs = require('fs-extra');
const path = require('path');
const { version } = require('pdfjs-dist/package.json');

// Пути к файлам
const pdfJsPath = path.join(__dirname, '../node_modules/pdfjs-dist');
const publicPath = path.join(__dirname, '../public/pdfjs');

// Создаем директорию, если она не существует
fs.ensureDirSync(publicPath);

// Копируем worker файл (проверяем разные возможные расположения)
let workerSrc = path.join(pdfJsPath, 'build', 'pdf.worker.min.mjs');
if (!fs.existsSync(workerSrc)) {
  workerSrc = path.join(pdfJsPath, 'build', 'pdf.worker.min.js');
}
if (!fs.existsSync(workerSrc)) {
  workerSrc = path.join(pdfJsPath, 'build', 'pdf.worker.js');
}
const workerDest = path.join(publicPath, 'pdf.worker.min.js');

try {
  // Копируем worker файл
  fs.copyFileSync(workerSrc, workerDest);
  console.log(`Copied PDF.js worker file to ${workerDest}`);

  // Создаем файл с правильным содержимым
  const workerContent = fs.readFileSync(workerSrc, 'utf8');
  // Добавляем полифиллы для совместимости
  const modifiedContent = `// Полифиллы для совместимости с PDF.js
if (typeof browser === 'undefined') {
  var browser = {};
}

// Полифилл для URL.parse (для совместимости с PDF.js)
if (typeof URL !== 'undefined' && !URL.parse) {
  URL.parse = function(url) {
    try {
      return new URL(url);
    } catch (e) {
      // Fallback для относительных URL
      try {
        return new URL(url, window.location.href);
      } catch (e2) {
        console.warn('Failed to parse URL:', url);
        return null;
      }
    }
  };
}

// Полифилл для глобального URL в worker контексте
if (typeof self !== 'undefined' && typeof self.URL !== 'undefined' && !self.URL.parse) {
  self.URL.parse = function(url) {
    try {
      return new self.URL(url);
    } catch (e) {
      console.warn('Failed to parse URL in worker:', url);
      return null;
    }
  };
}

${workerContent}`;
  fs.writeFileSync(workerDest, modifiedContent);

  // Создаем пустой файл web/viewer.html для совместимости
  const webDir = path.join(publicPath, 'web');
  fs.ensureDirSync(webDir);
  fs.writeFileSync(path.join(webDir, 'viewer.html'), '<html><body>PDF Viewer</body></html>');

  console.log(`PDF.js version: ${version}`);
} catch (err) {
  console.error(`Error copying PDF.js files: ${err.message}`);
}
