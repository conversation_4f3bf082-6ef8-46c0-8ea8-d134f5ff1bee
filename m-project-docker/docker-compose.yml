version: "3.8"

services:
  postgres:
    image: postgres:15
    container_name: postgres
    restart: always
    env_file: .env
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - mynetwork
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U monte -d montebase"]
      interval: 10s
      timeout: 5s
      retries: 5
    command: ["docker-entrypoint.sh", "postgres"]

  backend:
    container_name: backend
    build:
      context: ../m-project-back
    env_file: .env
    ports:
      - "3002:3002"
    command: sh -c "npx prisma migrate deploy && npm run start:prod"
    volumes:
      - uploads:/usr/src/app/uploads
      - ../m-project-back/prisma:/usr/src/app/prisma
    depends_on:
      postgres:
        condition: service_healthy
    networks:
      - mynetwork

  # frontend:
  #   build:
  #     context: ../m-project-front
  #   container_name: frontend
  #   restart: always
  #   env_file: .env
  #   ports:
  #     - "3000:3000"
  #   depends_on:
  #     - backend
  #   networks:
  #     - mynetwork

volumes:
  postgres_data:
    name: m_project_postgres_data
    driver: local
  uploads:
    name: m_project_uploads
    driver: local

networks:
  mynetwork:
    name: m_project_network
    driver: bridge