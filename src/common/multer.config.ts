import { MulterOptions } from '@nestjs/platform-express/multer/interfaces/multer-options.interface';
import { diskStorage } from 'multer';
import { extname } from 'path';
import { v4 as uuidv4 } from 'uuid';

// Конфигурация Multer с увеличенным лимитом размера файла
export const multerConfig: MulterOptions = {
  limits: {
    fileSize: 15 * 1024 * 1024, // 15 MB в байтах
  },
  // Можно добавить фильтрацию по типу файла, если нужно
  fileFilter: (req, file, callback) => {
    if (file.mimetype.match(/\/(pdf|jpeg|jpg|png)$/)) {
      // Разрешаем загрузку PDF и изображений
      callback(null, true);
    } else {
      // Отклоняем файл
      callback(new Error('Поддерживаются только PDF и изображения!'), false);
    }
  },
};

// Конфигурация для сохранения файлов на диск с уникальными именами
export const multerDiskStorage = {
  storage: diskStorage({
    destination: (req, file, callback) => {
      // Путь можно динамически определять в зависимости от типа файла
      callback(null, 'uploads/');
    },
    filename: (req, file, callback) => {
      // Создаем уникальное имя файла
      const uniqueSuffix = `${Date.now()}-${uuidv4()}`;
      const ext = extname(file.originalname);
      callback(null, `${file.fieldname}-${uniqueSuffix}${ext}`);
    },
  }),
};
